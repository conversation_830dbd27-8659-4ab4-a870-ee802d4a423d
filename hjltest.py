import pexpect
import sys
import time
import re
import threading
from queue import Queue


# 从文件读取SQL（忽略空行和注释行）
def read_sql_from_file(file_path):
    """读取SQL文件，返回SQL语句列表（过滤空行和--注释）"""
    sql_list = []
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            for line in f:
                # 处理行：去除首尾空格、过滤空行和注释行
                line = line.strip()
                if not line:
                    continue
                if line.startswith('--'):
                    continue
                sql_list.append(line)
        print(f"成功从 {file_path} 读取 {len(sql_list)} 条SQL语句")
        return sql_list
    except FileNotFoundError:
        print(f"错误：SQL文件 {file_path} 不存在")
        sys.exit(1)
    except Exception as e:
        print(f"读取SQL文件 {file_path} 失败：{e}")
        sys.exit(1)


class ClientExecutor:
    def __init__(self, client_path, client_id):
        self.client_path = client_path
        self.client_id = client_id
        self.child = None
        self.is_connected = True
        self.abort_encountered = False
        self.current_sql_idx = 0
        self.lock = threading.Lock()  # 用于线程安全
        self.finished = False  # 标记事务是否完成
        self.batch_size = 50  # 每50条SQL暂停一次，避免长时间执行

    def connect(self):
        print(f"启动客户端 {self.client_id}...")
        self.child = pexpect.spawn(self.client_path, encoding='utf-8')
        self.child.logfile = sys.stdout
        try:
            self.child.expect('Rucbase> ', timeout=10)  # 等待客户端提示符
            print(f"客户端 {self.client_id} 已连接")
        except Exception as e:
            print(f"客户端 {self.client_id} 连接失败: {e}")
            self.is_connected = False

    def execute_next_sql(self, sql_sequence):
        """执行下一条SQL，支持批量执行和暂停"""
        with self.lock:
            if self.finished or self.abort_encountered or self.current_sql_idx >= len(sql_sequence):
                if not self.finished:
                    self.finished = True
                    print(f"客户端 {self.client_id} 事务执行完成")
                return False

            sql = sql_sequence[self.current_sql_idx]
            if not self._execute_single_sql(sql):
                self.finished = True
                return False

            self.current_sql_idx += 1
            # 每执行50条SQL暂停一下
            if self.current_sql_idx % self.batch_size == 0:
                print(f"客户端 {self.client_id} 暂停执行，已执行 {self.current_sql_idx} 条SQL")
                time.sleep(1)

            if self.current_sql_idx >= len(sql_sequence):
                self.finished = True
                print(f"客户端 {self.client_id} 事务执行完成")
                return False
            return True

    def _execute_single_sql(self, sql):
        """执行单条SQL，处理abort检测"""
        if not self.is_connected or self.abort_encountered:
            return False

        print(f"\n客户端 {self.client_id} 执行SQL: {sql}")
        self.child.sendline(sql)

        try:
            self.child.expect('Rucbase> ', timeout=60)  # 增加超时时间
            output = self.child.before

            # 检测abort
            if re.search(r'\babort\b', output, re.IGNORECASE) or sql.strip().lower() == 'abort':
                print(f"客户端 {self.client_id} 检测到abort，停止执行后续SQL")
                self.abort_encountered = True
                threading.Thread(target=self._check_database_state).start()
                return False

            return True
        except pexpect.TIMEOUT:
            print(f"客户端 {self.client_id} 执行超时: {sql}")
            return False
        except Exception as e:
            print(f"客户端 {self.client_id} 执行异常: {e}")
            return False

    def _check_database_state(self):
        """在新线程中检查数据库状态"""
        print(f"\n===== 启动状态检查线程 =====")
        checker = ClientExecutor(self.client_path, f"{self.client_id}_CHECKER")
        checker.connect()
        if checker.is_connected:
            print("\n===== 当前数据库状态 =====")
            checker._execute_single_sql("select * from table1;")
            checker._execute_single_sql("select * from table2;")
        checker.close()

    def close(self):
        if self.child:
            self.child.close()
            print(f"客户端 {self.client_id} 已关闭")

    def is_done(self):
        """检查事务是否完成"""
        with self.lock:
            return self.finished or self.abort_encountered


def initialize_database(client_path, init_sql_file):
    """执行初始化SQL（从文件读取）"""
    print("\n===== 开始初始化数据库 =====")
    # 读取初始化SQL
    init_sql = read_sql_from_file(init_sql_file)
    initializer = ClientExecutor(client_path, "INIT")
    initializer.connect()
    if not initializer.is_connected:
        print("初始化失败：无法连接到数据库客户端")
        return False

    for sql in init_sql:
        if not initializer._execute_single_sql(sql):
            print("初始化失败")
            initializer.close()
            return False
    initializer.close()
    print("数据库初始化成功")
    return True


def run_random_order_test(client_path, init_sql_file, t1_sql_file, t2_sql_file, full_scan_sql_file):
    """随机交替执行两个事务（从文件读取SQL），完成后执行全表扫描和crash"""
    if not initialize_database(client_path, init_sql_file):
        return

    # 读取事务SQL和全表扫描SQL
    t1_sql = read_sql_from_file(t1_sql_file)
    t2_sql = read_sql_from_file(t2_sql_file)
    full_scan_sql = read_sql_from_file(full_scan_sql_file)

    # 创建两个客户端
    t1_client = ClientExecutor(client_path, "T1")
    t2_client = ClientExecutor(client_path, "T2")
    t1_client.connect()
    t2_client.connect()
    if not t1_client.is_connected or not t2_client.is_connected:
        print("测试取消：客户端连接失败")
        t1_client.close()
        t2_client.close()
        return

    print(f"\n===== 开始随机交替执行事务，T1将执行 {len(t1_sql)} 条SQL，T2将执行 {len(t2_sql)} 条SQL =====")

    # 创建共享队列用于线程间通信
    queue = Queue()

    # 创建并启动两个事务线程
    t1_thread = threading.Thread(target=transaction_worker, args=(t1_client, t1_sql, queue, "T1"))
    t2_thread = threading.Thread(target=transaction_worker, args=(t2_client, t2_sql, queue, "T2"))

    t1_thread.start()
    t2_thread.start()

    # 主线程负责随机选择事务执行
    active_clients = {"T1": True, "T2": True}
    while active_clients["T1"] or active_clients["T2"]:
        command = queue.get()  # 等待队列消息（实际是先放命令再取，这里逻辑调整为主动放命令）
        # 随机选择一个活跃的客户端发送执行命令
        available_clients = [client for client, active in active_clients.items() if active]
        if not available_clients:
            break

        chosen_client = random.choice(available_clients)
        queue.put(chosen_client)  # 放入选中的客户端名

        # 短暂睡眠，控制执行速度
        time.sleep(0.2)

        # 检查事务状态，更新活跃列表
        if chosen_client == "T1" and t1_client.is_done():
            active_clients["T1"] = False
        elif chosen_client == "T2" and t2_client.is_done():
            active_clients["T2"] = False

    # 通知线程停止
    queue.put("STOP")
    queue.put("STOP")

    # 等待线程完成
    t1_thread.join()
    t2_thread.join()

    # 执行全表扫描
    print("\n===== 两个事务都已完成，执行全表扫描 =====")
    scan_client = ClientExecutor(client_path, "SCAN")
    scan_client.connect()
    if scan_client.is_connected:
        for sql in full_scan_sql:
            scan_client._execute_single_sql(sql)
    scan_client.close()

    # 执行crash命令
    print("\n===== 全表扫描完成，执行crash命令 =====")
    crash_client = ClientExecutor(client_path, "CRASH")
    crash_client.connect()
    if crash_client.is_connected:
        crash_client._execute_single_sql("crash")
    crash_client.close()

    # 关闭客户端
    t1_client.close()
    t2_client.close()
    print("\n===== 测试完成 =====")


def transaction_worker(client, sql_sequence, queue, client_name):
    """事务工作线程，等待队列通知执行SQL"""
    while True:
        command = queue.get()
        if command == "STOP":
            queue.task_done()
            break
        elif command == client_name:
            # 执行下一条SQL
            has_more = client.execute_next_sql(sql_sequence)
            if not has_more:
                pass  # 事务完成
            queue.task_done()


if __name__ == "__main__":
    # 配置文件路径（可根据实际情况修改）
    CLIENT_PATH = "./rmdb_client/build/rmdb_client"
    INIT_SQL_FILE = "sql/init.sql"       # 初始化建表SQL文件
    T1_SQL_FILE = "sql/t1.sql"           # 第一个事务SQL文件
    T2_SQL_FILE = "sql/t2.sql"           # 第二个事务SQL文件
    FULL_SCAN_SQL_FILE = "full_scan.sql"  # 全表扫描SQL文件

    print("开始执行测试（随机交替执行T1和T2事务，SQL从文件读取）")
    run_random_order_test(CLIENT_PATH, INIT_SQL_FILE, T1_SQL_FILE, T2_SQL_FILE, FULL_SCAN_SQL_FILE)