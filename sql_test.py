import pexpect
import sys
import re
import os
import subprocess
import signal
import time


def batch_execute_sql(sql_file_path, client_path, db_name, server_path):
    """
    批量执行SQL并在每次SELECT后比较输出与标准答案的对应行数
    """
    # 构建正确的服务端启动命令
    server_cmd = f"{server_path} {db_name}"
    server_process = start_rmdb_server(server_cmd)
    if not server_process:
        print("错误：无法启动RMDB服务端")
        return

    try:
        # 等待服务端启动
        time.sleep(5)

        with open(sql_file_path, 'r') as f:
            sql_commands = [line.strip() for line in f if line.strip() and not line.strip().startswith('--')]
        if not sql_commands:
            print("错误：SQL脚本为空")
            return

        error_patterns = [re.compile(p, re.IGNORECASE) for p in
                          ['ERROR', 'ERR:', 'failed', 'exception', 'traceback']]

        child = pexpect.spawn(client_path, encoding='utf-8')
        child.logfile = sys.stdout

        try:
            child.expect('Rucbase> ', timeout=10)
            print("成功连接到数据库客户端")

            for idx, sql in enumerate(sql_commands, 1):
                print(f"\n===== 执行第 {idx} 条SQL =====")
                print(f"SQL: {sql}")

                child.sendline(sql)
                try:
                    child.expect('Rucbase> ', timeout=30)
                except pexpect.TIMEOUT:
                    print(f"[错误] 第 {idx} 条SQL执行超时: {sql}")
                    continue

                output = child.before
                if any(p.search(output) for p in error_patterns):
                    print(f"[错误] 第 {idx} 条SQL执行失败: {sql}")
                    print(f"错误输出: {output}")
                else:
                    print(f"[成功] 第 {idx} 条SQL执行完成")

                    if sql.strip().upper().startswith('SELECT'):
                        print(f"\n===== 开始比较输出（前{get_output_lines(db_name)}行）=====")
                        compare_output_files(db_name, f"standoutput/output.txt", idx)

        except pexpect.TIMEOUT:
            print("全局超时错误：客户端无响应")
        except pexpect.EOF:
            print("客户端意外退出")
        finally:
            child.close()
            print("\n===== SQL执行批次完成 =====")

    finally:
        # 关闭RMDB服务端
        stop_rmdb_server(server_process)


def start_rmdb_server(server_cmd):
    """启动RMDB服务端进程"""
    print(f"启动RMDB服务端: {server_cmd}")
    try:
        # 使用shell=True执行命令，确保参数正确传递
        server_process = subprocess.Popen(
            server_cmd,
            shell=True,
            stdout=subprocess.DEVNULL,
            stderr=subprocess.DEVNULL,
            preexec_fn=os.setsid  # 创建新的进程组
        )
        return server_process
    except Exception as e:
        print(f"启动服务端失败: {e}")
        return None


def stop_rmdb_server(server_process):
    """停止RMDB服务端进程"""
    if server_process:
        print("关闭RMDB服务端...")
        try:
            # 向进程组发送SIGTERM信号
            os.killpg(os.getpgid(server_process.pid), signal.SIGTERM)
            server_process.wait(timeout=5)
            print("RMDB服务端已关闭")
        except subprocess.TimeoutExpired:
            print("服务端关闭超时，强制终止")
            os.killpg(os.getpgid(server_process.pid), signal.SIGKILL)
        except Exception as e:
            print(f"关闭服务端时出错: {e}")


def get_output_lines(db_name):
    """获取当前output.txt的行数"""
    output_file = f"{db_name}/output.txt"
    if not os.path.exists(output_file):
        return 0
    with open(output_file, 'r') as f:
        return len(f.readlines())


def compare_output_files(db_name, standard_file, sql_idx):
    """比较output.txt当前行数与标准答案前N行"""
    output_file = f"{db_name}/output.txt"
    if not os.path.exists(output_file):
        print(f"错误：未找到输出文件 {output_file}")
        return
    if not os.path.exists(standard_file):
        print(f"错误：未找到标准答案文件 {standard_file}")
        return

    output_lines = open(output_file, 'r').readlines()
    output_count = len(output_lines)
    output_content = [line.strip() for line in output_lines]

    standard_lines = open(standard_file, 'r').readlines()
    standard_content = [line.strip() for line in standard_lines[:output_count]]
    standard_count = len(standard_content)

    print(f"比较输出文件({output_count}行) vs 标准答案前{standard_count}行 (SELECT语句#{sql_idx})")

    first_diff = None
    for i in range(min(output_count, standard_count)):
        if output_content[i] != standard_content[i]:
            first_diff = i + 1
            break

    if first_diff:
        print(f"[差异] 第{first_diff}行开始不同")
        show_context(output_content, standard_content, first_diff)
    elif output_count != standard_count:
        print(f"[差异] 行数不同 - 输出:{output_count}行 标准前N行:{standard_count}行")
    else:
        print("[成功] 输出与标准答案前N行完全一致")


def show_context(output, standard, diff_line):
    """显示差异上下文"""
    start = max(0, diff_line - 3)
    end = min(len(output), diff_line + 3)

    print("\n输出文件片段:")
    for i in range(start, end):
        prefix = ">> " if i == diff_line - 1 else "   "
        print(f"{prefix}{i + 1}: {output[i] if i < len(output) else '(空行)'}")

    print("\n标准答案片段:")
    for i in range(start, end):
        prefix = ">> " if i == diff_line - 1 else "   "
        print(f"{prefix}{i + 1}: {standard[i] if i < len(standard) else '(空行)'}")


if __name__ == "__main__":
    # SQL_FILE = "test/transaction/transaction_sql/abort_index_test.sql"
    SQL_FILE = "test.sql"
    CLIENT_EXEC = "./rmdb_client/build/rmdb_client"
    DB_NAME = "testdb5"
    SERVER_EXEC = "./build/bin/rmdb"

    if len(sys.argv) > 1:
        SQL_FILE = sys.argv[1]
    if len(sys.argv) > 2:
        CLIENT_EXEC = sys.argv[2]
    if len(sys.argv) > 3:
        DB_NAME = sys.argv[3]
    if len(sys.argv) > 4:
        SERVER_EXEC = sys.argv[4]

    batch_execute_sql(SQL_FILE, CLIENT_EXEC, DB_NAME, SERVER_EXEC)