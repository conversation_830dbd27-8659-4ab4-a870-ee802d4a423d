#!/usr/bin/env python3
"""
测试：基于静态检查点的故障恢复 - 使用聚合函数进行一致性校验
涵盖测试点: crash_recovery_single_thread_test, crash_recovery_multi_thread_test,
crash_recovery_index_test, crash_recovery_large_data_test, crash_recovery_without_checkpoint, crash_recovery_with_checkpoint
"""
import subprocess
import threading
import time
import os

class CheckpointRecoveryTester:
    def __init__(self):
        self.build_dir = "/home/<USER>/db2025-x2/db2025-x1-q9_zyy/db2025-x1-q9_zyy/build"
        self.client_dir = "/home/<USER>/db2025-x2/db2025-x1-q9_zyy/db2025-x1-q9_zyy/rmdb_client/build"
        self.server_process = None

    def cleanup(self):
        """终止服务器进程并清理残留"""
        if self.server_process:
            try:
                self.server_process.terminate()
                self.server_process.wait(timeout=5)
            except:
                try:
                    self.server_process.kill()
                except:
                    pass
        try:
            subprocess.run("pkill -9 -x rmdb", shell=True, timeout=3)
        except:
            pass
        try:
            subprocess.run("pkill -9 -x rmdb_client", shell=True, timeout=3)
        except:
            pass
        time.sleep(2)

    def start_server(self, db_name):
        """启动数据库服务器并等待准备就绪"""
        print(f"启动数据库服务器: {db_name}")
        self.cleanup()
        subprocess.run(f"rm -rf {self.build_dir}/{db_name}", shell=True)
        cmd = f"./bin/rmdb {db_name}"
        self.server_process = subprocess.Popen(
            cmd, shell=True, cwd=self.build_dir,
            stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True, bufsize=1
        )
        time.sleep(1)
        if self.server_process.poll() is not None:
            stdout, stderr = self.server_process.communicate(timeout=1)
            print(f"服务器启动失败: {stderr}")
            return False
        return True

    def run_client_commands(self, commands, timeout=30):
        """使用 rmdb_client 执行 SQL 命令序列"""
        cmd = "./rmdb_client"
        client_process = subprocess.Popen(
            cmd, shell=True, cwd=self.client_dir,
            stdin=subprocess.PIPE, stdout=subprocess.PIPE,
            stderr=subprocess.PIPE, text=True
        )
        input_text = "\n".join(commands) + "\nexit\n"
        try:
            stdout, stderr = client_process.communicate(input=input_text, timeout=timeout)
            return stdout, stderr
        except subprocess.TimeoutExpired:
            client_process.kill()
            return "", "TIMEOUT"

    def create_base_data(self):
        """创建基础表和初始化数据"""
        commands = [
            # 创建表结构（TPC-C 风格）
            "create table warehouse (w_id int, w_name char(10), w_street_1 char(20), w_street_2 char(20), w_city char(20), w_state char(2), w_zip char(9), w_tax float, w_ytd float);",
            "create table district (d_id int, d_w_id int, d_name char(10), d_street_1 char(20), d_street_2 char(20), d_city char(20), d_state char(2), d_zip char(9), d_tax float, d_ytd float, d_next_o_id int);",
            "create table customer (c_id int, c_d_id int, c_w_id int, c_first char(16), c_middle char(2), c_last char(16), c_street_1 char(20), c_street_2 char(20), c_city char(20), c_state char(2), c_zip char(9), c_phone char(16), c_since char(30), c_credit char(2), c_credit_lim int, c_discount float, c_balance float, c_ytd_payment float, c_payment_cnt int, c_delivery_cnt int, c_data char(50));",
            "create table orders (o_id int, o_d_id int, o_w_id int, o_c_id int, o_entry_d char(19), o_carrier_id int, o_ol_cnt int, o_all_local int);",
            "create table new_orders (no_o_id int, no_d_id int, no_w_id int);",
            "create table order_line (ol_o_id int, ol_d_id int, ol_w_id int, ol_number int, ol_i_id int, ol_supply_w_id int, ol_delivery_d char(30), ol_quantity int, ol_amount float, ol_dist_info char(24));",
            # 插入初始基础数据：1 个仓库，2 个区
            "insert into warehouse values (1, 'WH1', 'S1', 'S2', 'City', 'CA', '00001', 0.1, 10000.0);",
            "insert into district values (1, 1, 'D1', 'DStreet1', 'DStreet2', 'DCity', 'CA', '00001', 0.1, 1000.0, 5);",
            "insert into district values (2, 1, 'D2', 'DStreet1', 'DStreet2', 'DCity', 'CA', '00002', 0.1, 1000.0, 5);"
        ]
        stdout, stderr = self.run_client_commands(commands)
        print("创建表结构并插入基础数据")

    def consistency_check_aggregates(self):
        """使用聚合函数进行数据一致性验证:contentReference[oaicite:11]{index=11}:contentReference[oaicite:12]{index=12}"""
        print("执行一致性校验（使用聚合函数）")
        success = True
        details = []
        # 获取所有 (w_id, d_id)
        stdout, _ = self.run_client_commands(["select d_w_id, d_id from district;"])
        pairs = []
        for line in stdout.splitlines():
            if '|' in line:
                parts = [p.strip() for p in line.split('|') if p.strip()]
                if len(parts) >= 2:
                    w = int(parts[0]); d = int(parts[1])
                    pairs.append((w, d))
        for (w, d) in pairs:
            # 校验 1: d_next_o_id -1 == max(o_id) == max(no_o_id)
            out, _ = self.run_client_commands([f"select d_next_o_id from district where d_w_id = {w} and d_id = {d};"])
            try:
                d_next = int(out.splitlines()[1].split('|')[0])
            except:
                d_next = None
            out, _ = self.run_client_commands([f"select max(o_id) from orders where o_w_id = {w} and o_d_id = {d};"])
            max_o = int(out.splitlines()[1].split('|')[0]) if out and '|' in out.splitlines()[1] else 0
            out, _ = self.run_client_commands([f"select max(no_o_id) from new_orders where no_w_id = {w} and no_d_id = {d};"])
            max_no = int(out.splitlines()[1].split('|')[0]) if out and '|' in out.splitlines()[1] else 0
            if d_next is None or not (d_next - 1 == max_o == max_no):
                details.append(f"(W{w},D{d}) 校验1失败: d_next_o_id-1={d_next-1 if d_next else 'N/A'}, max(o_id)={max_o}, max(no_o_id)={max_no}")
                success = False
            # 校验 2: new_orders 连续性（count = max-min+1）
            out, _ = self.run_client_commands([f"select count(no_o_id) from new_orders where no_w_id = {w} and no_d_id = {d};"])
            cnt = int(out.splitlines()[1].split('|')[0]) if out else 0
            out, _ = self.run_client_commands([f"select max(no_o_id) from new_orders where no_w_id = {w} and no_d_id = {d};"])
            max_no = int(out.splitlines()[1].split('|')[0]) if out else 0
            out, _ = self.run_client_commands([f"select min(no_o_id) from new_orders where no_w_id = {w} and no_d_id = {d};"])
            min_no = int(out.splitlines()[1].split('|')[0]) if out else 0
            if max_no - min_no + 1 != cnt:
                details.append(f"(W{w},D{d}) 校验2失败: count={cnt}, max-min+1={max_no-min_no+1}")
                success = False
            # 校验 3: sum(o_ol_cnt) == count(order_line)
            out, _ = self.run_client_commands([f"select sum(o_ol_cnt) from orders where o_w_id = {w} and o_d_id = {d};"])
            sum_o = int(out.splitlines()[1].split('|')[0]) if out else 0
            out, _ = self.run_client_commands([f"select count(ol_o_id) from order_line where ol_w_id = {w} and ol_d_id = {d};"])
            cnt_ol = int(out.splitlines()[1].split('|')[0]) if out else 0
            if sum_o != cnt_ol:
                details.append(f"(W{w},D{d}) 校验3失败: sum(o_ol_cnt)={sum_o}, count(order_line)={cnt_ol}")
                success = False
        if success:
            print("✅ 一致性检查通过")
        else:
            print("❌ 一致性检查失败：")
            for msg in details:
                print("   " + msg)
        return success

    def test_single_thread_small(self):
        """单线程、小数据、无检查点测试"""
        print("\n=== 单线程小规模测试 ===")
        if not self.start_server("test_single"):
            return False
        self.create_base_data()
        # 执行单事务: 更新 district1, 插入订单5及对应的 order_line，未提交直接崩溃
        commands = [
            "begin;",
            "update district set d_next_o_id = 6 where d_id = 1 and d_w_id = 1;",
            "insert into orders values (5, 1, 1, 0, '2023-01-01 00:00:00', 1, 3, 1);",
            "insert into new_orders values (5, 1, 1);",
            # 插入对应的三条 order_line
            "insert into order_line values (5, 1, 1, 1, 0, 1, NULL, 1, 10.0, 'D');",
            "insert into order_line values (5, 1, 1, 2, 0, 1, NULL, 1, 10.0, 'D');",
            "insert into order_line values (5, 1, 1, 3, 0, 1, NULL, 1, 10.0, 'D');",
            "crash"
        ]
        print("执行事务 (单线程)，并模拟 crash")
        self.run_client_commands(commands, timeout=5)
        if self.server_process:
            self.server_process.wait(timeout=5)
        # 重启恢复
        print("重启数据库 (恢复)...")
        self.start_server("test_single")
        return self.consistency_check_aggregates()

    def test_multi_thread_small(self):
        """多线程、小数据、无检查点测试"""
        print("\n=== 多线程小规模测试 ===")
        if not self.start_server("test_multi"):
            return False
        self.create_base_data()
        # 并发事务：一个事务提交，一个事务不提交
        def tx1():
            cmds = [
                "begin;",
                "update district set d_next_o_id = 6 where d_id = 1 and d_w_id = 1;",
                "insert into orders values (5, 1, 1, 0, '2023-01-02 00:00:00', 1, 2, 1);",
                "insert into new_orders values (5, 1, 1);",
                # 对应两条 order_line
                "insert into order_line values (5, 1, 1, 1, 0, 1, NULL, 1, 20.0, 'D');",
                "insert into order_line values (5, 1, 1, 2, 0, 1, NULL, 1, 20.0, 'D');",
                "commit"
            ]
            self.run_client_commands(cmds)
        def tx2():
            cmds = [
                "begin;",
                "update district set d_next_o_id = 6 where d_id = 2 and d_w_id = 1;",
                "insert into orders values (5, 2, 1, 0, '2023-01-02 00:00:00', 1, 2, 1);",
                "insert into new_orders values (5, 2, 1);",
                "insert into order_line values (5, 2, 1, 1, 0, 1, NULL, 1, 20.0, 'D');",
                "insert into order_line values (5, 2, 1, 2, 0, 1, NULL, 1, 20.0, 'D');",
                "crash"
            ]
            self.run_client_commands(cmds)
        print("启动两个并发事务: tx1 提交, tx2 不提交")
        t1 = threading.Thread(target=tx1)
        t2 = threading.Thread(target=tx2)
        t1.start(); t2.start()
        t1.join(); t2.join()
        if self.server_process:
            self.server_process.wait(timeout=5)
        # 重启恢复
        print("重启数据库 (恢复)...")
        self.start_server("test_multi")
        return self.consistency_check_aggregates()

    def test_index_large(self):
        """单线程、大数据、创建索引、无检查点测试"""
        print("\n=== 单线程大数据测试 (含索引) ===")
        if not self.start_server("test_index"):
            return False
        self.create_base_data()
        print("创建索引...")
        self.run_client_commands(["create index idx_orders on orders (o_w_id, o_d_id);"])
        print("插入大量数据...")
        cmds = ["begin;"]
        for i in range(6, 20):
            cmds.append(f"insert into orders values ({i}, 1, 1, 0, '2023-01-03 00:00:00', 1, 1, 1);")
            cmds.append(f"insert into new_orders values ({i}, 1, 1);")
            cmds.append(f"insert into order_line values ({i}, 1, 1, 1, 0, 1, NULL, 1, 30.0, 'D');")
            cmds.append(f"update district set d_next_o_id = {i+1} where d_id = 1 and d_w_id = 1;")
        cmds.append("commit;")
        self.run_client_commands(cmds)
        print("数据插入完成，模拟 crash")
        self.run_client_commands(["crash"])
        if self.server_process:
            self.server_process.wait(timeout=5)
        print("重启数据库 (恢复)...")
        self.start_server("test_index")
        return self.consistency_check_aggregates()

    def test_large_data(self):
        """多线程、大数据、无检查点测试"""
        print("\n=== 多线程大数据测试 ===")
        if not self.start_server("test_large"):
            return False
        self.create_base_data()
        def tx_insert(start_id, d_id):
            cmds = ["begin;"]
            for i in range(start_id, start_id+10):
                cmds.append(f"insert into orders values ({i}, {d_id}, 1, 0, '2023-01-04 00:00:00', 1, 1, 1);")
                cmds.append(f"insert into new_orders values ({i}, {d_id}, 1);")
                cmds.append(f"insert into order_line values ({i}, {d_id}, 1, 1, 0, 1, NULL, 1, 40.0, 'D');")
                cmds.append(f"update district set d_next_o_id = {i+1} where d_id = {d_id} and d_w_id = 1;")
            cmds.append("commit;")
            self.run_client_commands(cmds)
        print("启动多个线程插入大量订单...")
        t1 = threading.Thread(target=tx_insert, args=(6,1))
        t2 = threading.Thread(target=tx_insert, args=(20,2))
        t1.start(); t2.start()
        t1.join(); t2.join()
        print("数据插入完成，模拟 crash")
        self.run_client_commands(["crash"])
        if self.server_process:
            self.server_process.wait(timeout=5)
        print("重启数据库 (恢复)...")
        self.start_server("test_large")
        return self.consistency_check_aggregates()

    def test_without_checkpoint(self):
        """单线程、超大数据、无检查点（记录恢复时间 t1）"""
        print("\n=== 单线程超大数据测试 (无检查点) ===")
        if not self.start_server("test_without_ckpt"):
            return False
        self.create_base_data()
        cmds = ["begin;"]
        for i in range(6, 100):
            cmds.append(f"insert into orders values ({i}, 1, 1, 0, '2023-01-05 00:00:00', 1, 1, 1);")
            cmds.append(f"insert into new_orders values ({i}, 1, 1);")
            cmds.append(f"insert into order_line values ({i}, 1, 1, 1, 0, 1, NULL, 1, 50.0, 'D');")
            cmds.append(f"update district set d_next_o_id = {i+1} where d_id = 1 and d_w_id = 1;")
        cmds.append("commit;")
        print("插入超大批量数据")
        self.run_client_commands(cmds, timeout=60)
        print("模拟 crash")
        self.run_client_commands(["crash"])
        if self.server_process:
            self.server_process.wait(timeout=5)
        print("重启数据库 (恢复)...")
        start_time = time.time()
        self.start_server("test_without_ckpt")
        t1 = time.time() - start_time
        print(f"恢复时间 t1 = {t1:.2f} 秒")
        ok = self.consistency_check_aggregates()
        return ok, t1

    def test_with_checkpoint(self, t1):
        """单线程、超大数据、有检查点（记录恢复时间 t2）"""
        print("\n=== 单线程超大数据测试 (有检查点) ===")
        if not self.start_server("test_with_ckpt"):
            return False
        self.create_base_data()
        cmds = ["begin;"]
        for i in range(6, 100):
            cmds.append(f"insert into orders values ({i}, 1, 1, 0, '2023-01-06 00:00:00', 1, 1, 1);")
            cmds.append(f"insert into new_orders values ({i}, 1, 1);")
            cmds.append(f"insert into order_line values ({i}, 1, 1, 1, 0, 1, NULL, 1, 60.0, 'D');")
            cmds.append(f"update district set d_next_o_id = {i+1} where d_id = 1 and d_w_id = 1;")
            if i % 20 == 0:
                cmds.append("create static_checkpoint;")
        cmds.append("commit;")
        print("插入数据并创建静态检查点")
        self.run_client_commands(cmds, timeout=60)
        print("模拟 crash")
        self.run_client_commands(["crash"])
        if self.server_process:
            self.server_process.wait(timeout=5)
        print("重启数据库 (恢复)...")
        start_time = time.time()
        self.start_server("test_with_ckpt")
        t2 = time.time() - start_time
        print(f"恢复时间 t2 = {t2:.2f} 秒")
        print(f"要求 t2 <= 0.7 * t1 = {0.7*t1:.2f} 秒")
        if t2 <= 0.7 * t1:
            print("✅ 恢复时间满足要求")
        else:
            print("❌ 恢复时间不满足要求")
        return self.consistency_check_aggregates()

    def run_all_tests(self):
        print("=== 开始故障恢复一致性测试 (聚合函数版) ===")
        results = []
        tests = [
            ("单线程小数据", self.test_single_thread_small),
            ("多线程小数据", self.test_multi_thread_small),
            ("单线程大数据索引", self.test_index_large),
            ("多线程大数据", self.test_large_data)
        ]
        for name, func in tests:
            print(f"\n开始测试: {name}")
            res = func()
            results.append((name, res))
        print("\n开始测试: 单线程超大数据 (无检查点)")
        res, t1 = self.test_without_checkpoint()
        results.append(("超大数据无检查点", res))
        print("\n开始测试: 单线程超大数据 (有检查点)")
        res2 = self.test_with_checkpoint(t1)
        results.append(("超大数据有检查点", res2))
        print("\n=== 测试结果 ===")
        for name, passed in results:
            stat = "通过" if passed else "失败"
            print(f"{name}: {stat}")

def main():
    tester = CheckpointRecoveryTester()
    try:
        tester.run_all_tests()
    except Exception as e:
        print(f"测试执行异常: {e}")
    finally:
        tester.cleanup()

if __name__ == "__main__":
    main()
