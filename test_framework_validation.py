#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试框架验证脚本
验证测试整合框架是否正常工作
"""

import os
import sys
import subprocess
import time

def log_message(message, level="INFO"):
    """记录日志消息"""
    print(f"[{level}] {message}")

def check_file_exists(filepath, description):
    """检查文件是否存在"""
    if os.path.exists(filepath):
        log_message(f"✓ {description}: {filepath}", "SUCCESS")
        return True
    else:
        log_message(f"✗ {description}: {filepath} 不存在", "ERROR")
        return False

def check_executable(filepath):
    """检查文件是否可执行"""
    if os.access(filepath, os.X_OK):
        log_message(f"✓ 可执行: {filepath}", "SUCCESS")
        return True
    else:
        log_message(f"✗ 不可执行: {filepath}", "ERROR")
        return False

def run_command_test(command, description, timeout=30):
    """运行命令测试"""
    log_message(f"测试: {description}")
    log_message(f"命令: {command}")
    
    try:
        result = subprocess.run(
            command,
            shell=True,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            timeout=timeout,
            text=True
        )
        
        if result.returncode == 0:
            log_message(f"✓ {description} 成功", "SUCCESS")
            return True
        else:
            log_message(f"✗ {description} 失败 (返回码: {result.returncode})", "ERROR")
            if result.stderr:
                log_message(f"错误输出: {result.stderr[:200]}...", "ERROR")
            return False
            
    except subprocess.TimeoutExpired:
        log_message(f"✗ {description} 超时", "ERROR")
        return False
    except Exception as e:
        log_message(f"✗ {description} 异常: {e}", "ERROR")
        return False

def validate_framework():
    """验证测试框架"""
    log_message("开始验证测试框架...")
    
    checks_passed = 0
    total_checks = 0
    
    # 1. 检查核心文件存在
    log_message("\n=== 检查核心文件 ===")
    files_to_check = [
        ("run_all_tests.sh", "主测试脚本"),
        ("comprehensive_test.py", "综合测试脚本"),
        ("aggregate_test_suite.py", "聚合测试脚本"),
        ("crash_recovery_test_suite.py", "故障恢复测试脚本"),
        ("测试整合说明.md", "说明文档")
    ]
    
    for filepath, description in files_to_check:
        total_checks += 1
        if check_file_exists(filepath, description):
            checks_passed += 1
    
    # 2. 检查可执行权限
    log_message("\n=== 检查可执行权限 ===")
    executables = [
        "run_all_tests.sh",
        "comprehensive_test.py", 
        "aggregate_test_suite.py",
        "crash_recovery_test_suite.py"
    ]
    
    for executable in executables:
        total_checks += 1
        if os.path.exists(executable) and check_executable(executable):
            checks_passed += 1
    
    # 3. 检查Python语法
    log_message("\n=== 检查Python语法 ===")
    python_scripts = [
        "comprehensive_test.py",
        "aggregate_test_suite.py", 
        "crash_recovery_test_suite.py"
    ]
    
    for script in python_scripts:
        if os.path.exists(script):
            total_checks += 1
            if run_command_test(f"python3 -m py_compile {script}", f"Python语法检查: {script}", 10):
                checks_passed += 1
    
    # 4. 检查帮助信息
    log_message("\n=== 检查帮助信息 ===")
    help_commands = [
        ("./run_all_tests.sh --help", "Shell脚本帮助"),
        ("python3 comprehensive_test.py --help", "Python脚本帮助")
    ]
    
    for command, description in help_commands:
        total_checks += 1
        if run_command_test(command, description, 15):
            checks_passed += 1
    
    # 5. 检查测试发现功能
    log_message("\n=== 检查测试发现功能 ===")
    total_checks += 1
    if run_command_test("python3 comprehensive_test.py --list-tests", "测试发现功能", 20):
        checks_passed += 1
    
    # 6. 检查依赖
    log_message("\n=== 检查Python依赖 ===")
    dependencies = ["pexpect", "subprocess", "os", "sys", "time", "signal", "glob", "re"]
    
    for dep in dependencies:
        total_checks += 1
        if run_command_test(f"python3 -c 'import {dep}'", f"导入 {dep}", 5):
            checks_passed += 1
    
    # 7. 检查目录结构
    log_message("\n=== 检查目录结构 ===")
    directories = [
        ("test", "测试目录"),
        ("src", "源码目录"),
        ("rmdb_client", "客户端目录")
    ]
    
    for directory, description in directories:
        total_checks += 1
        if check_file_exists(directory, description):
            checks_passed += 1
    
    # 8. 检查构建系统
    log_message("\n=== 检查构建系统 ===")
    build_files = [
        ("CMakeLists.txt", "根CMake文件"),
        ("rmdb_client/CMakeLists.txt", "客户端CMake文件")
    ]
    
    for filepath, description in build_files:
        total_checks += 1
        if check_file_exists(filepath, description):
            checks_passed += 1
    
    # 总结
    log_message(f"\n=== 验证结果 ===")
    log_message(f"通过检查: {checks_passed}/{total_checks}")
    
    if checks_passed == total_checks:
        log_message("✓ 测试框架验证完全通过!", "SUCCESS")
        return True
    else:
        failed = total_checks - checks_passed
        log_message(f"✗ 有 {failed} 项检查失败", "WARNING")
        
        if checks_passed / total_checks >= 0.8:
            log_message("框架基本可用，但建议修复失败项", "WARNING")
            return True
        else:
            log_message("框架存在严重问题，需要修复", "ERROR")
            return False

def show_usage_examples():
    """显示使用示例"""
    log_message("\n=== 使用示例 ===")
    
    examples = [
        "# 运行所有测试",
        "./run_all_tests.sh",
        "",
        "# 仅运行单元测试", 
        "./run_all_tests.sh --unit-only",
        "",
        "# 测试指定题目",
        "./run_all_tests.sh --topic 5",
        "",
        "# 运行聚合函数专项测试",
        "python3 aggregate_test_suite.py",
        "",
        "# 运行故障恢复专项测试", 
        "python3 crash_recovery_test_suite.py",
        "",
        "# 查看所有发现的测试",
        "python3 comprehensive_test.py --list-tests"
    ]
    
    for example in examples:
        print(example)

def main():
    """主函数"""
    log_message("db2025-x1 测试框架验证工具")
    log_message("=" * 50)
    
    # 验证框架
    if validate_framework():
        log_message("\n测试框架验证成功! 可以开始使用测试套件。", "SUCCESS")
        show_usage_examples()
        return 0
    else:
        log_message("\n测试框架验证失败! 请检查并修复问题。", "ERROR")
        return 1

if __name__ == "__main__":
    sys.exit(main())
