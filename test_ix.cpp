#include <iostream>
#include <vector>
#include <cstring>

#include"src/index/ix_index_handle.h"
#include "index/ix_manager.h"
#include "storage/buffer_pool_manager.h"
#include "storage/disk_manager.h"

int main() {
    std::string test_file_name = "test_ix_file.db";

    // 启动 DiskManager 和 BufferPoolManager
    DiskManager disk_manager;
    BufferPoolManager buffer_pool_manager(50, &disk_manager);
    IxManager ix_manager(&disk_manager, &buffer_pool_manager);

    // 删除旧文件（如果存在）
    if (disk_manager.is_file(test_file_name)) {
        disk_manager.destroy_file(test_file_name);
    }

    // 创建新的测试索引文件
    disk_manager.create_file(test_file_name);
    int fd = disk_manager.open_file(test_file_name);

    // 这里简单模拟手动创建 IxIndexHandle
    IxIndexHandle index_handle(&disk_manager, &buffer_pool_manager, fd);

    // 模拟 B+ 树的插入逻辑 (注意这里只是简单插入，实际使用你可以扩展)
    Transaction *txn = nullptr;  // 暂时不用事务

    for (int key = 1; key <= 5; key++) {
        Rid rid = {.page_no = 0, .slot_no = key};
        index_handle.insert_entry((char*)&key, rid, txn);
        std::cout << "Inserted key: " << key << std::endl;
    }

    // 查找验证
    for (int key = 1; key <= 5; key++) {
        std::vector<Rid> results;
        index_handle.get_value((char*)&key, &results, txn);
        if (!results.empty()) {
            std::cout << "Found key: " << key 
                      << ", rid: (" << results[0].page_no << "," << results[0].slot_no << ")" 
                      << std::endl;
        }
    }

    // 删除逻辑
    for (int key = 1; key <= 5; key++) {
        index_handle.delete_entry((char*)&key, txn);
        std::cout << "Deleted key: " << key << std::endl;
    }

    // 关闭文件
    disk_manager.close_file(fd);
    disk_manager.destroy_file(test_file_name);

    return 0;
}
