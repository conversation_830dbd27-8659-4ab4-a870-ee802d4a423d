#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
聚合函数与分组统计专项测试套件
专门针对题目5的测试需求
"""

import os
import sys
import time
import subprocess
import signal
import pexpect
import re
from datetime import datetime

# 配置
SERVER_EXEC = "./build/bin/rmdb"
CLIENT_EXEC = "./rmdb_client/build/rmdb_client"
DB_NAME = "agg_test_db"

# 聚合测试用例
AGGREGATE_TESTS = [
    {
        "name": "单独使用聚合函数",
        "sql_file": "test_single_aggregate.sql",
        "sql_commands": [
            "create table grade (course char(20),id int,score float);",
            "insert into grade values('DataStructure',1,95);",
            "insert into grade values('DataStructure',2,93.5);",
            "insert into grade values('DataStructure',4,87);",
            "insert into grade values('DataStructure',3,85);",
            "insert into grade values('DB',1,94);",
            "insert into grade values('DB',2,74.5);",
            "insert into grade values('DB',4,83);",
            "insert into grade values('DB',3,87);",
            "select MAX(id) as max_id from grade;",
            "select MIN(score) as min_score from grade where course = 'DB';",
            "select COUNT(course) as course_num from grade;",
            "select COUNT(*) as row_num from grade;",
            "select SUM(score) as sum_score from grade where id = 1;",
            "drop table grade;"
        ],
        "expected_output": [
            "| max_id |",
            "| 4 |",
            "| min_score |",
            "| 74.500000 |",
            "| course_num |",
            "| 8 |",
            "| row_num |",
            "| 8 |",
            "| sum_score |",
            "| 189.000000 |"
        ]
    },
    {
        "name": "聚合函数加分组统计",
        "sql_file": "test_group_aggregate.sql",
        "sql_commands": [
            "create table grade (course char(20),id int,score float);",
            "insert into grade values('DataStructure',1,95);",
            "insert into grade values('DataStructure',2,93.5);",
            "insert into grade values('DataStructure',3,94.5);",
            "insert into grade values('ComputerNetworks',1,99);",
            "insert into grade values('ComputerNetworks',2,88.5);",
            "insert into grade values('ComputerNetworks',3,92.5);",
            "insert into grade values('C++',1,92);",
            "insert into grade values('C++',2,89);",
            "insert into grade values('C++',3,89.5);",
            "select id,MAX(score) as max_score,MIN(score) as min_score,SUM(score) as sum_score from grade group by id;",
            "select id,MAX(score) as max_score from grade group by id having COUNT(*) > 3;",
            "insert into grade values ('ParallelCompute',1,100);",
            "select id,MAX(score) as max_score from grade group by id having COUNT(*) > 3;",
            "select id,MAX(score) as max_score,MIN(score) as min_score from grade group by id having COUNT(*) > 1 and MIN(score) > 88;",
            "select course ,COUNT(*) as row_num , COUNT(id) as student_num , MAX(score) as top_score, MIN(score) as lowest_score from grade group by course;",
            "drop table grade;"
        ]
    },
    {
        "name": "健壮性测试",
        "sql_file": "test_robustness.sql",
        "sql_commands": [
            "create table grade (course char(20),id int,score float);",
            "insert into grade values('DataStructure',1,95);",
            "insert into grade values('DataStructure',2,93.5);",
            "insert into grade values('DataStructure',3,94.5);",
            "insert into grade values('ComputerNetworks',1,99);",
            "insert into grade values('ComputerNetworks',2,88.5);",
            "insert into grade values('ComputerNetworks',3,92.5);",
            "select id , score from grade group by course;",
            "select id, MAX(score) as max_score from grade where MAX(score) > 90 group by id;"
        ],
        "expected_output": [
            "failure",
            "failure"
        ]
    },
    {
        "name": "ORDER BY语句测试",
        "sql_file": "test_order_by.sql",
        "sql_commands": [
            "create table records (vendor char(5), invoice_number int, amount float);",
            "insert into records values('alpha', 1001, 98.0);",
            "insert into records values('bravo', 2002, 76.5);",
            "insert into records values('charl', 3003, 99.0);",
            "insert into records values('delta', 1001, 98.5);",
            "insert into records values('echoo', 4004, 88.25);",
            "insert into records values('foxxx', 4004, 77.0);",
            "insert into records values('golfy', 5005, 97.75);",
            "insert into records values('hotel', 5005, 86.75);",
            "insert into records values('indio', 6006, 76.25);",
            "insert into records values('julie', 3003, 88.0);",
            "insert into records values('karen', 5005, 89.25);",
            "insert into records values('lenny', 2002, 91.125);",
            "insert into records values('mango', 6006, 98.5);",
            "insert into records values('nancy', 1001, 89.75);",
            "insert into records values('oscar', 2002, 90.0);",
            "insert into records values('peter', 3003, 95.0);",
            "insert into records values('quack', 6006, 88.625);",
            "insert into records values('romeo', 4004, 92.0);",
            "insert into records values('sunny', 1001, 95.25);",
            "insert into records values('tonny', 7007, 98.125);",
            "insert into records values('ultra', 4004, 91.5);",
            "insert into records values('vivid', 7007, 98.3125);",
            "select * from records order by invoice_number, amount asc limit 2;"
        ],
        "expected_output": [
            "| vendor | invoice_number | amount |",
            "| nancy | 1001 | 89.750000 |",
            "| sunny | 1001 | 95.250000 |"
        ]
    }
]

def log_message(message, level="INFO"):
    """记录日志消息"""
    timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    log_line = f"[{timestamp}] [{level}] {message}"
    print(log_line)

def start_rmdb_server(db_name):
    """启动RMDB服务器"""
    log_message(f"启动RMDB服务器: {SERVER_EXEC} {db_name}")
    
    # 确保输出目录存在
    os.makedirs(f"./build/{db_name}", exist_ok=True)
    
    try:
        server_process = subprocess.Popen(
            f"{SERVER_EXEC} {db_name}",
            shell=True,
            stdout=subprocess.DEVNULL,
            stderr=subprocess.DEVNULL,
            preexec_fn=os.setsid
        )
        time.sleep(2)
        return server_process
    except Exception as e:
        log_message(f"启动服务器失败: {e}", "ERROR")
        return None

def stop_rmdb_server(server_process):
    """停止RMDB服务器"""
    if server_process:
        log_message("关闭RMDB服务器...")
        try:
            os.killpg(os.getpgid(server_process.pid), signal.SIGTERM)
            server_process.wait(timeout=5)
            log_message("RMDB服务器已关闭")
        except subprocess.TimeoutExpired:
            log_message("服务器关闭超时，强制终止", "WARNING")
            os.killpg(os.getpgid(server_process.pid), signal.SIGKILL)
        except Exception as e:
            log_message(f"关闭服务器时出错: {e}", "ERROR")

def run_aggregate_test(test_case):
    """运行单个聚合测试用例"""
    log_message(f"\n===== 运行测试: {test_case['name']} =====")
    
    # 启动服务器
    server_process = start_rmdb_server(DB_NAME)
    if not server_process:
        return False
    
    try:
        # 启动客户端
        child = pexpect.spawn(CLIENT_EXEC, encoding='utf-8')
        child.logfile = None
        
        try:
            # 等待客户端连接
            child.expect('Rucbase> ', timeout=10)
            log_message("成功连接到数据库客户端")
            
            # 执行SQL命令
            for idx, sql in enumerate(test_case['sql_commands'], 1):
                log_message(f"执行SQL命令 {idx}/{len(test_case['sql_commands'])}: {sql}")
                child.sendline(sql)
                
                try:
                    child.expect('Rucbase> ', timeout=30)
                except pexpect.TIMEOUT:
                    log_message(f"SQL命令执行超时: {sql}", "ERROR")
                    continue
                
                # 检查输出是否有错误
                output = child.before
                if "ERROR" in output and "failure" not in output:
                    log_message(f"SQL命令执行失败: {sql}", "ERROR")
                    log_message(f"错误输出: {output}", "ERROR")
            
            # 检查输出
            output_file = f"./build/{DB_NAME}/output.txt"
            if os.path.exists(output_file):
                with open(output_file, 'r') as f:
                    actual_output = [line.strip() for line in f.readlines() if line.strip()]
                
                if 'expected_output' in test_case:
                    expected = test_case['expected_output']
                    if actual_output == expected:
                        log_message(f"测试 {test_case['name']} 通过", "SUCCESS")
                        return True
                    else:
                        log_message(f"测试 {test_case['name']} 失败 - 输出不匹配", "ERROR")
                        log_message(f"期望输出: {expected}")
                        log_message(f"实际输出: {actual_output}")
                        return False
                else:
                    log_message(f"测试 {test_case['name']} 完成 (无期望输出比较)", "SUCCESS")
                    return True
            else:
                log_message(f"输出文件不存在: {output_file}", "ERROR")
                return False
            
        except pexpect.TIMEOUT:
            log_message("客户端连接超时", "ERROR")
            return False
        except pexpect.EOF:
            log_message("客户端意外退出", "ERROR")
            return False
        finally:
            child.close()
    
    finally:
        # 停止服务器
        stop_rmdb_server(server_process)
        
        # 清理数据库文件
        if os.path.exists(f"./build/{DB_NAME}"):
            subprocess.run(f"rm -rf ./build/{DB_NAME}", shell=True)

def main():
    """主函数"""
    log_message("开始聚合函数与分组统计专项测试...")
    
    passed_tests = 0
    total_tests = len(AGGREGATE_TESTS)
    
    for test_case in AGGREGATE_TESTS:
        if run_aggregate_test(test_case):
            passed_tests += 1
    
    # 总结
    log_message(f"\n===== 聚合测试完成 =====")
    log_message(f"测试结果: {passed_tests}/{total_tests} 通过")
    
    if passed_tests == total_tests:
        log_message("所有聚合测试通过!", "SUCCESS")
        return 0
    else:
        log_message(f"有 {total_tests - passed_tests} 个测试失败", "WARNING")
        return 1

if __name__ == "__main__":
    sys.exit(main())
