#!/usr/bin/env python3
"""
完整的故障恢复测试 - 不依赖聚合函数
测试所有核心恢复功能：基础恢复、检查点恢复、TPC-C复杂场景、性能测试
"""

import subprocess
import time
import os
import signal

class RecoveryTester:
    def __init__(self):
        self.build_dir = "/home/<USER>/db2025-x1/build"
        self.client_dir = "/home/<USER>/db2025-x1/rmdb_client/build"
        self.server_process = None
        self.client_process = None
        
    def cleanup(self):
        """清理所有进程和资源"""
        # 终止服务器进程
        if self.server_process:
            try:
                self.server_process.terminate()
                self.server_process.wait(timeout=5)
            except:
                try:
                    self.server_process.kill()
                except:
                    pass
        
        # 终止客户端进程
        if self.client_process:
            try:
                self.client_process.terminate()
                self.client_process.wait(timeout=2)
            except:
                pass
        
        # 清理所有rmdb进程
        try:
            subprocess.run("pkill -f rmdb", shell=True, timeout=5)
            time.sleep(2)  # 等待进程完全清理
        except:
            pass
    
    def start_server(self, db_name):
        """启动服务器，包含错误处理"""
        print(f"启动服务器: {db_name}")
        
        # 确保进程清理
        self.cleanup()
        time.sleep(3)  # 等待端口释放
        
        cmd = f"./bin/rmdb {db_name}"
        self.server_process = subprocess.Popen(
            cmd, shell=True, cwd=self.build_dir,
            stdout=subprocess.PIPE, stderr=subprocess.PIPE,
            text=True, bufsize=1
        )
        
        # 等待服务器启动
        time.sleep(5)
        
        # 检查服务器是否正常启动
        if self.server_process.poll() is not None:
            stdout, stderr = self.server_process.communicate()
            print(f"服务器启动失败:")
            print(f"STDOUT: {stdout}")
            print(f"STDERR: {stderr}")
            return False
        
        print("服务器启动成功")
        return True
    
    def run_client_commands(self, commands, timeout=30):
        """运行客户端命令"""
        cmd = "./rmdb_client"
        self.client_process = subprocess.Popen(
            cmd, shell=True, cwd=self.client_dir,
            stdin=subprocess.PIPE, stdout=subprocess.PIPE, 
            stderr=subprocess.PIPE, text=True
        )
        
        input_text = "\n".join(commands) + "\nexit\n"
        try:
            stdout, stderr = self.client_process.communicate(input=input_text, timeout=timeout)
            return stdout, stderr
        except subprocess.TimeoutExpired:
            self.client_process.kill()
            return "", "TIMEOUT"
    
    def test_basic_recovery(self):
        """测试1: 基础故障恢复"""
        print("\n" + "="*60)
        print("测试1: 基础故障恢复 (单表、单事务)")
        print("="*60)
        
        # 清理并启动
        subprocess.run(f"rm -rf {self.build_dir}/test_basic", shell=True)
        if not self.start_server("test_basic"):
            return False
        
        try:
            # 阶段1: 创建表并插入已提交数据
            print("阶段1: 创建表并插入已提交数据")
            commands = [
                "create table users (id int, name char(20), age int);",
                "begin;",
                "insert into users values (1, 'Alice', 25);",
                "insert into users values (2, 'Bob', 30);",
                "commit;",
                "begin;",
                "insert into users values (3, 'Charlie', 35);",
                "insert into users values (4, 'David', 40);",
                "crash"  # 模拟崩溃，第二个事务未提交
            ]
            
            stdout, stderr = self.run_client_commands(commands)
            print("客户端输出:", stdout[:200] + "..." if len(stdout) > 200 else stdout)
            
            # 等待服务器崩溃
            if self.server_process:
                self.server_process.wait(timeout=10)
            
            # 阶段2: 重启并验证恢复
            print("阶段2: 重启服务器进行恢复")
            if not self.start_server("test_basic"):
                return False
            
            # 验证数据一致性
            verify_commands = [
                "select * from users;",
                "select * from users where id = 1;",
                "select * from users where id = 3;"  # 这条应该不存在
            ]
            
            stdout, stderr = self.run_client_commands(verify_commands)
            print("恢复后数据验证:")
            print(stdout)
            
            # 检查结果
            if ("Alice" in stdout and "Bob" in stdout and 
                "Charlie" not in stdout and "David" not in stdout):
                print("✅ 测试1通过: 已提交事务恢复，未提交事务回滚")
                return True
            else:
                print("❌ 测试1失败: 数据不一致")
                print(f"期望: 包含Alice和Bob，不包含Charlie和David")
                return False
                
        except Exception as e:
            print(f"❌ 测试1异常: {e}")
            return False
        finally:
            self.cleanup()
    
    def test_multi_table_recovery(self):
        """测试2: 多表复杂恢复"""
        print("\n" + "="*60)
        print("测试2: 多表复杂恢复 (多表、多事务)")
        print("="*60)
        
        subprocess.run(f"rm -rf {self.build_dir}/test_multi", shell=True)
        if not self.start_server("test_multi"):
            return False
        
        try:
            # 创建多个表并执行复杂操作
            print("阶段1: 创建多表并执行复杂事务")
            commands = [
                # 创建表
                "create table orders (order_id int, customer_id int, amount float);",
                "create table customers (customer_id int, name char(20), balance float);",
                "create table products (product_id int, name char(30), price float);",
                
                # 事务1: 已提交
                "begin;",
                "insert into customers values (1, 'John', 1000.0);",
                "insert into customers values (2, 'Jane', 2000.0);",
                "insert into products values (101, 'Laptop', 999.99);",
                "insert into products values (102, 'Mouse', 29.99);",
                "commit;",
                
                # 事务2: 已提交
                "begin;",
                "insert into orders values (1001, 1, 999.99);",
                "update customers set balance = 0.01 where customer_id = 1;",
                "commit;",
                
                # 事务3: 未提交（将被回滚）
                "begin;",
                "insert into orders values (1002, 2, 29.99);",
                "update customers set balance = 1970.01 where customer_id = 2;",
                "insert into products values (103, 'Keyboard', 79.99);",
                "crash"
            ]
            
            stdout, stderr = self.run_client_commands(commands)
            print("多表操作完成")
            
            if self.server_process:
                self.server_process.wait(timeout=10)
            
            # 重启并验证
            print("阶段2: 重启并验证多表数据一致性")
            if not self.start_server("test_multi"):
                return False
            
            verify_commands = [
                "select * from customers;",
                "select * from orders;", 
                "select * from products;",
                "select * from customers where customer_id = 1;",
                "select * from customers where customer_id = 2;"
            ]
            
            stdout, stderr = self.run_client_commands(verify_commands)
            print("多表恢复验证:")
            print(stdout)
            
            # 验证逻辑
            success = True
            reasons = []
            
            # 检查customers表
            if "John" not in stdout or "Jane" not in stdout:
                success = False
                reasons.append("客户数据丢失")
            
            # 检查John的余额应该是0.01（事务2已提交）
            if "0.01" not in stdout:
                success = False
                reasons.append("John余额更新丢失")
            
            # 检查Jane的余额应该还是2000.0（事务3未提交）
            if "2000" not in stdout:
                success = False
                reasons.append("Jane余额错误更新")
            
            # 检查orders表：应该只有1001订单
            if "1001" not in stdout:
                success = False
                reasons.append("已提交订单丢失")
            
            if "1002" in stdout:
                success = False
                reasons.append("未提交订单未回滚")
            
            # 检查products表：应该只有101和102
            if "Laptop" not in stdout or "Mouse" not in stdout:
                success = False
                reasons.append("产品数据丢失")
            
            if "Keyboard" in stdout:
                success = False
                reasons.append("未提交产品未回滚")
            
            if success:
                print("✅ 测试2通过: 多表复杂恢复成功")
                return True
            else:
                print(f"❌ 测试2失败: {', '.join(reasons)}")
                return False
                
        except Exception as e:
            print(f"❌ 测试2异常: {e}")
            return False
        finally:
            self.cleanup()

    def test_checkpoint_recovery(self):
        """测试3: 检查点恢复"""
        print("\n" + "="*60)
        print("测试3: 检查点恢复 (静态检查点功能)")
        print("="*60)

        subprocess.run(f"rm -rf {self.build_dir}/test_checkpoint", shell=True)
        if not self.start_server("test_checkpoint"):
            return False

        try:
            # 阶段1: 创建数据并建立检查点
            print("阶段1: 创建数据并建立检查点")
            commands = [
                "create table inventory (item_id int, name char(20), quantity int);",
                "begin;",
                "insert into inventory values (1, 'Item1', 100);",
                "insert into inventory values (2, 'Item2', 200);",
                "insert into inventory values (3, 'Item3', 300);",
                "commit;",
                "create static_checkpoint;"  # 创建检查点
            ]

            stdout, stderr = self.run_client_commands(commands)
            print("检查点创建完成")

            # 阶段2: 检查点后的操作
            print("阶段2: 检查点后继续操作")
            commands2 = [
                "begin;",
                "insert into inventory values (4, 'Item4', 400);",
                "update inventory set quantity = 150 where item_id = 1;",
                "commit;",
                "begin;",
                "insert into inventory values (5, 'Item5', 500);",
                "update inventory set quantity = 250 where item_id = 2;",
                "crash"  # 最后一个事务未提交
            ]

            stdout, stderr = self.run_client_commands(commands2)

            if self.server_process:
                self.server_process.wait(timeout=10)

            # 阶段3: 重启并验证检查点恢复
            print("阶段3: 重启并验证检查点恢复")
            recovery_start = time.time()
            if not self.start_server("test_checkpoint"):
                return False
            recovery_time = time.time() - recovery_start

            verify_commands = [
                "select * from inventory;",
                "select * from inventory where item_id = 1;",
                "select * from inventory where item_id = 2;",
                "select * from inventory where item_id = 4;",
                "select * from inventory where item_id = 5;"
            ]

            stdout, stderr = self.run_client_commands(verify_commands)
            print("检查点恢复验证:")
            print(stdout)

            # 验证逻辑
            success = True
            reasons = []

            # 检查点前的数据应该存在
            if "Item1" not in stdout or "Item2" not in stdout or "Item3" not in stdout:
                success = False
                reasons.append("检查点前数据丢失")

            # 检查点后已提交事务的数据应该存在
            if "Item4" not in stdout:
                success = False
                reasons.append("检查点后已提交数据丢失")

            # Item1的数量应该是150（已提交更新）
            if "150" not in stdout:
                success = False
                reasons.append("已提交更新丢失")

            # 检查点后未提交事务的数据不应该存在
            if "Item5" in stdout:
                success = False
                reasons.append("未提交数据未回滚")

            # Item2的数量应该还是200（未提交更新）
            lines = stdout.split('\n')
            item2_line = [line for line in lines if 'Item2' in line]
            if item2_line and "200" not in item2_line[0]:
                success = False
                reasons.append("未提交更新未回滚")

            if success:
                print(f"✅ 测试3通过: 检查点恢复成功 (恢复时间: {recovery_time:.2f}秒)")
                return True
            else:
                print(f"❌ 测试3失败: {', '.join(reasons)}")
                return False

        except Exception as e:
            print(f"❌ 测试3异常: {e}")
            return False
        finally:
            self.cleanup()

    def test_tpcc_style_recovery(self):
        """测试4: TPC-C风格复杂恢复"""
        print("\n" + "="*60)
        print("测试4: TPC-C风格复杂恢复 (模拟真实业务场景)")
        print("="*60)

        subprocess.run(f"rm -rf {self.build_dir}/test_tpcc", shell=True)
        if not self.start_server("test_tpcc"):
            return False

        try:
            # 创建TPC-C风格的表结构
            print("阶段1: 创建TPC-C风格表结构")
            commands = [
                "create table warehouse (w_id int, w_name char(10), w_tax float);",
                "create table district (d_id int, d_w_id int, d_name char(10), d_next_o_id int);",
                "create table customer (c_id int, c_d_id int, c_w_id int, c_first char(16));",
                "create table orders (o_id int, o_d_id int, o_w_id int, o_c_id int);",

                # 初始化基础数据（已提交）
                "begin;",
                "insert into warehouse values (1, 'WH1', 0.1);",
                "insert into district values (1, 1, 'DIST1', 1000);",
                "insert into customer values (1, 1, 1, 'John');",
                "insert into customer values (2, 1, 1, 'Jane');",
                "commit;"
            ]

            stdout, stderr = self.run_client_commands(commands)
            print("TPC-C表结构创建完成")

            # 创建检查点
            print("阶段2: 创建检查点")
            checkpoint_commands = ["create static_checkpoint;"]
            stdout, stderr = self.run_client_commands(checkpoint_commands)

            # 模拟复杂业务操作
            print("阶段3: 模拟复杂业务操作")
            business_commands = [
                # 事务1: 新订单事务（已提交）
                "begin;",
                "insert into orders values (1001, 1, 1, 1);",
                "update district set d_next_o_id = 1001 where d_id = 1;",
                "commit;",

                # 事务2: 付款事务（已提交）
                "begin;",
                "insert into orders values (1002, 1, 1, 2);",
                "update district set d_next_o_id = 1002 where d_id = 1;",
                "commit;",

                # 事务3: 订单状态查询事务（未提交，将被回滚）
                "begin;",
                "insert into orders values (1003, 1, 1, 1);",
                "update district set d_next_o_id = 1003 where d_id = 1;",
                "insert into customer values (3, 1, 1, 'Bob');",
                "crash"
            ]

            stdout, stderr = self.run_client_commands(business_commands)

            if self.server_process:
                self.server_process.wait(timeout=10)

            # 恢复并验证
            print("阶段4: 恢复并验证TPC-C数据一致性")
            if not self.start_server("test_tpcc"):
                return False

            verify_commands = [
                "select * from warehouse;",
                "select * from district;",
                "select * from customer;",
                "select * from orders;",
                "select * from district where d_id = 1;",
                "select * from orders where o_id = 1001;",
                "select * from orders where o_id = 1002;",
                "select * from orders where o_id = 1003;"
            ]

            stdout, stderr = self.run_client_commands(verify_commands)
            print("TPC-C恢复验证:")
            print(stdout)

            # 详细验证
            success = True
            reasons = []

            # 基础数据验证
            if "WH1" not in stdout:
                success = False
                reasons.append("仓库数据丢失")

            if "DIST1" not in stdout:
                success = False
                reasons.append("区域数据丢失")

            if "John" not in stdout or "Jane" not in stdout:
                success = False
                reasons.append("客户数据丢失")

            # 已提交事务验证
            if "1001" not in stdout or "1002" not in stdout:
                success = False
                reasons.append("已提交订单丢失")

            # district的d_next_o_id应该是1002（最后一个已提交事务）
            if "1002" not in stdout:
                success = False
                reasons.append("district更新丢失")

            # 未提交事务验证
            if "1003" in stdout:
                success = False
                reasons.append("未提交订单未回滚")

            if "Bob" in stdout:
                success = False
                reasons.append("未提交客户未回滚")

            # district的d_next_o_id不应该是1003
            lines = stdout.split('\n')
            district_lines = [line for line in lines if 'DIST1' in line]
            if district_lines and "1003" in district_lines[0]:
                success = False
                reasons.append("district未提交更新未回滚")

            if success:
                print("✅ 测试4通过: TPC-C风格复杂恢复成功")
                return True
            else:
                print(f"❌ 测试4失败: {', '.join(reasons)}")
                return False

        except Exception as e:
            print(f"❌ 测试4异常: {e}")
            return False
        finally:
            self.cleanup()

    def test_update_operations(self):
        """测试5: UPDATE操作的恢复"""
        print("\n" + "="*60)
        print("测试5: UPDATE操作的恢复 (测试新值和旧值)")
        print("="*60)

        subprocess.run(f"rm -rf {self.build_dir}/test_update", shell=True)
        if not self.start_server("test_update"):
            return False

        try:
            # 创建表并插入初始数据
            print("阶段1: 创建表并插入初始数据")
            commands = [
                "create table accounts (acc_id int, name char(20), balance float);",
                "begin;",
                "insert into accounts values (1, 'Alice', 1000.0);",
                "insert into accounts values (2, 'Bob', 2000.0);",
                "insert into accounts values (3, 'Charlie', 3000.0);",
                "commit;"
            ]

            stdout, stderr = self.run_client_commands(commands)
            print("初始数据创建完成")

            # 执行UPDATE操作
            print("阶段2: 执行UPDATE操作")
            update_commands = [
                # 已提交的UPDATE
                "begin;",
                "update accounts set balance = 1500.0 where acc_id = 1;",
                "update accounts set name = 'Robert' where acc_id = 2;",
                "commit;",

                # 未提交的UPDATE（将被回滚）
                "begin;",
                "update accounts set balance = 500.0 where acc_id = 1;",
                "update accounts set balance = 5000.0 where acc_id = 3;",
                "update accounts set name = 'Chuck' where acc_id = 3;",
                "crash"
            ]

            stdout, stderr = self.run_client_commands(update_commands)

            if self.server_process:
                self.server_process.wait(timeout=10)

            # 恢复并验证
            print("阶段3: 恢复并验证UPDATE操作")
            if not self.start_server("test_update"):
                return False

            verify_commands = [
                "select * from accounts;",
                "select * from accounts where acc_id = 1;",
                "select * from accounts where acc_id = 2;",
                "select * from accounts where acc_id = 3;"
            ]

            stdout, stderr = self.run_client_commands(verify_commands)
            print("UPDATE恢复验证:")
            print(stdout)

            # 验证逻辑
            success = True
            reasons = []

            # Alice的余额应该是1500.0（已提交更新）
            if "1500" not in stdout:
                success = False
                reasons.append("Alice已提交余额更新丢失")

            # Alice的余额不应该是500.0（未提交更新应该回滚）
            if "500" in stdout:
                success = False
                reasons.append("Alice未提交余额更新未回滚")

            # Bob的名字应该是Robert（已提交更新）
            if "Robert" not in stdout:
                success = False
                reasons.append("Bob已提交名字更新丢失")

            # Charlie的余额应该还是3000.0（未提交更新应该回滚）
            if "3000" not in stdout:
                success = False
                reasons.append("Charlie余额未提交更新未回滚")

            # Charlie的名字应该还是Charlie（未提交更新应该回滚）
            if "Chuck" in stdout:
                success = False
                reasons.append("Charlie名字未提交更新未回滚")

            if "Charlie" not in stdout:
                success = False
                reasons.append("Charlie原始名字丢失")

            if success:
                print("✅ 测试5通过: UPDATE操作恢复成功")
                return True
            else:
                print(f"❌ 测试5失败: {', '.join(reasons)}")
                return False

        except Exception as e:
            print(f"❌ 测试5异常: {e}")
            return False
        finally:
            self.cleanup()

    def run_all_tests(self):
        """运行所有恢复测试"""
        print("开始完整的故障恢复测试")
        print("="*80)

        tests = [
            ("基础故障恢复", self.test_basic_recovery),
            ("多表复杂恢复", self.test_multi_table_recovery),
            ("检查点恢复", self.test_checkpoint_recovery),
            ("TPC-C风格恢复", self.test_tpcc_style_recovery),
            ("UPDATE操作恢复", self.test_update_operations)
        ]

        results = []
        passed = 0
        total = len(tests)

        for test_name, test_func in tests:
            print(f"\n开始执行: {test_name}")
            try:
                result = test_func()
                results.append((test_name, result))
                if result:
                    passed += 1
            except Exception as e:
                print(f"测试 {test_name} 发生异常: {e}")
                results.append((test_name, False))

        # 输出总结
        print("\n" + "="*80)
        print("测试总结")
        print("="*80)

        for test_name, result in results:
            status = "✅ 通过" if result else "❌ 失败"
            print(f"{test_name}: {status}")

        print(f"\n总体结果: {passed}/{total} 测试通过")

        if passed == total:
            print("🎉 所有恢复测试通过！故障恢复功能完全正常！")
            return True
        else:
            print(f"⚠️  有 {total - passed} 个测试失败，需要进一步调试")
            return False

def main():
    """主函数"""
    print("RMDB 故障恢复完整测试")
    print("测试范围: 基础恢复、多表恢复、检查点恢复、TPC-C场景、UPDATE操作")
    print("不依赖聚合函数，纯粹测试恢复逻辑")

    tester = RecoveryTester()
    try:
        success = tester.run_all_tests()
        exit_code = 0 if success else 1
    except KeyboardInterrupt:
        print("\n测试被用户中断")
        exit_code = 2
    except Exception as e:
        print(f"\n测试过程中发生异常: {e}")
        exit_code = 3
    finally:
        tester.cleanup()

    exit(exit_code)

if __name__ == "__main__":
    main()
