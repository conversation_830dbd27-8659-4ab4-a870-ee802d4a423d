-- 题目3：唯一索引基础测试
-- 测试点1：创建、删除、展示索引

create table warehouse (id int, name char(8));
create index warehouse (id);
show index from warehouse;
drop index warehouse (id);
show index from warehouse;

-- 测试点2：索引查询
create table warehouse2 (w_id int, name char(8));
insert into warehouse2 values (10, 'qweruiop');
insert into warehouse2 values (534, 'asdfhjkl');
create index warehouse2 (w_id);
select * from warehouse2 where w_id = 10;

-- 清理
drop table warehouse;
drop table warehouse2;
