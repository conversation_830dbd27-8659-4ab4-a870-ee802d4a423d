-- 性能测试：验证检查点恢复比全日志恢复快70%以上

-- 1. 创建测试表（模拟题目中的表结构）
create table warehouse (w_id int, w_name char(10), w_street_1 char(20), w_street_2 char(20), w_city char(20), w_state char(2), w_zip char(9), w_tax float, w_ytd float);

create table district (d_id int, d_w_id int, d_name char(10), d_street_1 char(20), d_street_2 char(20), d_city char(20), d_state char(2), d_zip char(9), d_tax float, d_ytd float, d_next_o_id int);

create table customer (c_id int, c_d_id int, c_w_id int, c_first char(16), c_middle char(2), c_last char(16), c_street_1 char(20), c_street_2 char(20), c_city char(20), c_state char(2), c_zip char(9), c_phone char(16), c_since char(30), c_credit char(2), c_credit_lim int, c_discount float, c_balance float, c_ytd_payment float, c_payment_cnt int, c_delivery_cnt int, c_data char(50));

-- 2. 插入大量数据
begin;
insert into warehouse values (1, 'warehouse1', 'street1', 'street2', 'city1', 'CA', '12345', 0.1, 1000.0);
insert into warehouse values (2, 'warehouse2', 'street1', 'street2', 'city2', 'NY', '54321', 0.2, 2000.0);
insert into warehouse values (3, 'warehouse3', 'street1', 'street2', 'city3', 'TX', '67890', 0.3, 3000.0);
commit;

begin;
insert into district values (1, 1, 'district1', 'street1', 'street2', 'city1', 'CA', '12345', 0.1, 1000.0, 1);
insert into district values (2, 1, 'district2', 'street1', 'street2', 'city2', 'NY', '54321', 0.2, 2000.0, 2);
insert into district values (3, 2, 'district3', 'street1', 'street2', 'city3', 'TX', '67890', 0.3, 3000.0, 3);
commit;

begin;
insert into customer values (1, 1, 1, 'John', 'A', 'Doe', 'street1', 'street2', 'city1', 'CA', '12345', '1234567890', '2023-01-01 00:00:00', 'GC', 50000, 0.1, 1000.0, 500.0, 10, 5, 'customer data 1');
insert into customer values (2, 1, 1, 'Jane', 'B', 'Smith', 'street1', 'street2', 'city2', 'NY', '54321', '0987654321', '2023-01-02 00:00:00', 'BC', 60000, 0.2, 2000.0, 600.0, 20, 10, 'customer data 2');
commit;

-- 测试说明：
-- 1. 这个脚本创建了大量数据和事务
-- 2. 可以用来测试不带检查点的恢复时间（测试点5）
-- 3. 然后在中间创建检查点，测试带检查点的恢复时间（测试点6）
-- 4. 验证检查点恢复时间是否比全日志恢复快70%以上
