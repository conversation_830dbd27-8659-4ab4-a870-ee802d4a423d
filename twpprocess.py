import pexpect
import sys
import time
import re
import threading
import random
from queue import Queue

# 生成初始化SQL（创建表并插入测试数据）
def generate_init_sql():
    init_sql = [
        "drop table test_table;",
        "create table test_table (id int, value int, name char(20));",
        # 插入几条测试数据，供两个事务操作
        "insert into test_table values(1, 100, 'data1');",
        "insert into test_table values(2, 200, 'data2');",
        "insert into test_table values(3, 300, 'data3');",
        "insert into test_table values(4, 400, 'data4');"
    ]
    return init_sql

# 生成T1事务的SQL（操作共享数据）
def generate_t1_sql():
    return [
        "begin;",
        # 先锁定记录1
        "select * from test_table where id = 1 for update;",
        "update test_table set value = 10 where id = 1;",
        # 短暂等待，让T2有机会锁定记录2
        "select sleep(1);",
        # 尝试锁定记录2（T2已锁定）
        "select * from test_table where id = 2 for update;",
        "update test_table set value = 20 where id = 2;",
        "commit;"
    ]

# 生成T2事务的SQL（操作共享数据，顺序与T1相反）
def generate_t2_sql():
    return [
        "begin;",
        # 先锁定记录2（与T1顺序相反）
        "select * from test_table where id = 2 for update;",
        "update test_table set value = 1 where id = 2;",
        # 短暂等待，让T1有机会锁定记录1
        "select sleep(1);",
        # 尝试锁定记录1（T1已锁定）
        "select * from test_table where id = 1 for update;",
        "update test_table set value = 23 where id = 1;",
        "commit;"
    ]

# 全表扫描SQL（检查最终结果）
FULL_SCAN_SQL = [
    "select * from test_table;"
]

# crash命令
CRASH_SQL = "crash"


class ClientExecutor:
    def __init__(self, client_path, client_id):
        self.client_path = client_path
        self.client_id = client_id
        self.child = None
        self.is_connected = True
        self.abort_encountered = False
        self.deadlock_encountered = False  # 新增死锁检测标记
        self.current_sql_idx = 0
        self.lock = threading.Lock()
        self.finished = False
        self.results = []  # 存储执行结果

    def connect(self):
        print(f"启动客户端 {self.client_id}...")
        self.child = pexpect.spawn(self.client_path, encoding='utf-8')
        self.child.logfile = sys.stdout
        try:
            self.child.expect('Rucbase> ', timeout=10)
            print(f"客户端 {self.client_id} 已连接")
        except Exception as e:
            print(f"客户端 {self.client_id} 连接失败: {e}")
            self.is_connected = False

    def execute_next_sql(self, sql_sequence):
        with self.lock:
            if self.finished or self.abort_encountered or self.current_sql_idx >= len(sql_sequence):
                if not self.finished:
                    self.finished = True
                    print(f"客户端 {self.client_id} 事务执行完成")
                return False

            sql = sql_sequence[self.current_sql_idx]
            if not self._execute_single_sql(sql):
                self.finished = True
                return False

            self.current_sql_idx += 1

            if self.current_sql_idx >= len(sql_sequence):
                self.finished = True
                print(f"客户端 {self.client_id} 事务执行完成")
                return False
            return True

    def _execute_single_sql(self, sql):
        if not self.is_connected or self.abort_encountered:
            return False

        print(f"\n客户端 {self.client_id} 执行SQL: {sql}")
        self.child.sendline(sql)

        try:
            self.child.expect('Rucbase> ', timeout=60)
            output = self.child.before
            self.results.append((sql, output))

            # 检测死锁
            if re.search(r'deadlock', output, re.IGNORECASE):
                print(f"⚠️ 客户端 {self.client_id} 检测到死锁!")
                self.deadlock_encountered = True
                threading.Thread(target=self._check_database_state).start()
                return False

            # 检测abort
            if re.search(r'\babort\b', output, re.IGNORECASE) or sql.strip().lower() == 'abort':
                print(f"客户端 {self.client_id} 检测到abort")
                self.abort_encountered = True
                threading.Thread(target=self._check_database_state).start()
                return False

            return True
        except pexpect.TIMEOUT:
            print(f"客户端 {self.client_id} 执行超时: {sql}")
            return False
        except Exception as e:
            print(f"客户端 {self.client_id} 执行异常: {e}")
            return False

    def _check_database_state(self):
        print(f"\n===== 启动状态检查线程 =====")
        checker = ClientExecutor(self.client_path, f"{self.client_id}_CHECKER")
        checker.connect()
        if checker.is_connected:
            print("\n===== 当前数据库状态 =====")
            checker._execute_single_sql("select * from test_table;")
        checker.close()

    def close(self):
        if self.child:
            self.child.close()
            print(f"客户端 {self.client_id} 已关闭")

    def is_done(self):
        with self.lock:
            return self.finished or self.abort_encountered or self.deadlock_encountered

    def has_deadlock(self):
        with self.lock:
            return self.deadlock_encountered


def initialize_database(client_path):
    print("\n===== 开始初始化数据库 =====")
    initializer = ClientExecutor(client_path, "INIT")
    initializer.connect()
    if not initializer.is_connected:
        print("初始化失败：无法连接到数据库客户端")
        return False

    init_sql = generate_init_sql()
    for sql in init_sql:
        if not initializer._execute_single_sql(sql):
            print("初始化失败")
            initializer.close()
            return False
    
    # 显示初始化后的数据
    print("\n===== 初始化后的数据 =====")
    initializer._execute_single_sql("select * from test_table;")
    initializer.close()
    print("数据库初始化成功，已插入测试数据")
    return True


def run_deadlock_test(client_path):
    if not initialize_database(client_path):
        return

    # 生成事务SQL
    t1_sql = generate_t1_sql()
    t2_sql = generate_t2_sql()

    # 创建两个客户端
    t1_client = ClientExecutor(client_path, "T1")
    t2_client = ClientExecutor(client_path, "T2")
    t1_client.connect()
    t2_client.connect()
    if not t1_client.is_connected or not t2_client.is_connected:
        print("测试取消：客户端连接失败")
        t1_client.close()
        t2_client.close()
        return

    print(f"\n===== 开始死锁测试，两个事务将操作相同数据 =====")
    print(f"T1 SQL序列: {[sql.strip() for sql in t1_sql]}")
    print(f"T2 SQL序列: {[sql.strip() for sql in t2_sql]}")

    # 创建共享队列用于线程间通信
    queue = Queue()

    # 创建并启动两个事务线程
    t1_thread = threading.Thread(target=transaction_worker, args=(t1_client, t1_sql, queue, "T1"))
    t2_thread = threading.Thread(target=transaction_worker, args=(t2_client, t2_sql, queue, "T2"))

    t1_thread.start()
    t2_thread.start()

    # 主线程负责交替执行两个事务（增加冲突概率）
    active_clients = {"T1": True, "T2": True}
    step = 0
    max_steps = 20  # 最大步骤数，防止无限循环
    while (active_clients["T1"] or active_clients["T2"]) and step < max_steps:
        step += 1
        # 交替选择客户端，增加死锁概率
        chosen_client = "T1" if step % 2 == 1 else "T2"
        
        if active_clients.get(chosen_client, False):
            queue.put(chosen_client)
            time.sleep(0.5)  # 调整间隔，增加冲突机会
            
            # 检查事务状态
            if chosen_client == "T1" and t1_client.is_done():
                active_clients["T1"] = False
            elif chosen_client == "T2" and t2_client.is_done():
                active_clients["T2"] = False

    # 通知线程停止
    queue.put("STOP")
    queue.put("STOP")

    # 等待线程完成
    t1_thread.join()
    t2_thread.join()

    # 检查是否检测到死锁
    deadlock_detected = t1_client.has_deadlock() or t2_client.has_deadlock()
    if deadlock_detected:
        print("\n===== 测试结果：检测到死锁 =====")
    else:
        print("\n===== 测试结果：未检测到死锁 =====")

    # 执行全表扫描，检查最终数据状态
    print("\n===== 执行全表扫描，检查最终数据 =====")
    scan_client = ClientExecutor(client_path, "SCAN")
    scan_client.connect()
    if scan_client.is_connected:
        for sql in FULL_SCAN_SQL:
            scan_client._execute_single_sql(sql)
    scan_client.close()

    # 执行crash命令
    print("\n===== 执行crash命令 =====")
    crash_client = ClientExecutor(client_path, "CRASH")
    crash_client.connect()
    if crash_client.is_connected:
        crash_client._execute_single_sql(CRASH_SQL)
    crash_client.close()

    # 关闭客户端
    t1_client.close()
    t2_client.close()
    print("\n===== 测试完成 =====")


def transaction_worker(client, sql_sequence, queue, client_name):
    while True:
        command = queue.get()
        if command == "STOP":
            queue.task_done()
            break
        elif command == client_name:
            has_more = client.execute_next_sql(sql_sequence)
            if not has_more:
                pass  # 事务完成
            queue.task_done()


if __name__ == "__main__":
    CLIENT_PATH = "./rmdb_client/build/rmdb_client"
    print("开始执行死锁检测测试（两个事务操作相同数据）")
    run_deadlock_test(CLIENT_PATH)
