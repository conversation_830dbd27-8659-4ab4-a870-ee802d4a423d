#!/bin/bash

# 题目九测试脚本
# 用法: ./run_test_case_9.sh [test_number]
# test_number: 1-6 对应不同的测试点

echo "=== 题目九：基于静态检查点的故障恢复 测试脚本 ==="

# 检查参数
if [ $# -eq 0 ]; then
    echo "用法: $0 [test_number]"
    echo "测试点:"
    echo "  1 - crash_recovery_single_thread_test (基础故障恢复)"
    echo "  2 - crash_recovery_multi_thread_test (多线程故障恢复)"
    echo "  3 - crash_recovery_index_test (索引故障恢复)"
    echo "  4 - crash_recovery_large_data_test (大数据故障恢复)"
    echo "  5 - crash_recovery_without_checkpoint (无检查点性能测试)"
    echo "  6 - crash_recovery_with_checkpoint (有检查点性能测试)"
    exit 1
fi

TEST_NUM=$1
DB_NAME="test_case_${TEST_NUM}"

# 编译项目
echo "编译项目..."
cd build
make rmdb
if [ $? -ne 0 ]; then
    echo "编译失败!"
    exit 1
fi

# 清理旧的测试数据
echo "清理旧的测试数据..."
rm -rf ${DB_NAME}

case $TEST_NUM in
    1)
        echo "=== 测试点1: 基础故障恢复（单线程，小数据量，无检查点） ==="
        
        # 启动服务器
        echo "启动服务器..."
        ./bin/rmdb ${DB_NAME} &
        SERVER_PID=$!
        sleep 2
        
        # 创建测试SQL文件
        cat > test1.sql << EOF
create table t1 (id int, num int);
begin;
insert into t1 values(1, 1);
commit;
begin;
insert into t1 values(2, 2);
EOF
        
        echo "执行测试SQL..."
        echo "预期: 第一个事务提交，第二个事务未提交"
        
        # 模拟crash
        echo "模拟系统crash..."
        kill -9 $SERVER_PID
        sleep 1
        
        # 重启服务器
        echo "重启服务器进行故障恢复..."
        ./bin/rmdb ${DB_NAME} &
        SERVER_PID=$!
        sleep 2
        
        echo "验证恢复结果..."
        echo "预期结果: 只有(1,1)一条记录，(2,2)应该被回滚"
        
        # 清理
        kill $SERVER_PID 2>/dev/null
        ;;
        
    2)
        echo "=== 测试点2: 多线程故障恢复 ==="
        echo "注意: 这个测试需要多个客户端同时连接"
        
        ./bin/rmdb ${DB_NAME} &
        SERVER_PID=$!
        sleep 2
        
        echo "启动多个事务..."
        echo "预期: 多个并发事务的正确恢复"
        
        kill $SERVER_PID 2>/dev/null
        ;;
        
    3)
        echo "=== 测试点3: 索引故障恢复 ==="
        
        ./bin/rmdb ${DB_NAME} &
        SERVER_PID=$!
        sleep 2
        
        cat > test3.sql << EOF
create table test_index (id int, name char(20));
create index test_index (id);
begin;
insert into test_index values (1, 'test1');
insert into test_index values (2, 'test2');
commit;
begin;
insert into test_index values (3, 'test3');
EOF
        
        echo "测试包含索引的故障恢复..."
        echo "预期: 索引和数据都能正确恢复"
        
        kill -9 $SERVER_PID
        sleep 1
        
        ./bin/rmdb ${DB_NAME} &
        SERVER_PID=$!
        sleep 2
        
        kill $SERVER_PID 2>/dev/null
        ;;
        
    4)
        echo "=== 测试点4: 大数据量故障恢复 ==="
        
        ./bin/rmdb ${DB_NAME} &
        SERVER_PID=$!
        sleep 2
        
        echo "创建大量数据进行测试..."
        echo "预期: 大数据量下的正确恢复"
        
        kill $SERVER_PID 2>/dev/null
        ;;
        
    5)
        echo "=== 测试点5: 无检查点性能测试 ==="
        
        echo "开始性能测试（无检查点）..."
        start_time=$(date +%s%3N)
        
        ./bin/rmdb ${DB_NAME} &
        SERVER_PID=$!
        sleep 2
        
        # 创建大量数据
        echo "创建大量数据和事务..."
        
        # 模拟crash
        kill -9 $SERVER_PID
        sleep 1
        
        # 记录恢复开始时间
        recovery_start=$(date +%s%3N)
        
        # 重启并恢复
        ./bin/rmdb ${DB_NAME} &
        SERVER_PID=$!
        sleep 3
        
        recovery_end=$(date +%s%3N)
        recovery_time=$((recovery_end - recovery_start))
        
        echo "无检查点恢复时间: ${recovery_time} ms"
        echo "记录此时间作为基准t1"
        
        kill $SERVER_PID 2>/dev/null
        ;;
        
    6)
        echo "=== 测试点6: 有检查点性能测试 ==="
        
        ./bin/rmdb ${DB_NAME} &
        SERVER_PID=$!
        sleep 2
        
        echo "创建数据并建立检查点..."
        
        cat > test6.sql << EOF
create table warehouse (w_id int, w_name char(10), w_street_1 char(20), w_street_2 char(20), w_city char(20), w_state char(2), w_zip char(9), w_tax float, w_ytd float);
begin;
insert into warehouse values (1, 'warehouse1', 'street1', 'street2', 'city1', 'CA', '12345', 0.08, 300000.0);
commit;
create static_checkpoint;
begin;
insert into warehouse values (2, 'warehouse2', 'street1', 'street2', 'city2', 'NY', '54321', 0.09, 400000.0);
EOF
        
        # 模拟crash
        kill -9 $SERVER_PID
        sleep 1
        
        # 记录恢复开始时间
        recovery_start=$(date +%s%3N)
        
        # 重启并恢复
        ./bin/rmdb ${DB_NAME} &
        SERVER_PID=$!
        sleep 3
        
        recovery_end=$(date +%s%3N)
        recovery_time=$((recovery_end - recovery_start))
        
        echo "有检查点恢复时间: ${recovery_time} ms"
        echo "预期: 此时间应该 ≤ 测试点5时间的70%"
        
        kill $SERVER_PID 2>/dev/null
        ;;
        
    *)
        echo "无效的测试点编号: $TEST_NUM"
        echo "请选择1-6之间的数字"
        exit 1
        ;;
esac

echo "测试完成!"
echo ""
echo "验证要点:"
echo "1. 检查服务器启动时的恢复信息"
echo "2. 验证数据一致性（已提交事务恢复，未提交事务回滚）"
echo "3. 确认检查点功能正常（create static_checkpoint成功）"
echo "4. 性能对比（测试点6应该比测试点5快）"
