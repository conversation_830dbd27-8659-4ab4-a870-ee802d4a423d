-- 综合测试：使用题目提供的完整测试用例

-- 1. 创建所有测试表
create table warehouse (w_id int, w_name char(10), w_street_1 char(20), w_street_2 char(20), w_city char(20), w_state char(2), w_zip char(9), w_tax float, w_ytd float);

create table district (d_id int, d_w_id int, d_name char(10), d_street_1 char(20), d_street_2 char(20), d_city char(20), d_state char(2), d_zip char(9), d_tax float, d_ytd float, d_next_o_id int);

create table customer (c_id int, c_d_id int, c_w_id int, c_first char(16), c_middle char(2), c_last char(16), c_street_1 char(20), c_street_2 char(20), c_city char(20), c_state char(2), c_zip char(9), c_phone char(16), c_since char(30), c_credit char(2), c_credit_lim int, c_discount float, c_balance float, c_ytd_payment float, c_payment_cnt int, c_delivery_cnt int, c_data char(50));

create table history (h_c_id int, h_c_d_id int, h_c_w_id int, h_d_id int, h_w_id int, h_date char(19), h_amount float, h_data char(24));

create table new_orders (no_o_id int, no_d_id int, no_w_id int);

create table orders (o_id int, o_d_id int, o_w_id int, o_c_id int, o_entry_d char(19), o_carrier_id int, o_ol_cnt int, o_all_local int);

create table order_line (ol_o_id int, ol_d_id int, ol_w_id int, ol_number int, ol_i_id int, ol_supply_w_id int, ol_delivery_d char(30), ol_quantity int, ol_amount float, ol_dist_info char(24));

create table item (i_id int, i_im_id int, i_name char(24), i_price float, i_data char(50));

create table stock (s_i_id int, s_w_id int, s_quantity int, s_dist_01 char(24), s_dist_02 char(24), s_dist_03 char(24), s_dist_04 char(24), s_dist_05 char(24), s_dist_06 char(24), s_dist_07 char(24), s_dist_08 char(24), s_dist_09 char(24), s_dist_10 char(24), s_ytd float, s_order_cnt int, s_remote_cnt int, s_data char(50));

-- 2. 插入初始数据
begin;
insert into warehouse values (1, 'warehouse1', 'street1', 'street2', 'city1', 'CA', '12345', 0.08, 300000.0);
insert into district values (1, 1, 'district1', 'street1', 'street2', 'city1', 'CA', '12345', 0.1, 30000.0, 4);
insert into customer values (2, 1, 1, 'John', 'OE', 'Doe', 'street1', 'street2', 'city1', 'CA', '12345', '1234567890', '2023-06-03 19:25:47', 'GC', 50000, 0.1, 1000.0, 500.0, 10, 5, 'customer data');
insert into item values (10, 1, 'item name', 28.66, 'item data');
insert into stock values (10, 1, 15, 'dist01', 'dist02', 'dist03', 'dist04', 'dist05', 'dist06', 'dist07', 'dist08', 'dist09', 'dist10', 100.0, 5, 2, 'stock data');
commit;

-- 3. 创建静态检查点
create static_checkpoint;

-- 4. 执行题目中的复杂事务
begin;
select c_discount, c_last, c_credit, w_tax from customer, warehouse where w_id=1 and c_w_id=w_id and c_d_id=1 and c_id=2;
select d_next_o_id, d_tax from district where d_id=1 and d_w_id=1;
update district set d_next_o_id=5 where d_id=1 and d_w_id=1;
insert into orders values (4, 1, 1, 2, '2023-06-03 19:25:47', 26, 5, 1);
insert into new_orders values (4, 1, 1);
select i_price, i_name, i_data from item where i_id=10;
select s_quantity, s_data, s_dist_01, s_dist_02, s_dist_03, s_dist_04, s_dist_05, s_dist_06, s_dist_07, s_dist_08, s_dist_09, s_dist_10 from stock where s_i_id=10 and s_w_id=1;
update stock set s_quantity=7 where s_i_id=10 and s_w_id=1;
insert into order_line values (4, 1, 1, 1, 10, 1, '2023-06-03 19:25:47', 7, 286.625000, 'VF2uQHlDhtxa5dKhPwWyCqgY');
select i_price, i_name, i_data from item where i_id=10;
commit;

-- 5. 再次创建检查点
create static_checkpoint;

-- 6. 查看最终结果
select * from district;
select * from orders;
select * from new_orders;
select * from order_line;
select * from stock where s_i_id=10;
