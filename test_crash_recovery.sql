-- 题目九：基于静态检查点的故障恢复测试脚本

-- 1. 创建测试表
create table warehouse (w_id int, w_name char(10), w_street_1 char(20));
create table district (d_id int, d_w_id int, d_name char(10));

-- 2. 插入初始数据
begin;
insert into warehouse values (1, 'warehouse1', 'street1');
insert into district values (1, 1, 'district1');
commit;

-- 3. 创建静态检查点
create static_checkpoint;

-- 4. 插入更多数据（检查点后的数据）
begin;
insert into warehouse values (2, 'warehouse2', 'street2');
insert into district values (2, 2, 'district2');
commit;

-- 5. 查看数据
select * from warehouse;
select * from district;

-- 测试说明：
-- 1. 执行上述SQL后，系统会创建检查点
-- 2. 模拟crash后重启，系统应该能够：
--    - 读取重启文件中的检查点位置
--    - 从检查点开始进行故障恢复
--    - 恢复数据到一致性状态
-- 3. 检查点功能验证：
--    - create static_checkpoint 语法正常工作
--    - 检查点记录写入日志文件
--    - 重启文件正确更新
--    - 系统重启时能找到并使用检查点
