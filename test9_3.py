#!/usr/bin/env python3
"""
题目九 - 故障恢复复杂压力测试脚本
覆盖重点:
- 超大数据量
- 有/无索引
- 单线程 / 多线程并发写入
- 有/无静态检查点
- 恢复时间 & 数据一致性验证
"""

import subprocess
import threading
import time
import os
import random


class StressRecoveryTester:
    def __init__(self):
        self.build_dir = "/root/dbms6/db2025-x1/build"
        self.client_dir = "/root/dbms6/db2025-x1/rmdb_client/build"
        self.server_process = None
        self.log_dir = "stress_logs"
        os.makedirs(self.log_dir, exist_ok=True)

    def log_path(self, tag):
        return os.path.join(self.log_dir, f"{tag}.log")

    def cleanup(self):
        if self.server_process:
            try:
                self.server_process.terminate()
                self.server_process.wait(timeout=5)
            except:
                try:
                    self.server_process.kill()
                except:
                    pass
        try:
            subprocess.run("pkill -9 -x rmdb", shell=True, timeout=3)
            subprocess.run("pkill -9 -x rmdb_client", shell=True, timeout=3)
        except:
            pass
        time.sleep(1)

    def start_server(self, db_name, tag):
        self.cleanup()
        subprocess.run(f"rm -rf {self.build_dir}/{db_name}", shell=True)
        cmd = f"./bin/rmdb {db_name}"
        log = open(self.log_path(f"server_{tag}"), "w")
        self.server_process = subprocess.Popen(
            cmd, shell=True, cwd=self.build_dir,
            stdout=log, stderr=log, text=True, bufsize=1
        )
        time.sleep(2)
        return self.server_process.poll() is None

    def run_client(self, commands, tag, timeout=60):
        """执行客户端命令，打印发送的SQL和服务端响应"""
        cmd = "./rmdb_client"
        log_file = self.log_path(f"client_{tag}")

        # 打印客户端发送的SQL指令
        print(f"\n===== 客户端[{tag}] 发送的SQL指令 =====")
        for sql in commands:
            if sql.strip().lower() != "exit":  # 忽略exit命令的打印
                print(f"[发送] {sql}")

        with open(log_file, "w") as f:
            proc = subprocess.Popen(
                cmd, shell=True, cwd=self.client_dir,
                stdin=subprocess.PIPE, stdout=subprocess.PIPE,
                stderr=subprocess.STDOUT, text=True
            )
            try:
                full_input = "\n".join(commands) + "\nexit\n"
                out, _ = proc.communicate(input=full_input, timeout=timeout)
                f.write(out)

                # 打印服务端返回的响应
                print(f"\n===== 客户端[{tag}] 收到的服务端响应 =====")
                if out.strip():
                    for line in out.splitlines():
                        print(f"[响应] {line}")
                else:
                    print("[响应] 无返回内容")
                return out
            except subprocess.TimeoutExpired:
                proc.kill()
                err_msg = "TIMEOUT: 命令执行超时"
                f.write(err_msg)
                print(f"\n===== 客户端[{tag}] 执行结果 =====")
                print(f"[错误] {err_msg}")
                return err_msg

    def create_base_tables(self):
        return [
            "create table orders (id int, name char(50), amount float, status char(10));",
            "create table logs (lid int, oid int, text char(100));"
        ]

    def insert_large_dataset(self, thread_id, count=1000, with_checkpoint=False):
        cmds = []
        for i in range(count):
            oid = thread_id * 100000 + i
            name = f"'Item_{oid}'"
            status = random.choice(["'NEW'", "'OK'", "'PENDING'"])
            cmds.append("begin;")
            cmds.append(f"insert into orders values ({oid}, {name}, {random.uniform(1, 9999):.2f}, {status});")
            cmds.append(f"insert into logs values ({oid}, {oid}, 'Generated by thread {thread_id}');")
            cmds.append("commit;")
            if with_checkpoint and i > 0 and i % 200 == 0:
                cmds.append("create static_checkpoint;")
        # 多线程写入时通过thread_id区分客户端
        self.run_client(cmds, tag=f"thread_{thread_id}", timeout=120)

    def crash_server(self):
        # 崩溃命令的发送和响应打印
        self.run_client(["crash"], tag="crash")
        if self.server_process:
            self.server_process.wait(timeout=5)

    def verify_data_count(self, expected_min):
        # 验证数据时的SQL和响应打印
        result = self.run_client(["select count(*) from orders;"], tag="verify")
        if "ERROR" in result or "TIMEOUT" in result:
            print("❌ 数据校验失败")
            return False
        for line in result.splitlines():
            if "|" in line:
                try:
                    count = int(line.strip().split("|")[0])
                    print(f"恢复后订单记录数: {count}")
                    return count >= expected_min
                except:
                    pass
        return False

    def test_case(self, name, threads=1, rows_per_thread=1000, with_index=False, with_ckpt=False):
        print(f"\n\n=== [测试点: {name}] ===")
        if not self.start_server(name, tag=f"{name}_start"):
            print("❌ 启动失败")
            return False
        # 建表操作（带SQL和响应打印）
        self.run_client(self.create_base_tables(), tag=f"{name}_setup")
        # 建索引操作（带打印）
        if with_index:
            self.run_client(["create index idx_orders on orders (id);"], tag=f"{name}_index")
        # 启动多线程写入
        print(f"\n开始插入总计 {threads * rows_per_thread} 条记录...")
        worker_threads = []
        for tid in range(threads):
            t = threading.Thread(target=self.insert_large_dataset, args=(tid, rows_per_thread, with_ckpt))
            worker_threads.append(t)
            t.start()
        for t in worker_threads:
            t.join()
        # 模拟崩溃（带打印）
        print("\n开始模拟crash...")
        self.crash_server()
        # 重启恢复
        print("\n开始重启并恢复数据库...")
        start_time = time.time()
        self.start_server(name, tag=f"{name}_recovery")
        recovery_time = time.time() - start_time
        print(f"恢复时间: {recovery_time:.2f} 秒")
        # 验证数据（带打印）
        result = self.verify_data_count(expected_min=threads * rows_per_thread)
        print("✅ 数据恢复成功" if result else "❌ 数据恢复失败")
        return result

    def run_all_tests(self):
        cases = [
            ("stress_single_noindex", 1, 2, False, False),
            ("stress_multi_noindex", 4, 1, False, False),
            ("stress_single_index", 1, 2, True, False),
            ("stress_multi_index", 4, 1, True, False),
            ("stress_single_ckpt", 1, 2, False, True),
            ("stress_multi_ckpt", 4, 1, False, True)
        ]
        results = []
        for name, threads, rows, idx, ckpt in cases:
            passed = self.test_case(name, threads, rows, idx, ckpt)
            results.append((name, passed))
        print("\n\n=== [测试汇总] ===")
        for name, ok in results:
            print(f"{name}: {'✅ 通过' if ok else '❌ 失败'}")


if __name__ == "__main__":
    tester = StressRecoveryTester()
    try:
        tester.run_all_tests()
    finally:
        tester.cleanup()