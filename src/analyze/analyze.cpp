/* Copyright (c) 2023 Renmin University of China
RMDB is licensed under Mulan PSL v2.
You can use this software according to the terms and conditions of the Mulan PSL v2.
You may obtain a copy of Mulan PSL v2 at:
        http://license.coscl.org.cn/MulanPSL2
THIS SOFTWARE IS PROVIDED ON AN "AS IS" BASIS, WITHOUT WARRANTIES OF ANY KIND,
EITHER EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO NON-INFRINGEMENT,
MERCHANTABILITY OR FIT FOR A PARTICULAR PURPOSE.
See the Mulan PSL v2 for more details. */

#include "analyze.h"

/**
 * @description: 分析器，进行语义分析和查询重写，需要检查不符合语义规定的部分
 * @param {shared_ptr<ast::TreeNode>} parse parser生成的结果集
 * @return {shared_ptr<Query>} Query 
 */
std::shared_ptr<Query> Analyze::do_analyze(std::shared_ptr<ast::TreeNode> parse)
{
    std::shared_ptr<Query> query = std::make_shared<Query>();
    if (auto x = std::dynamic_pointer_cast<ast::SelectStmt>(parse))
    {
        /*1.处理表名和别名*/
        for(const auto& tab : x->tabs)
        {
            if(!tab.second.empty())
            {
                sm_manager_->add_alia(tab.first, tab.second);
            }
            query->tables.push_back(tab.first);
        }
        /** TODO: 检查表是否存在 */
        std::vector<std::string>::iterator it = query->tables.begin();
        while(it != query->tables.end())
        {
            if(!sm_manager_->db_.is_table((*it)))
            {
                throw TableNotFoundError((*it));
            }
            it++;
        }

        // 处理target list，再target list中添加上表名，例如 a.id
        for (auto &sv_sel_col : x->cols) {
            TabCol sel_col = {.tab_name = sv_sel_col->tab_name, .col_name = sv_sel_col->col_name};
            query->cols.push_back(sel_col);
        }
        
        std::vector<ColMeta> all_cols;
        get_all_cols(query->tables, all_cols);
        if (query->cols.empty()) {
            query->is_select_all_cols = true;
            // select all columns
            for (auto &col : all_cols) {
                TabCol sel_col = {.tab_name = col.tab_name, .col_name = col.name};
                query->cols.push_back(sel_col);
            }
        } else {
            // infer table name from column name
            for (auto &sel_col : query->cols) {
                sel_col = check_column(all_cols, sel_col);  // 列元数据校验
            }
        }

        /*处理join条件*/
        get_join(x->jointree, query->jointree);
        check_join_clause(query->jointree);

        //处理where条件
        get_clause(x->conds, query->conds);
        check_clause(query->tables, query->conds);

        // 处理LIMIT
        query->limit = x->limit;
    } else if (auto x = std::dynamic_pointer_cast<ast::UpdateStmt>(parse)) {
        /*1.处理表名*/
        query->tables.push_back(x->tab_name);
        
        /*2.检查表是否存在 */
        if(!sm_manager_->db_.is_table(x->tab_name))
        {
            throw TableNotFoundError(x->tab_name);
        }

        /*3.处理set_clauses，主要是转namespace*/
        std::vector<ColMeta> all_cols;
        get_all_cols(query->tables, all_cols);
        for(const auto& s_c : x->set_clauses)
        {
            TabCol lhs = check_column(all_cols,TabCol {"",s_c->col_name});
            SetValue rhs = convert_stvalue(s_c->valexpr);
            query->set_clauses.push_back(SetClause{lhs,rhs});
        }

        /*4.处理where条件*/
        get_clause(x->conds, query->conds);
        check_clause({x->tab_name}, query->conds);
    } else if (auto x = std::dynamic_pointer_cast<ast::DeleteStmt>(parse)) {
        //处理where条件
        get_clause(x->conds, query->conds);
        check_clause({x->tab_name}, query->conds);
    } else if (auto x = std::dynamic_pointer_cast<ast::InsertStmt>(parse)) {
        // 处理insert 的values值
        for (auto &sv_val : x->vals) {
            query->values.push_back(convert_sv_value(sv_val));
        }
    } else if (auto x = std::dynamic_pointer_cast<ast::ExplainStmt>(parse))
    {
        /*1.处理explain语句*/
        /*1.1对实际的语句进行词法分析*/
        query->explain_query = do_analyze(x->getOriginalStmt());
    } else if (auto x = std::dynamic_pointer_cast<ast::SemijoinStmt>(parse))
    {
        /*1.处理表名和别名*/
        for(const auto& tab : x->tabs)
        {
            if(!tab.second.empty())
            {
                sm_manager_->add_alia(tab.first, tab.second);
            }
            query->tables.push_back(tab.first);
        }
        /** TODO: 检查表是否存在 */
        std::vector<std::string>::iterator it = query->tables.begin();
        while(it != query->tables.end())
        {
            if(!sm_manager_->db_.is_table((*it)))
            {
                throw TableNotFoundError((*it));
            }
            it++;
        }

        // 处理target list，再target list中添加上表名，例如 a.id
        for (auto &sv_sel_col : x->cols) {
            TabCol sel_col = {.tab_name = sv_sel_col->tab_name, .col_name = sv_sel_col->col_name};
            query->cols.push_back(sel_col);
        }
        
        std::vector<ColMeta> all_cols;
        std::vector<std::string> left_table;
        left_table.push_back(x->jointree[0]->left);
        get_all_cols(left_table, all_cols);
        if (query->cols.empty()) {
            query->is_select_all_cols = true;
            // 选择左表的全部列
            for (auto &col : all_cols) {
                TabCol sel_col = {.tab_name = col.tab_name, .col_name = col.name};
                query->cols.push_back(sel_col);
            }
        } else {
            // infer table name from column name
            for (auto &sel_col : query->cols) {
                sel_col = semi_join_check_column(all_cols, sel_col, x->jointree[0]->left);  // 列元数据校验
            }
        }
        
        query->is_semi_join = true;
        /*处理join条件*/
        get_join(x->jointree, query->jointree);
        check_join_clause(query->jointree);

        //处理where条件
        get_clause(x->conds, query->conds);
        check_clause(query->tables, query->conds);
    } else if(auto x = std::dynamic_pointer_cast<ast::GroupByStmt>(parse))
    {
        //该部分主要把groupbystmt的变量转化到query，并作健壮性检查就是不合法的sql要在这里报错
        
        /*1.处理表名*/
        query->tables = std::move(x->tabs);

        /*2检验表是否合法*/
        std::vector<std::string>::iterator it = query->tables.begin();
        while(it != query->tables.end())
        {
            if(!sm_manager_->db_.is_table(*it))
            {
                throw TableNotFoundError(*it);
            }
            it++;
        }

        /*3.处理target list，再target list中添加上表名，例如 a.id*/
        // 对于聚合查询，我们需要特殊处理SELECT列表
        // SELECT列表中可能包含：1) 分组列 2) 聚合函数的别名
        // 我们暂时收集所有SELECT列，后续会根据聚合函数信息进行调整
        for (auto &sv_sel_col : x->select_cols) {
            TabCol sel_col = {.tab_name = sv_sel_col->tab_name, .col_name = sv_sel_col->col_name};
            query->cols.push_back(sel_col);
        }

        // 同时添加聚合函数的别名到SELECT列表中
        for (auto &aggexpr : x->agg_exprs) {
            if (!aggexpr->alias.empty()) {
                TabCol agg_col = {.tab_name = "", .col_name = aggexpr->alias};
                query->cols.push_back(agg_col);
            }
        }
        std::vector<ColMeta> all_cols;
        get_all_cols(query->tables, all_cols);

        /*4.处理groupby的列*/
        for (auto &sv_group_col : x->group_cols) {
            TabCol gr_col = {.tab_name = sv_group_col->tab_name, .col_name = sv_group_col->col_name};
            gr_col = check_column(all_cols, gr_col);  // 验证分组列并推断表名
            query->group_cols.push_back(gr_col);
        }

        /*5.处理聚合*/
        for(auto & aggexpr : x->agg_exprs)
        {
            // 验证聚合表达式中的列是否存在
            if (aggexpr->col != nullptr) {
                TabCol agg_col = {aggexpr->col->tab_name, aggexpr->col->col_name};
                agg_col = check_column(all_cols, agg_col);
                // 更新聚合表达式中的列信息
                aggexpr->col->tab_name = agg_col.tab_name;
                aggexpr->col->col_name = agg_col.col_name;
            }
            query->agg_exprs.push_back(*aggexpr);
        }

        // 保存原始的SELECT列表，用于后续重新构建
        std::vector<TabCol> original_cols = query->cols;

        /*6.处理having条件
        就是BinaryAggexpr转换成having_condition 目前认为左边只可能是聚合或者列，右边是值或者列*/
        for (auto &sv_having_cond : x->having_conds) {
            Condition cond;

            // 转换操作符（使用现有函数）
            cond.op = convert_sv_comp_op(sv_having_cond->op);

            // 处理左操作数
            if (auto agg_expr = std::dynamic_pointer_cast<ast::AggExpr>(sv_having_cond->lhs)) {
                // 左操作数是聚合表达式
                cond.is_lhs_agg = true;
                cond.lhs_agg = *agg_expr;

                // 验证聚合表达式中的列是否存在（如果不是COUNT(*)）
                if (agg_expr->col != nullptr) {
                    TabCol agg_col = {agg_expr->col->tab_name, agg_expr->col->col_name};
                    agg_col = check_column(all_cols, agg_col);
                    // 更新聚合表达式中的列信息
                    cond.lhs_agg.col->tab_name = agg_col.tab_name;
                    cond.lhs_agg.col->col_name = agg_col.col_name;
                }
            } else if (auto col_expr = std::dynamic_pointer_cast<ast::Col>(sv_having_cond->lhs)) {
                // 左操作数是列引用（使用现有函数校验列）
                cond.is_lhs_agg = false;
                cond.lhs_col = {col_expr->tab_name, col_expr->col_name};
                cond.lhs_col = check_column(all_cols, cond.lhs_col);
            } else {
                throw RMDBError("Unsupported left operand type in HAVING condition");
            }

            // 处理右操作数
            if (auto val_expr = std::dynamic_pointer_cast<ast::Value>(sv_having_cond->rhs)) {
                // 右操作数是值（使用现有函数转换值）
                cond.is_rhs_val = true;
                cond.rhs_val = convert_sv_value(val_expr);
            } else if (auto col_expr = std::dynamic_pointer_cast<ast::Col>(sv_having_cond->rhs)) {
                // 右操作数是列引用（使用现有函数校验列）
                cond.is_rhs_val = false;
                cond.rhs_col = {col_expr->tab_name, col_expr->col_name};
                cond.rhs_col = check_column(all_cols, cond.rhs_col);
            } else {
                throw RMDBError("Unsupported right operand type in HAVING condition");
            }

            query->having_conds.push_back(cond);
        }

        /*6.1 确保HAVING条件中的聚合函数也被计算*/
        // 收集HAVING条件中的聚合函数，如果它们不在SELECT列表中，则添加到agg_exprs中
        for (const auto& having_cond : query->having_conds) {
            if (having_cond.is_lhs_agg) {
                // 检查这个聚合函数是否已经在agg_exprs中
                bool found = false;
                for (const auto& existing_agg : query->agg_exprs) {
                    if (existing_agg.type == having_cond.lhs_agg.type) {
                        // 检查列是否匹配
                        bool col_match = false;
                        if (existing_agg.col == nullptr && having_cond.lhs_agg.col == nullptr) {
                            col_match = true;  // 都是COUNT(*)
                        } else if (existing_agg.col != nullptr && having_cond.lhs_agg.col != nullptr) {
                            col_match = (existing_agg.col->col_name == having_cond.lhs_agg.col->col_name);
                        }

                        if (col_match) {
                            found = true;
                            break;
                        }
                    }
                }

                // 如果没有找到，添加到agg_exprs中
                if (!found) {
                    query->agg_exprs.push_back(having_cond.lhs_agg);
                }
            }
        }

        // 健壮性检验
        validate_aggregation_query(x, query, all_cols);

        // 验证和处理SELECT列
        if (query->cols.empty()) {
            // select all columns
            for (auto &col : all_cols) {
                TabCol sel_col = {.tab_name = col.tab_name, .col_name = col.name};
                query->cols.push_back(sel_col);
            }
        } else {
            // infer table name from column name
            for (auto &sel_col : query->cols) {
                bool is_agg_alias = false;
                for(auto & aggexpr : x->agg_exprs)
                {
                    if(aggexpr->alias == sel_col.col_name)
                    {
                        is_agg_alias = true;
                        break;
                    }
                }
                if(!is_agg_alias)
                {
                    sel_col = check_column(all_cols, sel_col);  // 列元数据校验

                    // 如果有GROUP BY子句，检查非聚合列是否在GROUP BY中
                    if (!query->group_cols.empty()) {
                        std::cout << "DEBUG: Checking non-aggregate column: " << sel_col.tab_name << "." << sel_col.col_name << std::endl;
                        std::cout << "DEBUG: GROUP BY columns:" << std::endl;
                        for (const auto& group_col : query->group_cols) {
                            std::cout << "  " << group_col.tab_name << "." << group_col.col_name << std::endl;
                        }

                        bool found_in_group_by = false;
                        for (const auto& group_col : query->group_cols) {
                            if (sel_col.tab_name == group_col.tab_name && sel_col.col_name == group_col.col_name) {
                                found_in_group_by = true;
                                break;
                            }
                        }
                        std::cout << "DEBUG: found_in_group_by = " << found_in_group_by << std::endl;
                        if (!found_in_group_by) {
                            std::cout << "DEBUG: Throwing error for non-aggregate column" << std::endl;
                            throw RMDBError("SELECT list contains non-aggregate column '" + sel_col.col_name + "' that is not in GROUP BY clause");
                        }
                    }
                }
            }

            /*7. 对于聚合查询，重新构建query->cols以确保包含正确的输出列*/
            if (!query->agg_exprs.empty()) {
                // 这是一个聚合查询，重新构建输出列列表
                std::vector<TabCol> new_cols;



                // 首先添加所有分组列
                for (const auto& group_col : query->group_cols) {
                    new_cols.push_back(group_col);
                }

                // 然后添加原始SELECT列表中的聚合函数列
                for (const auto& sel_col : original_cols) {
                    // 检查这个列是否是聚合函数（通过别名或者函数名匹配）
                    bool is_agg_col = false;
                    for (const auto& agg_expr : x->agg_exprs) {
                        // 检查别名匹配
                        if (!agg_expr->alias.empty() && agg_expr->alias == sel_col.col_name) {
                            is_agg_col = true;
                            break;
                        }
                        // 检查函数名匹配（如果没有别名）
                        if (agg_expr->alias.empty()) {
                            std::string expected_name = agg_expr->col ? agg_expr->col->col_name : "COUNT(*)";
                            if (expected_name == sel_col.col_name) {
                                is_agg_col = true;
                                break;
                            }
                        }
                    }

                    // 如果是聚合函数列，添加到输出列
                    if (is_agg_col) {
                        new_cols.push_back(sel_col);
                    }
                }


                // 替换原来的cols
                query->cols = std::move(new_cols);
            }
        }

        //处理where条件
        get_clause(x->conds, query->conds);
        check_clause(query->tables, query->conds);

        // 处理LIMIT
        query->limit = x->limit;
    } else {
        // do nothing
    }
    /*insert和delete不处理表名是因为parse这里有*/
    query->parse = std::move(parse);
    return query;
}


TabCol Analyze::check_column(const std::vector<ColMeta> &all_cols, TabCol target) {
    if (target.tab_name.empty()) {
        // Table name not specified, infer table name from column name
        std::string tab_name;
        for (auto &col : all_cols) {
            if (col.name == target.col_name) {
                if (!tab_name.empty()) {
                    throw AmbiguousColumnError(target.col_name);
                }
                tab_name = col.tab_name;
            }
        }
        if (tab_name.empty()) {
            throw ColumnNotFoundError(target.col_name);
        }
        target.tab_name = tab_name;
    } else {
        /** TODO: Make sure target column exists */
        /*先将别名处理为真的表名*/
        std::string tab_name;
        if(!sm_manager_->db_.is_table(target.tab_name))
        {
            /*该表未找到*/
            if(!sm_manager_->get_real_table_name(&tab_name,target.tab_name))
            {
                /*且不是别名*/
                throw TableExistsError(target.tab_name);
            }
            /*是别名记录原表名并标记*/
            target.alia_name = target.tab_name;
            target.tab_name = tab_name;
            target.have_alia = true;
        }
        bool is_valid = false;
        for(auto &col : all_cols)
        {
            if(col.tab_name == target.tab_name && col.name == target.col_name)
            {
                is_valid = true;
            }
        }
        if(!is_valid)
        {
            throw ColumnNotFoundError(target.col_name);
        }
    }
    return target;
}

TabCol Analyze::semi_join_check_column(const std::vector<ColMeta> &all_cols, TabCol target, std::string left_table) {
    if (target.tab_name.empty()) {
        // Table name not specified, infer table name from column name
        std::string tab_name;
        for (auto &col : all_cols) {
            if (col.name == target.col_name) {
                if (!tab_name.empty()) {
                    throw AmbiguousColumnError(target.col_name);
                }
                tab_name = col.tab_name;
            }
        }
        if (tab_name.empty()) {
            throw ColumnNotFoundError(target.col_name);
        }
        target.tab_name = tab_name;
    } else {
        /** TODO: Make sure target column exists */
        /*先将别名处理为真的表名*/
        std::string tab_name;
        if(!sm_manager_->db_.is_table(target.tab_name))
        {
            /*该表未找到*/
            if(!sm_manager_->get_real_table_name(&tab_name,target.tab_name))
            {
                /*且不是别名*/
                throw TableExistsError(target.tab_name);
            }
            /*是别名记录原表名并标记*/
            target.alia_name = target.tab_name;
            target.tab_name = tab_name;
            target.have_alia = true;
        }
        /*半连接只能是左表*/
        if(target.tab_name != left_table)
        {
            throw InternalError("Semini join select cols must from left_table\n");
        }
        bool is_valid = false;
        for(auto &col : all_cols)
        {
            if(col.tab_name == target.tab_name && col.name == target.col_name)
            {
                is_valid = true;
            }
        }
        if(!is_valid)
        {
            throw ColumnNotFoundError(target.col_name);
        }
    }
    return target;
}

void Analyze::get_all_cols(const std::vector<std::string> &tab_names, std::vector<ColMeta> &all_cols) {
    for (auto &sel_tab_name : tab_names) {
        // 这里db_不能写成get_db(), 注意要传指针
        const auto &sel_tab_cols = sm_manager_->db_.get_table(sel_tab_name).cols;
        all_cols.insert(all_cols.end(), sel_tab_cols.begin(), sel_tab_cols.end());
    }
}

void Analyze::get_clause(const std::vector<std::shared_ptr<ast::BinaryExpr>> &sv_conds, std::vector<Condition> &conds) {
    conds.clear();
    for (auto &expr : sv_conds) {  
        Condition cond;
        cond.lhs_col = {.tab_name = expr->lhs->tab_name, .col_name = expr->lhs->col_name};
        cond.op = convert_sv_comp_op(expr->op);
        if (auto rhs_val = std::dynamic_pointer_cast<ast::Value>(expr->rhs)) {
            cond.is_rhs_val = true;
            cond.rhs_val = convert_sv_value(rhs_val);
        } else if (auto rhs_col = std::dynamic_pointer_cast<ast::Col>(expr->rhs)) {
            cond.is_rhs_val = false;
            cond.rhs_col = {.tab_name = rhs_col->tab_name, .col_name = rhs_col->col_name};
        }
        conds.push_back(cond);
    }
}

void Analyze::check_clause(const std::vector<std::string> &tab_names, std::vector<Condition> &conds) {
    // auto all_cols = get_all_cols(tab_names);
    std::vector<ColMeta> all_cols;
    get_all_cols(tab_names, all_cols);
    // Get raw values in where clause
    for (auto &cond : conds) {
        // Infer table name from column name
        /*检查条件的左列，如果右值是列则检查右列*/
        cond.lhs_col = check_column(all_cols, cond.lhs_col);
        if (!cond.is_rhs_val) {
            cond.rhs_col = check_column(all_cols, cond.rhs_col);
        }

        /*确定左值的类型*/
        TabMeta &lhs_tab = sm_manager_->db_.get_table(cond.lhs_col.tab_name);
        auto lhs_col = lhs_tab.get_col(cond.lhs_col.col_name);
        ColType lhs_type = lhs_col->type;

        /*确定右值的类型*/
        ColType rhs_type;
        if (cond.is_rhs_val) {
            cond.rhs_val.init_raw(lhs_col->len);
            rhs_type = cond.rhs_val.type;
        } else {
            TabMeta &rhs_tab = sm_manager_->db_.get_table(cond.rhs_col.tab_name);
            auto rhs_col = rhs_tab.get_col(cond.rhs_col.col_name);
            rhs_type = rhs_col->type;
        }

        /*判断左右值得类型是否相同,如果是int那么转成float否则抛出异常*/
        if (lhs_type != rhs_type) {
            if((lhs_type == TYPE_STRING && rhs_type != TYPE_STRING) 
            // || (lhs_type == TYPE_INT && rhs_type == TYPE_FLOAT)
            || (lhs_type != TYPE_STRING && rhs_type == TYPE_STRING))
            {
                throw IncompatibleTypeError(coltype2str(lhs_type), coltype2str(rhs_type));
            }
            else if(lhs_type == TYPE_INT && rhs_type == TYPE_FLOAT)
            {
                // if (cond.is_rhs_val)
                // {
                //     cond.rhs_val.type = TYPE_INT;
                //     cond.rhs_val.set_int(int(cond.rhs_val.float_val));
                // }
                // else
                // {
                //     TabMeta &rhs_tab = sm_manager_->db_.get_table(cond.rhs_col.tab_name);
                //     auto rhs_col = rhs_tab.get_col(cond.rhs_col.col_name);
                //     rhs_col->type = TYPE_INT;
                // }
            }
            else
            {
                if (cond.is_rhs_val)
                {
                    cond.rhs_val.type = TYPE_FLOAT;
                    cond.rhs_val.set_float(float(cond.rhs_val.int_val));
                }
                else
                {
                    TabMeta &rhs_tab = sm_manager_->db_.get_table(cond.rhs_col.tab_name);
                    auto rhs_col = rhs_tab.get_col(cond.rhs_col.col_name);
                    rhs_col->type = TYPE_FLOAT;
                }
            }
        }
    }
}

void Analyze::get_join(const std::vector<std::shared_ptr<ast::JoinExpr>> sv_joins, std::vector<JoinRef> &joins)
{
    joins.clear();
    for(const auto& entry : sv_joins)
    {
        JoinRef join;
        join.left_table = entry->left;
        join.right_table = entry->right;
        join.type = entry->type;

        /*连接条件*/
        get_clause(entry->conds, join.join_conds);
        joins.push_back(join);
    }
}

void Analyze::check_join_clause(std::vector<JoinRef> &joins)
{
    std::vector<std::string> join_tables;
    for(auto& entry : joins)
    {
        join_tables.push_back(entry.left_table);
        join_tables.push_back(entry.right_table);
        check_clause(join_tables, entry.join_conds);
        join_tables.clear();
    }
}

/*将ast得value转到std*/
Value Analyze::convert_sv_value(const std::shared_ptr<ast::Value> &sv_val) {
    Value val;
    if (auto int_lit = std::dynamic_pointer_cast<ast::IntLit>(sv_val)) {
        val.set_int(int_lit->val);
    } else if (auto float_lit = std::dynamic_pointer_cast<ast::FloatLit>(sv_val)) {
        val.set_float(float_lit->val);
    } else if (auto str_lit = std::dynamic_pointer_cast<ast::StringLit>(sv_val)) {
        val.set_str(str_lit->val);
    } else {
        throw InternalError("Unexpected sv value type");
    }
    return val;
}

SetValue Analyze::convert_stvalue(const std::shared_ptr<ast::BinaryUpdateExpr> &sv_val)
{
    SetValue value;
    value.op = convert_sv_comp_op(sv_val->op);

    if(value.op != OP_NONE)
    {
        value.is_expr = true;
        
        /*2.第二个表达式，只有右边是表达式的时候才有第二个表达式*/
        if (auto val_expr = std::dynamic_pointer_cast<ast::Value>(sv_val->rhs))
        {
            value.second_is_val = true;
            value.second_is_col = false;
            value.second_val = convert_sv_value(val_expr);
        }
        else if(auto col_expr = std::dynamic_pointer_cast<ast::Col>(sv_val->rhs))
        {
            value.second_is_col= true;
            value.second_is_val = false;
            value.second_col = {col_expr->tab_name, col_expr->col_name};
        }
        else
        {
            throw RMDBError("Unsupported left setvalue\n");
        }
    }

    /*1.第一个表达式*/
    if (auto val_expr = std::dynamic_pointer_cast<ast::Value>(sv_val->lhs))
    {
        value.first_is_val = true;
        value.first_is_col = false;
        value.first_val = convert_sv_value(val_expr);
    }
    else if(auto col_expr = std::dynamic_pointer_cast<ast::Col>(sv_val->lhs))
    {
        value.first_is_col = true;
        value.first_is_val = false;
        value.first_col = {col_expr->tab_name, col_expr->col_name};
    }
    else
    {
        throw RMDBError("Unsupported left setvalue\n");
    }

    return value;
}

/*将ast得value转到std*/
CompOp Analyze::convert_sv_comp_op(ast::SvCompOp op) {
    std::map<ast::SvCompOp, CompOp> m = {
        {ast::SV_OP_EQ, OP_EQ}, {ast::SV_OP_NE, OP_NE}, {ast::SV_OP_LT, OP_LT},
        {ast::SV_OP_GT, OP_GT}, {ast::SV_OP_LE, OP_LE}, {ast::SV_OP_GE, OP_GE},
        {ast::SV_OP_ADD, OP_ADD}, {ast::SV_OP_SUB, OP_SUB}, {ast::SV_OP_MUL, OP_MUL},
        {ast::SV_OP_DIV, OP_DIV}, {ast::SV_OP_NONE, OP_NONE}
    };
    return m.at(op);
}

/**
 * @brief 验证聚合查询的语义正确性
 *
 * @param stmt GroupByStmt AST节点
 * @param query 查询对象
 * @param all_cols 所有列的元数据
 */
void Analyze::validate_aggregation_query(std::shared_ptr<ast::GroupByStmt> stmt, std::shared_ptr<Query> query, const std::vector<ColMeta> &all_cols) {
    // 1. 检查WHERE子句中不能使用聚合函数
    validate_where_clause_no_aggregates(stmt->conds);

    // 2. 检查SELECT列表中的非聚合列必须出现在GROUP BY中
    validate_select_columns_in_group_by(stmt, all_cols);

    // 3. 检查聚合函数的参数类型
    validate_aggregate_function_types(stmt->agg_exprs, all_cols);

    // 4. 检查GROUP BY列的有效性
    validate_group_by_columns(stmt->group_cols, all_cols);
}

/**
 * @brief 检查WHERE子句中不能使用聚合函数
 */
void Analyze::validate_where_clause_no_aggregates(const std::vector<std::shared_ptr<ast::BinaryExpr>>& where_conds) {
    for (const auto& cond : where_conds) {
        // 检查左操作数
        if (auto agg_expr = std::dynamic_pointer_cast<ast::AggExpr>(cond->lhs)) {
            throw RMDBError("WHERE clause cannot contain aggregate functions");
        }

        // 检查右操作数
        if (auto agg_expr = std::dynamic_pointer_cast<ast::AggExpr>(cond->rhs)) {
            throw RMDBError("WHERE clause cannot contain aggregate functions");
        }
    }
}

/**
 * @brief 检查SELECT列表中的非聚合列必须出现在GROUP BY中
 */
void Analyze::validate_select_columns_in_group_by(std::shared_ptr<ast::GroupByStmt> stmt, const std::vector<ColMeta> &all_cols) {
    // 如果有GROUP BY子句，检查SELECT列表中的非聚合列是否都在GROUP BY中
    if (!stmt->group_cols.empty()) {
        for (const auto& sel_col : stmt->select_cols) {
            // 检查这个列是否是聚合函数
            bool is_aggregate = false;
            for (const auto& agg_expr : stmt->agg_exprs) {
                if (!agg_expr->alias.empty() && agg_expr->alias == sel_col->col_name) {
                    is_aggregate = true;
                    break;
                }
            }

            // 如果不是聚合函数，检查是否在GROUP BY中
            if (!is_aggregate) {
                bool found_in_group_by = false;
                for (const auto& group_col : stmt->group_cols) {
                    if (sel_col->tab_name == group_col->tab_name && sel_col->col_name == group_col->col_name) {
                        found_in_group_by = true;
                        break;
                    }
                }

                if (!found_in_group_by) {
                    throw RMDBError("SELECT list contains non-aggregate column '" + sel_col->col_name + "' that is not in GROUP BY clause");
                }
            }
        }
    }
}

/**
 * @brief 检查聚合函数的参数类型
 */
void Analyze::validate_aggregate_function_types(const std::vector<std::shared_ptr<ast::AggExpr>>& agg_exprs, const std::vector<ColMeta> &all_cols) {
    for (const auto& agg_expr : agg_exprs) {
        if (agg_expr->col == nullptr) {
            // COUNT(*) 总是有效的
            if (agg_expr->type != AGG_COUNT) {
                throw RMDBError("Only COUNT can use * as parameter");
            }
            continue;
        }

        // 查找列的类型
        ColMeta col_meta;
        bool found = false;
        for (const auto& col : all_cols) {
            if (col.tab_name == agg_expr->col->tab_name && col.name == agg_expr->col->col_name) {
                col_meta = col;
                found = true;
                break;
            }
        }

        if (!found) {
            throw ColumnNotFoundError(agg_expr->col->col_name);
        }

        // 检查聚合函数与列类型的兼容性
        switch (agg_expr->type) {
            case AGG_COUNT:
                // COUNT可以用于任何类型
                break;
            case AGG_SUM:
            case AGG_AVG:
                // SUM和AVG只能用于数值类型
                if (col_meta.type != TYPE_INT && col_meta.type != TYPE_FLOAT) {
                    throw RMDBError("SUM/AVG can only be used with numeric columns");
                }
                break;
            case AGG_MAX:
            case AGG_MIN:
                // MAX和MIN只能用于数值类型
                if (col_meta.type != TYPE_INT && col_meta.type != TYPE_FLOAT) {
                    throw RMDBError("MAX/MIN can only be used with numeric columns");
                }
                break;
            default:
                throw RMDBError("Unknown aggregate function type");
        }
    }
}

/**
 * @brief 检查GROUP BY列的有效性
 */
void Analyze::validate_group_by_columns(const std::vector<std::shared_ptr<ast::Col>>& group_cols, const std::vector<ColMeta> &all_cols) {
    for (const auto& group_col : group_cols) {
        // 使用check_column来验证列，这样可以正确处理表名推断
        TabCol temp_col = {group_col->tab_name, group_col->col_name};
        try {
            check_column(all_cols, temp_col);
        } catch (const ColumnNotFoundError& e) {
            throw ColumnNotFoundError(group_col->col_name);
        }
    }
}
