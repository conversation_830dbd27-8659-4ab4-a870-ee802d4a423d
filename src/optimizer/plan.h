/* Copyright (c) 2023 Renmin University of China
RMDB is licensed under Mulan PSL v2.
You can use this software according to the terms and conditions of the Mulan PSL v2.
You may obtain a copy of Mulan PSL v2 at:
        http://license.coscl.org.cn/MulanPSL2
THIS SOFTWARE IS PROVIDED ON AN "AS IS" BASIS, WITHOUT WARRANTIES OF ANY KIND,
EITHER EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO NON-INFRINGEMENT,
MERCHANTABILITY OR FIT FOR A PARTICULAR PURPOSE.
See the Mulan PSL v2 for more details. */

#pragma once

#include <cassert>
#include <cstring>
#include <memory>
#include <string>
#include <vector>
#include "parser/ast.h"
#include "parser/parser.h"

// Forward declarations
class SmManager;
struct Condition;
struct ColMeta;
struct TabCol;
struct Value;
struct SetClause;
struct ColDef;
struct TabMeta;

typedef enum PlanTag{
    T_Invalid = 1,
    T_Help,
    T_ShowTable,
    T_ShowIndex,
    T_DescTable,
    T_CreateTable,
    T_DropTable,
    T_CreateIndex,
    T_DropIndex,
    T_CreateStaticCheckpoint,
    T_SetKnob,
    T_Insert,
    T_Update,
    T_Delete,
    T_select,
    T_Transaction_begin,
    T_Transaction_commit,
    T_Transaction_abort,
    T_Transaction_rollback,
    T_SeqScan,
    T_IndexScan,
    T_NestLoop,
    T_SortMerge,    // sort merge join
    T_Sort,
    T_Projection,
    T_Explain,
    T_SEMI_join,
    T_Aggregation   // aggregation with group by and having
} PlanTag;

// 查询执行计划
class Plan
{
public:
    PlanTag tag;
    bool is_select_all_cols{false};
    virtual ~Plan() = default;
};

class ScanPlan : public Plan
{
    public:
        ScanPlan(PlanTag tag, SmManager *sm_manager, std::string tab_name, std::vector<Condition> conds, std::vector<std::string> index_col_names)
        {
            Plan::tag = tag;
            tab_name_ = std::move(tab_name);
            conds_ = std::move(conds);
            TabMeta &tab = sm_manager->db_.get_table(tab_name_);
            cols_ = tab.cols;
            len_ = cols_.back().offset + cols_.back().len;
            fed_conds_ = conds_;
            index_col_names_ = index_col_names;
        
        }
        ~ScanPlan(){}
        // 以下变量同ScanExecutor中的变量
        std::string tab_name_;                     
        std::vector<ColMeta> cols_;                
        std::vector<Condition> conds_;             
        size_t len_;                               
        std::vector<Condition> fed_conds_;
        std::vector<std::string> index_col_names_;
    
};

class JoinPlan : public Plan
{
    public:
        JoinPlan(PlanTag tag, std::shared_ptr<Plan> left, std::shared_ptr<Plan> right, std::vector<Condition> conds)
        {
            Plan::tag = tag;
            left_ = std::move(left);
            right_ = std::move(right);
            conds_ = std::move(conds);
            type = INNER_JOIN;
        }
        ~JoinPlan(){}
        // 左节点
        std::shared_ptr<Plan> left_;
        // 右节点
        std::shared_ptr<Plan> right_;
        // 连接条件
        std::vector<Condition> conds_;
        // future TODO: 后续可以支持的连接类型
        JoinType type;
};

class ProjectionPlan : public Plan
{
    public:
        ProjectionPlan(PlanTag tag, std::shared_ptr<Plan> subplan, std::vector<TabCol> sel_cols)
        {
            Plan::tag = tag;
            subplan_ = std::move(subplan);
            sel_cols_ = std::move(sel_cols);
        }
        ~ProjectionPlan(){}
        std::shared_ptr<Plan> subplan_;
        std::vector<TabCol> sel_cols_;
        bool is_all_cols{false};
};

class SortPlan : public Plan
{
    public:
        SortPlan(PlanTag tag, std::shared_ptr<Plan> subplan, std::vector<TabCol> sel_cols, std::vector<bool> is_desc, int limit = -1)
        {
            Plan::tag = tag;
            subplan_ = std::move(subplan);
            sel_cols_ = sel_cols;
            is_desc_ = is_desc;
            limit_ = limit;
        }
        ~SortPlan(){}
        std::shared_ptr<Plan> subplan_;
        // TabCol sel_col_;
        // bool is_desc_;
        std::vector<TabCol> sel_cols_;                 // 框架中只支持一个键排序，需要自行修改数据结构支持多个键排序
        std::vector<bool> is_desc_;
        int limit_;                                    // LIMIT数量，-1表示无限制
};

// dml语句，包括insert; delete; update; select语句　
class DMLPlan : public Plan
{
    public:
        DMLPlan(PlanTag tag, std::shared_ptr<Plan> subplan,std::string tab_name,
                std::vector<Value> values, std::vector<Condition> conds,
                std::vector<SetClause> set_clauses)
        {
            Plan::tag = tag;
            subplan_ = std::move(subplan);
            tab_name_ = std::move(tab_name);
            values_ = std::move(values);
            conds_ = std::move(conds);
            set_clauses_ = std::move(set_clauses);
        }
        ~DMLPlan(){}
        std::shared_ptr<Plan> subplan_;
        std::string tab_name_;
        std::vector<Value> values_;
        std::vector<Condition> conds_;
        std::vector<SetClause> set_clauses_;
        bool is_select_all_cols{false};
};

// ddl语句, 包括create/drop table; create/drop index;
class DDLPlan : public Plan
{
    public:
        DDLPlan(PlanTag tag, std::string tab_name, std::vector<std::string> col_names, std::vector<ColDef> cols)
        {
            Plan::tag = tag;
            tab_name_ = std::move(tab_name);
            cols_ = std::move(cols);
            tab_col_names_ = std::move(col_names);
        }
        ~DDLPlan(){}
        std::string tab_name_;
        std::vector<std::string> tab_col_names_;
        std::vector<ColDef> cols_;
};

// help; show tables; desc tables; begin; abort; commit; rollback语句对应的plan
class OtherPlan : public Plan
{
    public:
        OtherPlan(PlanTag tag, std::string tab_name)
        {
            Plan::tag = tag;
            tab_name_ = std::move(tab_name);            
        }
        ~OtherPlan(){}
        std::string tab_name_;
};

// Set Knob Plan
class SetKnobPlan : public Plan
{
    public:
        SetKnobPlan(ast::SetKnobType knob_type, bool bool_value) {
            Plan::tag = T_SetKnob;
            set_knob_type_ = knob_type;
            bool_value_ = bool_value;
        }
    ast::SetKnobType set_knob_type_;
    bool bool_value_;
};

// // Explain Plan
// class ExplainPlan : public Plan
// {
//     public:
//         ExplainPlan(ast::SetKnobType knob_type, bool bool_value) {
//             Plan::tag = T_SetKnob;
//             set_knob_type_ = knob_type;
//             bool_value_ = bool_value;
//         }
//     ast::SetKnobType set_knob_type_;
//     bool bool_value_;
// };

// Aggregation Plan for GROUP BY, HAVING and aggregate functions
class AggregationPlan : public Plan
{
    public:
        AggregationPlan(std::shared_ptr<Plan> subplan,
                       std::vector<TabCol> group_cols,
                       std::vector<ast::AggExpr> agg_exprs,
                       std::vector<Condition> having_conds,
                       std::vector<TabCol> sel_cols)
        {
            Plan::tag = T_Aggregation;
            subplan_ = std::move(subplan);
            group_cols_ = std::move(group_cols);
            agg_exprs_ = std::move(agg_exprs);
            having_conds_ = std::move(having_conds);
            sel_cols_ = std::move(sel_cols);
        }
        ~AggregationPlan(){}

        std::shared_ptr<Plan> subplan_;              // 子计划（通常是扫描或连接）
        std::vector<TabCol> group_cols_;             // GROUP BY 列
        std::vector<ast::AggExpr> agg_exprs_;        // 聚合表达式
        std::vector<Condition> having_conds_;        // HAVING 条件
        std::vector<TabCol> sel_cols_;               // 选择列（包括普通列和聚合列）
};

class plannerInfo{
    public:
    std::shared_ptr<ast::SelectStmt> parse;
    std::vector<Condition> where_conds;
    std::vector<TabCol> sel_cols;
    std::shared_ptr<Plan> plan;
    std::vector<std::shared_ptr<Plan>> table_scan_executors;
    std::vector<SetClause> set_clauses;
    plannerInfo(std::shared_ptr<ast::SelectStmt> parse_):parse(std::move(parse_)){}

};
