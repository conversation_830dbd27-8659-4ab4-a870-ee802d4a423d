/* Copyright (c) 2023 Renmin University of China
RMDB is licensed under Mulan PSL v2.
You can use this software according to the terms and conditions of the Mulan PSL v2.
You may obtain a copy of Mulan PSL v2 at:
        http://license.coscl.org.cn/MulanPSL2
THIS SOFTWARE IS PROVIDED ON AN "AS IS" BASIS, WITHOUT WARRANTIES OF ANY KIND,
EITHER EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO NON-INFRINGEMENT,
MERCHANTABILITY OR FIT FOR A PARTICULAR PURPOSE.
See the Mulan PSL v2 for more details. */

#pragma once
#include "execution_defs.h"
#include "execution_manager.h"
#include "executor_abstract.h"
#include "index/ix.h"
#include "system/sm.h"

class UpdateExecutor : public AbstractExecutor {
   private:
    TabMeta tab_;
    std::vector<Condition> conds_;
    RmFileHandle *fh_;
    std::vector<Rid> rids_;
    std::string tab_name_;
    std::vector<SetClause> set_clauses_;
    SmManager *sm_manager_;

   public:
    UpdateExecutor(SmManager *sm_manager, const std::string &tab_name, std::vector<SetClause> set_clauses,
                   std::vector<Condition> conds, std::vector<Rid> rids, Context *context) {
        sm_manager_ = sm_manager;
        tab_name_ = tab_name;
        set_clauses_ = set_clauses;
        tab_ = sm_manager_->db_.get_table(tab_name);
        fh_ = sm_manager_->fhs_.at(tab_name).get();
        conds_ = conds;
        rids_ = rids;
        context_ = context;
    }
    std::unique_ptr<RmRecord> Next() override {
    RmRecord new_record(fh_->get_file_hdr().record_size);

    // 1. 提取本次更新涉及的列名
    std::unordered_set<std::string> updated_cols;
    for (auto &clause : set_clauses_) {
        updated_cols.insert(clause.lhs.col_name);
    }

    // 2. 遍历所有要更新的元组
    for (const auto &rid : rids_) {
        auto old_record = fh_->get_record(rid, context_);
        new_record.SetData(old_record->data);  // 基于老数据构造新 record

        // 3. 更新新 record 中的字段值
        for (const auto &clause : set_clauses_) {
            auto col = tab_.get_col(clause.lhs.col_name);
            Value val = clause.rhs.first_val;

            // 类型转换
            if (col->type != val.type) {
                if ((col->type == TYPE_STRING && val.type != TYPE_STRING) ||
                    (col->type != TYPE_STRING && val.type == TYPE_STRING)) {
                    throw IncompatibleTypeError(coltype2str(col->type), coltype2str(val.type));
                } else if (col->type == TYPE_INT && val.type == TYPE_FLOAT) {
                    val.set_int(static_cast<int>(val.float_val));
                } else {
                    val.set_float(static_cast<float>(val.int_val));
                }
            }

            val.init_raw(col->len);
            memcpy(new_record.data + col->offset, val.raw->data, col->len);
        }

        // 4. 构造新 key 并检查唯一性（仅检查受影响的索引）
        std::vector<std::unique_ptr<char[]>> new_keys;  // 缓存 key，后续重复用
        std::vector<IxIndexHandle *> related_indexes;   // 对应 ih
        for (auto &index : tab_.indexes) {
            // 检查这个索引是否和更新字段有关
            bool related = false;
            for (auto &col : index.cols) {
                if (updated_cols.count(col.name)) {
                    related = true;
                    break;
                }
            }
            if (!related) continue;

            // 构造新 key
            auto key = std::make_unique<char[]>(index.col_tot_len);
            int offset = 0;
            for (int j = 0; j < index.col_num; ++j) {
                memcpy(key.get() + offset, new_record.data + index.cols[j].offset, index.cols[j].len);
                offset += index.cols[j].len;
            }

            // 获取对应 ih 并检查唯一性（insert_entry 会抛异常）
            auto ih = sm_manager_->ihs_.at(
                sm_manager_->get_ix_manager()->get_index_name(tab_name_, index.cols)).get();
            ih->insert_entry(key.get(), rid, context_->txn_);  // 预插入验证唯一性

            related_indexes.push_back(ih);
            new_keys.push_back(std::move(key));
        }

        // 5. 删除旧索引项（仅删除受影响的索引）
        for (size_t i = 0; i < related_indexes.size(); ++i) {
            auto ih = related_indexes[i];
            auto &index = tab_.indexes[i];  // 找到对应 index meta
            std::unique_ptr<char[]> old_key = std::make_unique<char[]>(index.col_tot_len);
            int offset = 0;
            for (int j = 0; j < index.col_num; ++j) {
                memcpy(old_key.get() + offset, old_record->data + index.cols[j].offset, index.cols[j].len);
                offset += index.cols[j].len;
            }
            ih->delete_entry(old_key.get(), context_->txn_);
        }

        // 6. 更新表记录
        fh_->update_record(rid, new_record.data, context_);

        /*3.写事务的写集合*/
        WriteRecord *wr = new WriteRecord(WType::UPDATE_TUPLE, tab_name_, rid, *(old_record.get()));
        context_->txn_->append_write_record(wr);
    }

    return nullptr;
}

    Rid &rid() override { return _abstract_rid; }
};
