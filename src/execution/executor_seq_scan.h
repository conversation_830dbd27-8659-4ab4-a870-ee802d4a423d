/* Copyright (c) 2023 Renmin University of China
RMDB is licensed under Mulan PSL v2.
You can use this software according to the terms and conditions of the Mulan PSL v2.
You may obtain a copy of Mulan PSL v2 at:
        http://license.coscl.org.cn/MulanPSL2
THIS SOFTWARE IS PROVIDED ON AN "AS IS" BASIS, WITHOUT WARRANTIES OF ANY KIND,
EITHER EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO NON-INFRINGEMENT,
MERCHANTABILITY OR FIT FOR A PARTICULAR PURPOSE.
See the Mulan PSL v2 for more details. */

#pragma once

#include "execution_defs.h"
#include "execution_manager.h"
#include "executor_abstract.h"
#include "index/ix.h"
#include "system/sm.h"

class SeqScanExecutor : public AbstractExecutor {
   private:
    std::string tab_name_;              // 表的名称
    std::vector<Condition> conds_;      // scan的条件
    RmFileHandle *fh_;                  // 表的数据文件句柄
    std::vector<ColMeta> cols_;         // scan后生成的记录的字段
    size_t len_;                        // scan后生成的每条记录的长度
    std::vector<Condition> fed_conds_;  // 同conds_，两个字段相同

    Rid rid_;
    std::unique_ptr<RecScan> scan_;     // table_iterator

    SmManager *sm_manager_;

    // bool is_end{false};
    // bool is_find{false};

   public:
    SeqScanExecutor(SmManager *sm_manager, std::string tab_name, std::vector<Condition> conds, Context *context) {
        sm_manager_ = sm_manager;
        tab_name_ = std::move(tab_name);
        conds_ = std::move(conds);
        TabMeta &tab = sm_manager_->db_.get_table(tab_name_);
        fh_ = sm_manager_->fhs_.at(tab_name_).get();
        cols_ = tab.cols;
        len_ = cols_.back().offset + cols_.back().len;

        context_ = context;

        fed_conds_ = conds_;
    }

    void beginTuple() override {
        /*1.定位到第一个元组*/
        scan_ = std::make_unique<RmScan>(fh_);
        rid_ = scan_->rid();
    }

    void nextTuple() override {
        /*1.找到下一条记录*/
        if(!is_end())
        {
            scan_->next();
            rid_ = scan_->rid();
        }
    }

    std::unique_ptr<RmRecord> Next() override {
        /*1.从当前元组开始直到找到下一条符合条件的记录*/
        // is_find = false;
        while(!is_end())
        {
            std::unique_ptr<RmRecord> record = fh_->get_record(rid_,context_);
            /*1.1判断是否满足过滤条件*/
            /*1.1.1从元组取出对应列的值*/
            bool flag = true;
            for(size_t i = 0; i < fed_conds_.size(); i++)
            {
                Condition cond = fed_conds_[i];
                TabCol ltc = cond.lhs_col;
                Value &rv = cond.rhs_val;
                Value recordColValue;
                auto col = get_col(cols_,ltc);
                std::string str(record->data + col->offset,col->len);
                switch(col->type)
                {
                    case TYPE_INT:
                        recordColValue.set_int(*reinterpret_cast<const int*>(record->data + col->offset));
                        break;
                    case TYPE_FLOAT:
                        recordColValue.set_float(*reinterpret_cast<const float*>(record->data + col->offset));
                        break;
                    case TYPE_STRING:
                        recordColValue.set_str(str.c_str());
                        break;
                    default:
                        throw InternalError("invalid type\n");
                }
                recordColValue.init_raw(col->len);
                if(!cond.ValueIsEqual(recordColValue,rv,cond.op))
                {
                    flag = false;
                    break;
                }
            }
            if(flag)
            {
                return record;
            }
            nextTuple();
        }
        return nullptr;
    }

    const std::vector<ColMeta> &cols() const override
    {
        return cols_;
    }
    size_t tupleLen() const override{ return len_; };
    bool is_end() const override{
        return scan_->is_end();
    }

    ColMeta get_col_offset(const TabCol &target) override{
        return *(get_col(cols_,target));
    };

    Rid &rid() override { return rid_; }
};
