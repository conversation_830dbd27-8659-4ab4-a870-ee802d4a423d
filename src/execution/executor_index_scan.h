#pragma once

#include "execution_defs.h"
#include "execution_manager.h"
#include "executor_abstract.h"
#include "index/ix.h"
#include "system/sm.h"

class IndexScanExecutor : public AbstractExecutor {
   private:
    std::string tab_name_;                      // 表名称
    TabMeta tab_;                               // 表的元数据
    std::vector<Condition> conds_;              // 扫描条件
    RmFileHandle *fh_;                          // 表的数据文件句柄
    std::vector<ColMeta> cols_;                 // 需要读取的字段
    size_t len_;                                // 选取出来的一条记录的长度
    std::vector<Condition> fed_conds_;          // 扫描条件，和conds_字段相同

    std::vector<std::string> index_col_names_;  // index scan涉及到的索引包含的字段
    IndexMeta index_meta_;                      // index scan涉及到的索引元数据

    Rid rid_;
    std::unique_ptr<IxScan> scan_;

    SmManager *sm_manager_;

   public:
    IndexScanExecutor(SmManager *sm_manager, std::string tab_name, std::vector<Condition> conds, std::vector<std::string> index_col_names,
                    Context *context) {
        sm_manager_ = sm_manager;
        context_ = context;
        tab_name_ = std::move(tab_name);
        tab_ = sm_manager_->db_.get_table(tab_name_);
        conds_ = std::move(conds);
        // index_no_ = index_no;
        index_col_names_ = index_col_names; 
        index_meta_ = *(tab_.get_index_meta(index_col_names_));
        fh_ = sm_manager_->fhs_.at(tab_name_).get();
        cols_ = tab_.cols;
        len_ = cols_.back().offset + cols_.back().len;
        std::map<CompOp, CompOp> swap_op = {
            {OP_EQ, OP_EQ}, {OP_NE, OP_NE}, {OP_LT, OP_GT}, {OP_GT, OP_LT}, {OP_LE, OP_GE}, {OP_GE, OP_LE},
        };

        for (auto &cond : conds_) {
            if (cond.lhs_col.tab_name != tab_name_) {
                // lhs is on other table, now rhs must be on this table
                assert(!cond.is_rhs_val && cond.rhs_col.tab_name == tab_name_);
                // swap lhs and rhs
                std::swap(cond.lhs_col, cond.rhs_col);
                cond.op = swap_op.at(cond.op);
            }
        }
        fed_conds_ = conds_;
    }

    std::string getType() { return "indexScan"; }

void beginTuple() override {
    check_runtime_conds();
    std::cout << "Entering beginTuple()" << std::endl;

    // 获取索引句柄
    auto ih = sm_manager_->ihs_.at(
        sm_manager_->get_ix_manager()->get_index_name(tab_name_, index_col_names_, sm_manager_->db_)
    ).get();
    BufferPoolManager *bpm = sm_manager_->get_bpm();

    Iid lower = ih->leaf_begin();
    Iid upper = ih->leaf_end();

    // 构造联合 key buffer
    int key_len = index_meta_.col_tot_len;
    std::vector<char> lower_key(key_len, 0);
    std::vector<char> upper_key(key_len, 0);

    int prefix_len = 0;
    bool found_range = false;

    for (int i = 0; i < index_meta_.col_num; ++i) {
        const auto &index_col = index_meta_.cols[i];

        auto cond_it = std::find_if(fed_conds_.begin(), fed_conds_.end(), [&](const Condition &cond) {
            return cond.lhs_col.tab_name == tab_name_
                && cond.lhs_col.col_name == index_col.name
                && cond.is_rhs_val
                && cond.op != OP_NE;
        });

        if (cond_it == fed_conds_.end()) {
            break;  // 联合索引必须是连续匹配
        }

        const Condition &cond = *cond_it;
        const char *rhs_key = cond.rhs_val.raw->data;

        if (cond.op == OP_EQ) {
            memcpy(lower_key.data() + prefix_len, rhs_key, index_col.len);
            memcpy(upper_key.data() + prefix_len, rhs_key, index_col.len);
            prefix_len += index_col.len;
        } else if (cond.op == OP_LT || cond.op == OP_LE) {
            memcpy(upper_key.data() + prefix_len, rhs_key, index_col.len);
            prefix_len += index_col.len;

            // 填充剩余字段为 0xFF
            if (prefix_len < key_len) {
                memset(upper_key.data() + prefix_len, 0xFF, key_len - prefix_len);
                memset(lower_key.data() + prefix_len, 0x00, key_len - prefix_len);
            }

            lower = ih->lower_bound(lower_key.data());
            upper = (cond.op == OP_LT) ? ih->lower_bound(upper_key.data())
                                       : ih->upper_bound(upper_key.data());
            found_range = true;
            break;
        } else if (cond.op == OP_GT || cond.op == OP_GE) {
            memcpy(lower_key.data() + prefix_len, rhs_key, index_col.len);
            prefix_len += index_col.len;

            if (prefix_len < key_len) {
                memset(lower_key.data() + prefix_len, 0x00, key_len - prefix_len);
                memset(upper_key.data() + prefix_len, 0xFF, key_len - prefix_len);
            }

            lower = (cond.op == OP_GT) ? ih->upper_bound(lower_key.data())
                                       : ih->lower_bound(lower_key.data());
            upper = ih->leaf_end();
            found_range = true;
            break;
        } else {
            break;
        }
    }

    // 如果是全等值前缀，构造精确范围
    if (prefix_len > 0 && !found_range) {
        // 填充剩余字段为 0x00 / 0xFF
        memset(lower_key.data() + prefix_len, 0x00, key_len - prefix_len);
        memset(upper_key.data() + prefix_len, 0xFF, key_len - prefix_len);

        lower = ih->lower_bound(lower_key.data());
        upper = ih->upper_bound(upper_key.data());
    }

    // 构建 IxScan 执行器
    scan_ = std::make_unique<IxScan>(ih, lower, upper, bpm);

    // 滤除不符合 fed_conds_ 的记录
    while (!scan_->is_end()) {
        rid_ = scan_->rid();
        auto rec = fh_->get_record(rid_, context_);
        if (eval_conds(cols_, fed_conds_, rec.get())) {
            break;
        }
        scan_->next();
    }

    std::cout << "Exiting beginTuple()" << std::endl;
}




    void nextTuple() override {
        std::cout << "Entering nextTuple()" << std::endl;
        check_runtime_conds();
        scan_->next();
        while ( !scan_->is_end() ) {
            rid_ = scan_->rid();
            auto rec = fh_->get_record(rid_, context_);
            // 利用eval_conds判断是否当前记录(rec.get())满足谓词条件
            if( eval_conds( cols_, fed_conds_, rec.get() ) ) {
                context_->lock_mgr_->lock_shared_on_record(context_->txn_, rid_, fh_->GetFd());
                break;
            }
            scan_->next();  // 找下一个有record的位置
        }
    }


    bool is_end() const override { return scan_->is_end(); }

    size_t tupleLen() const override { return len_; }

    const std::vector<ColMeta> &cols() const override { return cols_; }

    std::unique_ptr<RmRecord> Next() override {
        assert(!is_end());
        return fh_->get_record(rid_, context_);
    }

    void feed(const std::map<TabCol, Value> &feed_dict) override {
        fed_conds_ = conds_;
        for (auto &cond : fed_conds_) {
            // lab3 task2 todo
            // 参考seqscan
            // lab3 task2 todo end
			if (!cond.is_rhs_val && cond.rhs_col.tab_name != tab_name_) {
                cond.is_rhs_val = true;
                cond.rhs_val = feed_dict.at(cond.rhs_col);
            }
        }
        check_runtime_conds();
    }

    Rid &rid() override { return rid_; }

    void check_runtime_conds() {
        for (auto &cond : fed_conds_) {
            assert(cond.lhs_col.tab_name == tab_name_);
            if (!cond.is_rhs_val) {
                assert(cond.rhs_col.tab_name == tab_name_);
            }
        }
    }

    bool eval_cond(const std::vector<ColMeta> &rec_cols, const Condition &cond, const RmRecord *rec) {
        auto lhs_col = get_col(rec_cols, cond.lhs_col);
        char *lhs = rec->data + lhs_col->offset;
        char *rhs;
        ColType rhs_type;
        if (cond.is_rhs_val) {
            rhs_type = cond.rhs_val.type;
            rhs = cond.rhs_val.raw->data;
        } else {
            // rhs is a column
            auto rhs_col = get_col(rec_cols, cond.rhs_col);
            rhs_type = rhs_col->type;
            rhs = rec->data + rhs_col->offset;
        }
        assert(rhs_type == lhs_col->type);  // TODO convert to common type
        int cmp = ix_compare(lhs, rhs, rhs_type, lhs_col->len);
        if (cond.op == OP_EQ) {
            return cmp == 0;
        } else if (cond.op == OP_NE) {
            return cmp != 0;
        } else if (cond.op == OP_LT) {
            return cmp < 0;
        } else if (cond.op == OP_GT) {
            return cmp > 0;
        } else if (cond.op == OP_LE) {
            return cmp <= 0;
        } else if (cond.op == OP_GE) {
            return cmp >= 0;
        } else {
            throw InternalError("Unexpected op type");
        }
    }

    bool eval_conds(const std::vector<ColMeta> &rec_cols, const std::vector<Condition> &conds, const RmRecord *rec) {
        return std::all_of(conds.begin(), conds.end(),
                           [&](const Condition &cond) { return eval_cond(rec_cols, cond, rec); });
    }
};