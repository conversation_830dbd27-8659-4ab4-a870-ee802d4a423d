%{
#include "ast.h"
#include "yacc.tab.h"
#include <iostream>
#include <memory>

int yylex(YYSTYPE *yylval, YYLTYPE *yylloc);

void yyerror(YYLTYPE *locp, const char* s) {
    std::cerr << "Parser Error at line " << locp->first_line << " column " << locp->first_column << ": " << s << std::endl;
}

using namespace ast;
%}

// request a pure (reentrant) parser
%define api.pure full
// enable location in error handler
%locations
// enable verbose syntax error message
%define parse.error verbose

// keywords
%token SHOW TABLES CREATE TABLE DROP DESC INSERT INTO VALUES DELETE FROM ASC EXPLAIN ORDER BY ON SEMI COUNT MIN MAX AVG SUM GROUP HAVING AS
WHERE UPDATE SET SELECT INT CHAR FLOAT INDEX AND JOIN EXIT HELP TXN_BEGIN TXN_COMMIT TXN_ABORT TXN_ROLLBACK ORDER_BY ENABLE_NESTLOOP ENABLE_SORTMERGE STATIC_CHECKPOINT LIMIT
// non-keywords
%token LEQ NEQ GEQ T_EOF

// type-specific tokens
%token <sv_str> IDENTIFIER VALUE_STRING 
%token <sv_int> VALUE_INT
%token <sv_float> VALUE_FLOAT
%token <sv_bool> VALUE_BOOL

// specify types for non-terminal symbol
%type <sv_node> stmt dbStmt ddl dml txnStmt setStmt explainStmt
%type <sv_field> field
%type <sv_fields> fieldList
%type <sv_type_len> type
%type <sv_comp_op> op
%type <sv_expr> expr
%type <sv_val> value
%type <sv_vals> valueList
%type <sv_str> tbName colName optAlias
%type <sv_strpair> tableRef
%type <sv_strs> colNameList
%type <sv_table_expr> tableExpression
/* %type <sv_mapstr> tableList */
%type <sv_col> col
%type <sv_cols> colList
%type <sv_select_items> selector
%type <sv_select_item> select_item
%type <sv_set_clause> setClause
%type <sv_set_clauses> setClauses
%type <sv_cond> condition
%type <sv_twoexpr> valuexpr
%type <sv_conds> whereClause optWhereClause
%type <sv_orderby>  order_clause opt_order_clause
%type <sv_orderby_dir> opt_asc_desc
%type <sv_setKnobType> set_knob_type
%type <sv_having> opt_having havingClause
%type <sv_having_cond> havingCondition
%type <sv_group_by> opt_group_by
%type <sv_agg> aggExpr
%type <sv_int> opt_limit

%%
start:
        stmt ';'
    {
        parse_tree = $1;
        YYACCEPT;
    }
    |   HELP
    {
        parse_tree = std::make_shared<Help>();
        YYACCEPT;
    }
    |   EXIT
    {
        parse_tree = nullptr;
        YYACCEPT;
    }
    |   T_EOF
    {
        parse_tree = nullptr;
        YYACCEPT;
    }
    ;

stmt:
        dbStmt
    |   ddl
    |   dml
    |   txnStmt
    |   setStmt
    |   explainStmt
    ;

txnStmt:
        TXN_BEGIN
    {
        $$ = std::make_shared<TxnBegin>();
    }
    |   TXN_COMMIT
    {
        $$ = std::make_shared<TxnCommit>();
    }
    |   TXN_ABORT
    {
        $$ = std::make_shared<TxnAbort>();
    }
    | TXN_ROLLBACK
    {
        $$ = std::make_shared<TxnRollback>();
    }
    ;

dbStmt:
        SHOW TABLES
    {
        $$ = std::make_shared<ShowTables>();
    }
    ;

setStmt:
        SET set_knob_type '=' VALUE_BOOL
    {
        $$ = std::make_shared<SetStmt>($2, $4);
    }
    ;

ddl:
        CREATE TABLE tbName '(' fieldList ')'
    {
        $$ = std::make_shared<CreateTable>($3, $5);
    }
    |   DROP TABLE tbName
    {
        $$ = std::make_shared<DropTable>($3);
    }
    |   DESC tbName
    {
        $$ = std::make_shared<DescTable>($2);
    }
    |   CREATE INDEX tbName '(' colNameList ')'
    {
        $$ = std::make_shared<CreateIndex>($3, $5);
    }
    |   DROP INDEX tbName '(' colNameList ')'
    {
        $$ = std::make_shared<DropIndex>($3, $5);
    }
    |   SHOW INDEX FROM tbName
    {
        $$ = std::make_shared<ShowIndex>($4);
    }
    |   CREATE STATIC_CHECKPOINT
    {
        $$ = std::make_shared<CreateStaticCheckpoint>();
    }
    ;

dml:
        INSERT INTO tbName VALUES '(' valueList ')'
    {
        $$ = std::make_shared<InsertStmt>($3, $6);
    }
    |   DELETE FROM tbName optWhereClause
    {
        $$ = std::make_shared<DeleteStmt>($3, $4);
    }
    |   UPDATE tbName SET setClauses optWhereClause
    {
        $$ = std::make_shared<UpdateStmt>($2, $4, $5);
    }
    |   SELECT selector FROM tableExpression optWhereClause opt_group_by opt_having opt_order_clause opt_limit
    {


        // 检查是否包含聚合函数
        std::vector<std::shared_ptr<AggExpr>> aggexprs;
        std::vector<std::shared_ptr<Col>> cols;
        std::vector<std::shared_ptr<Col>> select_cols; // 只包含普通列，不包括聚合函数别名
        bool has_aggregate = false;
        for (const auto& item : $2) {
            if (item->agg_expr) {
                // 如果聚合函数没有别名，生成默认列名
                std::string col_name = item->agg_expr->alias;
                if (col_name.empty()) {
                    // 生成默认列名，如 MAX(id), COUNT(*) 等
                    std::string func_name;
                    switch (item->agg_expr->type) {
                        case AGG_COUNT: func_name = "COUNT"; break;
                        case AGG_SUM: func_name = "SUM"; break;
                        case AGG_AVG: func_name = "AVG"; break;
                        case AGG_MAX: func_name = "MAX"; break;
                        case AGG_MIN: func_name = "MIN"; break;
                        default: func_name = "UNKNOWN"; break;
                    }

                    if (item->agg_expr->col) {
                        col_name = func_name + "(" + item->agg_expr->col->col_name + ")";
                    } else {
                        col_name = func_name + "(*)";
                    }
                    item->agg_expr->alias = col_name;  // 更新别名
                }

                std::shared_ptr<Col> col = std::make_shared<Col>("", col_name);
                aggexprs.push_back(item->agg_expr);
                cols.push_back(col);
                has_aggregate = true;
            }
            else
            {
                cols.push_back(item->column);
                select_cols.push_back(item->column); // 只添加普通列到select_cols
            }
        }

        if (has_aggregate || !$6.empty()) {
            // 构建聚合查询（有聚合函数或有GROUP BY子句）
            std::vector<std::string> Agg_tables;
            for(auto& aggtab : $4.tables)
            {
                Agg_tables.push_back(aggtab.first);
            }
            $$ = std::make_shared<GroupByStmt>(
                select_cols, // 使用select_cols而不是cols
                aggexprs,
                Agg_tables,
                std::vector<std::shared_ptr<JoinExpr>>{},
                $6,
                $7,
                $5,
                $8,
                $9  // 添加LIMIT参数
            );
        }
        else
        {
            // 检查是否包含SEMI JOIN
            bool has_semi_join = false;
            for (const auto& join : $4.joins) {
                if (join->type == SEMI_JOIN) {
                    has_semi_join = true;
                    break;
                }
            }

            if (has_semi_join) {
                $$ = std::make_shared<SemijoinStmt>(cols, $4.tables, $4.joins, $5);
            }
            else {
                // 普通查询
                $$ = std::make_shared<SelectStmt>(cols, $4.tables, $4.joins, $5, $8, $9);  // 添加LIMIT参数
            }
        }
        

    }
    ;

explainStmt:
    EXPLAIN dml
    {
        $$ = std::make_shared<ExplainStmt>($2);
    }
    | EXPLAIN ddl
    {
        $$ = std::make_shared<ExplainStmt>($2);
    }
    ;

fieldList:
        field
    {
        $$ = std::vector<std::shared_ptr<Field>>{$1};
    }
    |   fieldList ',' field
    {
        $$.push_back($3);
    }
    ;

colNameList:
        colName
    {
        $$ = std::vector<std::string>{$1};
    }
    | colNameList ',' colName
    {
        $$.push_back($3);
    }
    ;

field:
        colName type
    {
        $$ = std::make_shared<ColDef>($1, $2);
    }
    ;

type:
        INT
    {
        $$ = std::make_shared<TypeLen>(SV_TYPE_INT, sizeof(int));
    }
    |   CHAR '(' VALUE_INT ')'
    {
        $$ = std::make_shared<TypeLen>(SV_TYPE_STRING, $3);
    }
    |   FLOAT
    {
        $$ = std::make_shared<TypeLen>(SV_TYPE_FLOAT, sizeof(float));
    }
    ;

valueList:
        value
    {
        $$ = std::vector<std::shared_ptr<Value>>{$1};
    }
    |   valueList ',' value
    {
        $$.push_back($3);
    }
    ;

value:
        VALUE_INT
    {
        $$ = std::make_shared<IntLit>($1);
    }
    |   VALUE_FLOAT
    {
        $$ = std::make_shared<FloatLit>($1);
    }
    |   VALUE_STRING
    {
        $$ = std::make_shared<StringLit>($1);
    }
    |   VALUE_BOOL
    {
        $$ = std::make_shared<BoolLit>($1);
    }
    ;

condition:
        col op expr
    {
        $$ = std::make_shared<BinaryExpr>($1, $2, $3);
    }
    ;


optWhereClause:
        /* epsilon */ { /* ignore*/ }
    |   WHERE whereClause
    {
        $$ = $2;
    }
    ;

whereClause:
        condition 
    {
        $$ = std::vector<std::shared_ptr<BinaryExpr>>{$1};
    }
    |   whereClause AND condition
    {
        $$.push_back($3);
    }
    ;

// 分组子句解析
opt_group_by:
    GROUP BY colList
    {
        $$ = $3;
    }
    |   /* epsilon */ { $$ = std::vector<std::shared_ptr<Col>>{}; }
;

// HAVING子句解析
opt_having:
    HAVING havingClause
    {
        $$ = $2;
    }
    |   /* epsilon */ { $$ = std::vector<std::shared_ptr<BinaryAggExpr>>{}; }
;

// HAVING条件解析 - 支持普通条件和聚合条件
havingClause:
    havingCondition 
    {
        $$ = std::vector<std::shared_ptr<BinaryAggExpr>>{$1};
    }
    |   havingClause AND havingCondition
    {
        $$.push_back($3);
    }
;


// 统一的HAVING条件类型
havingCondition:
    aggExpr op expr    // 聚合函数条件 (如 COUNT(*) > 5)
    {
        $$ = std::make_shared<BinaryAggExpr>($1, $2, $3);
    }
    | col op expr      // 普通列条件 (如 id > 100)
    {
        $$ = std::make_shared<BinaryAggExpr>($1, $2, $3);
    }
    | col op aggExpr   // 混合条件 (如 score > AVG(score))
    {
        $$ = std::make_shared<BinaryAggExpr>($1, $2, $3);
    }
;

// 聚合表达式解析
aggExpr:
    COUNT '(' '*' ')'
    {
        $$ = std::make_shared<AggExpr>(AGG_COUNT, nullptr, "");
    }
    |   COUNT '(' col ')'
    {
        $$ = std::make_shared<AggExpr>(AGG_COUNT, $3, "");
    }
    |   SUM '(' col ')'
    {
        $$ = std::make_shared<AggExpr>(AGG_SUM, $3, "");
    }
    |   AVG '(' col ')'
    {
        $$ = std::make_shared<AggExpr>(AGG_AVG, $3, "");
    }
    |   MAX '(' col ')'
    {
        $$ = std::make_shared<AggExpr>(AGG_MAX, $3, "");
    }
    |   MIN '(' col ')'
    {
        $$ = std::make_shared<AggExpr>(AGG_MIN, $3, "");
    }
;




col:
        tbName '.' colName
    {
        $$ = std::make_shared<Col>($1, $3);
    }
    |   colName
    {
        $$ = std::make_shared<Col>("", $1);
    }
    ;

colList:
        col
    {
        $$ = std::vector<std::shared_ptr<Col>>{$1};
    }
    |   colList ',' col
    {
        $$.push_back($3);
    }
    ;

op:
        '='
    {
        $$ = SV_OP_EQ;
    }
    |   '<'
    {
        $$ = SV_OP_LT;
    }
    |   '>'
    {
        $$ = SV_OP_GT;
    }
    |    '+'
    {
        printf("op: +\n");
        $$ = SV_OP_ADD;
    }
    |   '-'
    {
        $$ = SV_OP_SUB;
    }
    |   '*'
    {
        $$ = SV_OP_MUL;
    }
    |   '/'
    {
        $$ = SV_OP_DIV;
    }
    |   NEQ
    {
        $$ = SV_OP_NE;
    }
    |   LEQ
    {
        $$ = SV_OP_LE;
    }
    |   GEQ
    {
        $$ = SV_OP_GE;
    }
    ;


expr:
        value
    {
        printf("expr: value\n");
        $$ = std::static_pointer_cast<Expr>($1);
    }
    |   col
    {
        printf("expr: col\n");
        $$ = std::static_pointer_cast<Expr>($1);
    }
    ;

setClauses:
        setClause
    {
        $$ = std::vector<std::shared_ptr<SetClause>>{$1};
    }
    |   setClauses ',' setClause
    {
        $$.push_back($3);
    }
    ;

setClause:
        colName '=' valuexpr
    {
        printf("setClause\n");
        $$ = std::make_shared<SetClause>($1, $3);
    }
    ;

valuexpr:
        expr op expr
    {
        printf("valuexpr\n");
        $$ = std::make_shared<BinaryUpdateExpr>($1, $2, $3);
    }
    | value
    {
        $$ = std::make_shared<BinaryUpdateExpr>($1, SV_OP_NONE, nullptr);
    }

// 修改selector规则
selector:
    '*'
    {
        $$ = {};
    }
    |   select_item
    {
        $$ = std::vector<std::shared_ptr<SelectItem>>{$1};
    }
    |   selector ',' select_item
    {
        $$.push_back($3);
    }
    ;

// 新增select_item规则，支持列和聚合函数
select_item:
    col
    {
        $$ = std::make_shared<SelectItem>($1, nullptr);
    }
    |   aggExpr optAlias
    {
        $1->alias = $2;  // $2现在是std::string，可以直接赋值
        $$ = std::make_shared<SelectItem>(nullptr, $1);
    }
    ;


// 表表达式规则
tableExpression:
    tableRef                  // 单个表
    {
        $$.tables = std::vector<std::pair<std::string, std::string>>{$1};
        $$.joins = {};
    }
    |   tableExpression ',' tableRef   // 逗号分隔的表（隐式JOIN）
    {
        $$.tables = $1.tables;
        $$.tables.push_back($3);
        $$.joins = $1.joins;
    }
    |   tableExpression JOIN tableRef ON whereClause  // 显式JOIN（带ON条件）
    {
        $$.tables = $1.tables;
        $$.tables.push_back($3);
        
        // 创建JOIN表达式
        std::string left_table = $1.tables.back().first;
        std::string right_table = $3.first;
        auto join = std::make_shared<JoinExpr>(left_table, right_table, $5, INNER_JOIN);
        $$.joins = $1.joins;
        $$.joins.push_back(join);
    }
    |   tableExpression JOIN tableRef  // 简化JOIN（条件在WHERE中）
    {
        $$.tables = $1.tables;
        $$.tables.push_back($3);
        $$.joins = $1.joins;
        
        // 这种情况下JOIN条件会在WHERE子句中处理
        // 这里仅记录JOIN操作，不设置ON条件
        std::string left_table = $1.tables.back().first;
        std::string right_table = $3.first;
        auto join = std::make_shared<JoinExpr>(left_table, right_table, std::vector<std::shared_ptr<BinaryExpr>>(), INNER_JOIN);
        $$.joins.push_back(join);
    }
    |   tableExpression SEMI JOIN tableRef ON whereClause  // SEMI JOIN带ON条件
    {
        $$.tables = $1.tables;
        $$.tables.push_back($4);
        
        // 创建SEMI JOIN表达式
        std::string left_table = $1.tables.back().first;
        std::string right_table = $4.first;
        auto join = std::make_shared<JoinExpr>(left_table, right_table, $6, SEMI_JOIN);
        $$.joins = $1.joins;
        $$.joins.push_back(join);
    }
    ;

// 新增：表引用（表名+可选别名）
tableRef:
        tbName                    // 无别名
    {
        $$ = std::make_pair($1, "");  // 无别名时，别名为空字符串
    }
    |   tbName optAlias           // 有别名
    {
        $$ = std::make_pair($1, $2);
    }
    ;

// 新增：可选别名规则
optAlias:
    AS IDENTIFIER             // 显式使用 AS 关键字
    {
        $$ = $2;
    }
    |   IDENTIFIER                // 隐式别名（无 AS）
    {
        $$ = $1;
    }
    ;

opt_order_clause:
    ORDER BY order_clause      
    { 
        $$ = $3; 
    }
    |   /* epsilon */ { /* ignore*/ }
    ;

order_clause:
      col  opt_asc_desc 
    { 
        auto order = std::make_shared<OrderBy>($1, $2);
        $$ = std::vector<std::shared_ptr<OrderBy>>{order};
    }
    | order_clause ',' col opt_asc_desc
    {
        auto next_order = std::make_shared<OrderBy>($3, $4);
        $$.push_back(next_order);
    }
    ;   

opt_asc_desc:
    ASC          { $$ = OrderBy_ASC;     }
    |  DESC      { $$ = OrderBy_DESC;    }
    |       { $$ = OrderBy_DEFAULT; }
    ;

opt_limit:
    LIMIT VALUE_INT  { $$ = $2; }
    |   /* epsilon */ { $$ = -1; }  // -1表示没有LIMIT
    ;

set_knob_type:
    ENABLE_NESTLOOP { $$ = EnableNestLoop; }
    |   ENABLE_SORTMERGE { $$ = EnableSortMerge; }
    ;

tbName: IDENTIFIER;

colName: IDENTIFIER;
%%
