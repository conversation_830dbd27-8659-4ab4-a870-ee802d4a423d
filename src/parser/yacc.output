Terminals unused in grammar

    ORDER_BY


State 121 conflicts: 1 shift/reduce


Grammar

    0 $accept: start $end

    1 start: stmt ';'
    2      | HELP
    3      | EXIT
    4      | T_EOF

    5 stmt: dbStmt
    6     | ddl
    7     | dml
    8     | txnStmt
    9     | setStmt
   10     | explainStmt

   11 txnStmt: TXN_BEGIN
   12        | TXN_COMMIT
   13        | TXN_ABORT
   14        | TXN_ROLLBACK

   15 dbStmt: SHOW TABLES

   16 setStmt: SET set_knob_type '=' VALUE_BOOL

   17 ddl: CREATE TABLE tbName '(' fieldList ')'
   18    | DROP TABLE tbName
   19    | DESC tbName
   20    | CREATE INDEX tbName '(' colNameList ')'
   21    | DROP INDEX tbName '(' colNameList ')'

   22 dml: INSERT INTO tbName VALUES '(' valueList ')'
   23    | DELETE FROM tbName optWhereClause
   24    | UPDATE tbName SET setClauses optWhereClause
   25    | SELECT selector FROM tableExpression optWhereClause opt_order_clause

   26 explainStmt: EXPLAIN dml
   27            | EXPLAIN ddl

   28 fieldList: field
   29          | fieldList ',' field

   30 colNameList: colName
   31            | colNameList ',' colName

   32 field: colName type

   33 type: INT
   34     | CHAR '(' VALUE_INT ')'
   35     | FLOAT

   36 valueList: value
   37          | valueList ',' value

   38 value: VALUE_INT
   39      | VALUE_FLOAT
   40      | VALUE_STRING
   41      | VALUE_BOOL

   42 condition: col op expr

   43 optWhereClause: ε
   44               | WHERE whereClause

   45 whereClause: condition
   46            | whereClause AND condition

   47 col: tbName '.' colName
   48    | colName

   49 colList: col
   50        | colList ',' col

   51 op: '='
   52   | '<'
   53   | '>'
   54   | NEQ
   55   | LEQ
   56   | GEQ

   57 expr: value
   58     | col

   59 setClauses: setClause
   60           | setClauses ',' setClause

   61 setClause: colName '=' value

   62 selector: '*'
   63         | colList

   64 tableExpression: tableRef
   65                | tableExpression ',' tableRef
   66                | tableExpression JOIN tableRef ON whereClause
   67                | tableExpression JOIN tableRef optWhereClause

   68 tableRef: tbName
   69         | tbName optAlias

   70 optAlias: AS IDENTIFIER
   71         | IDENTIFIER

   72 opt_order_clause: ORDER BY order_clause
   73                 | ε

   74 order_clause: col opt_asc_desc
   75             | order_clause ',' col opt_asc_desc

   76 opt_asc_desc: ASC
   77             | DESC
   78             | ε

   79 set_knob_type: ENABLE_NESTLOOP
   80              | ENABLE_SORTMERGE

   81 tbName: IDENTIFIER

   82 colName: IDENTIFIER


Terminals, with rules where they appear

    $end (0) 0
    '(' (40) 17 20 21 22 34
    ')' (41) 17 20 21 22 34
    '*' (42) 62
    ',' (44) 29 31 37 50 60 65 75
    '.' (46) 47
    ';' (59) 1
    '<' (60) 52
    '=' (61) 16 51 61
    '>' (62) 53
    error (256)
    SHOW (258) 15
    TABLES (259) 15
    CREATE (260) 17 20
    TABLE (261) 17 18
    DROP (262) 18 21
    DESC (263) 19 77
    INSERT (264) 22
    INTO (265) 22
    VALUES (266) 22
    DELETE (267) 23
    FROM (268) 23 25
    ASC (269) 76
    EXPLAIN (270) 26 27
    ORDER (271) 72
    BY (272) 72
    AS (273) 70
    ON (274) 66
    WHERE (275) 44
    UPDATE (276) 24
    SET (277) 16 24
    SELECT (278) 25
    INT (279) 33
    CHAR (280) 34
    FLOAT (281) 35
    INDEX (282) 20 21
    AND (283) 46
    JOIN (284) 66 67
    EXIT (285) 3
    HELP (286) 2
    TXN_BEGIN (287) 11
    TXN_COMMIT (288) 12
    TXN_ABORT (289) 13
    TXN_ROLLBACK (290) 14
    ORDER_BY (291)
    ENABLE_NESTLOOP (292) 79
    ENABLE_SORTMERGE (293) 80
    LEQ (294) 55
    NEQ (295) 54
    GEQ (296) 56
    T_EOF (297) 4
    IDENTIFIER <sv_str> (298) 70 71 81 82
    VALUE_STRING <sv_str> (299) 40
    VALUE_INT <sv_int> (300) 34 38
    VALUE_FLOAT <sv_float> (301) 39
    VALUE_BOOL <sv_bool> (302) 16 41


Nonterminals, with rules where they appear

    $accept (57)
        on left: 0
    start (58)
        on left: 1 2 3 4
        on right: 0
    stmt <sv_node> (59)
        on left: 5 6 7 8 9 10
        on right: 1
    txnStmt <sv_node> (60)
        on left: 11 12 13 14
        on right: 8
    dbStmt <sv_node> (61)
        on left: 15
        on right: 5
    setStmt <sv_node> (62)
        on left: 16
        on right: 9
    ddl <sv_node> (63)
        on left: 17 18 19 20 21
        on right: 6 27
    dml <sv_node> (64)
        on left: 22 23 24 25
        on right: 7 26
    explainStmt <sv_node> (65)
        on left: 26 27
        on right: 10
    fieldList <sv_fields> (66)
        on left: 28 29
        on right: 17 29
    colNameList <sv_strs> (67)
        on left: 30 31
        on right: 20 21 31
    field <sv_field> (68)
        on left: 32
        on right: 28 29
    type <sv_type_len> (69)
        on left: 33 34 35
        on right: 32
    valueList <sv_vals> (70)
        on left: 36 37
        on right: 22 37
    value <sv_val> (71)
        on left: 38 39 40 41
        on right: 36 37 57 61
    condition <sv_cond> (72)
        on left: 42
        on right: 45 46
    optWhereClause <sv_conds> (73)
        on left: 43 44
        on right: 23 24 25 67
    whereClause <sv_conds> (74)
        on left: 45 46
        on right: 44 46 66
    col <sv_col> (75)
        on left: 47 48
        on right: 42 49 50 58 74 75
    colList <sv_cols> (76)
        on left: 49 50
        on right: 50 63
    op <sv_comp_op> (77)
        on left: 51 52 53 54 55 56
        on right: 42
    expr <sv_expr> (78)
        on left: 57 58
        on right: 42
    setClauses <sv_set_clauses> (79)
        on left: 59 60
        on right: 24 60
    setClause <sv_set_clause> (80)
        on left: 61
        on right: 59 60
    selector <sv_cols> (81)
        on left: 62 63
        on right: 25
    tableExpression <sv_table_expr> (82)
        on left: 64 65 66 67
        on right: 25 65 66 67
    tableRef <sv_strpair> (83)
        on left: 68 69
        on right: 64 65 66 67
    optAlias <sv_str> (84)
        on left: 70 71
        on right: 69
    opt_order_clause <sv_orderby> (85)
        on left: 72 73
        on right: 25
    order_clause <sv_orderby> (86)
        on left: 74 75
        on right: 72 75
    opt_asc_desc <sv_orderby_dir> (87)
        on left: 76 77 78
        on right: 74 75
    set_knob_type <sv_setKnobType> (88)
        on left: 79 80
        on right: 16
    tbName <sv_str> (89)
        on left: 81
        on right: 17 18 19 20 21 22 23 24 47 68 69
    colName <sv_str> (90)
        on left: 82
        on right: 30 31 32 47 48 61


State 0

    0 $accept: • start $end

    SHOW          shift, and go to state 1
    CREATE        shift, and go to state 2
    DROP          shift, and go to state 3
    DESC          shift, and go to state 4
    INSERT        shift, and go to state 5
    DELETE        shift, and go to state 6
    EXPLAIN       shift, and go to state 7
    UPDATE        shift, and go to state 8
    SET           shift, and go to state 9
    SELECT        shift, and go to state 10
    EXIT          shift, and go to state 11
    HELP          shift, and go to state 12
    TXN_BEGIN     shift, and go to state 13
    TXN_COMMIT    shift, and go to state 14
    TXN_ABORT     shift, and go to state 15
    TXN_ROLLBACK  shift, and go to state 16
    T_EOF         shift, and go to state 17

    start        go to state 18
    stmt         go to state 19
    txnStmt      go to state 20
    dbStmt       go to state 21
    setStmt      go to state 22
    ddl          go to state 23
    dml          go to state 24
    explainStmt  go to state 25


State 1

   15 dbStmt: SHOW • TABLES

    TABLES  shift, and go to state 26


State 2

   17 ddl: CREATE • TABLE tbName '(' fieldList ')'
   20    | CREATE • INDEX tbName '(' colNameList ')'

    TABLE  shift, and go to state 27
    INDEX  shift, and go to state 28


State 3

   18 ddl: DROP • TABLE tbName
   21    | DROP • INDEX tbName '(' colNameList ')'

    TABLE  shift, and go to state 29
    INDEX  shift, and go to state 30


State 4

   19 ddl: DESC • tbName

    IDENTIFIER  shift, and go to state 31

    tbName  go to state 32


State 5

   22 dml: INSERT • INTO tbName VALUES '(' valueList ')'

    INTO  shift, and go to state 33


State 6

   23 dml: DELETE • FROM tbName optWhereClause

    FROM  shift, and go to state 34


State 7

   26 explainStmt: EXPLAIN • dml
   27            | EXPLAIN • ddl

    CREATE  shift, and go to state 2
    DROP    shift, and go to state 3
    DESC    shift, and go to state 4
    INSERT  shift, and go to state 5
    DELETE  shift, and go to state 6
    UPDATE  shift, and go to state 8
    SELECT  shift, and go to state 10

    ddl  go to state 35
    dml  go to state 36


State 8

   24 dml: UPDATE • tbName SET setClauses optWhereClause

    IDENTIFIER  shift, and go to state 31

    tbName  go to state 37


State 9

   16 setStmt: SET • set_knob_type '=' VALUE_BOOL

    ENABLE_NESTLOOP   shift, and go to state 38
    ENABLE_SORTMERGE  shift, and go to state 39

    set_knob_type  go to state 40


State 10

   25 dml: SELECT • selector FROM tableExpression optWhereClause opt_order_clause

    IDENTIFIER  shift, and go to state 41
    '*'         shift, and go to state 42

    col       go to state 43
    colList   go to state 44
    selector  go to state 45
    tbName    go to state 46
    colName   go to state 47


State 11

    3 start: EXIT •

    $default  reduce using rule 3 (start)


State 12

    2 start: HELP •

    $default  reduce using rule 2 (start)


State 13

   11 txnStmt: TXN_BEGIN •

    $default  reduce using rule 11 (txnStmt)


State 14

   12 txnStmt: TXN_COMMIT •

    $default  reduce using rule 12 (txnStmt)


State 15

   13 txnStmt: TXN_ABORT •

    $default  reduce using rule 13 (txnStmt)


State 16

   14 txnStmt: TXN_ROLLBACK •

    $default  reduce using rule 14 (txnStmt)


State 17

    4 start: T_EOF •

    $default  reduce using rule 4 (start)


State 18

    0 $accept: start • $end

    $end  shift, and go to state 48


State 19

    1 start: stmt • ';'

    ';'  shift, and go to state 49


State 20

    8 stmt: txnStmt •

    $default  reduce using rule 8 (stmt)


State 21

    5 stmt: dbStmt •

    $default  reduce using rule 5 (stmt)


State 22

    9 stmt: setStmt •

    $default  reduce using rule 9 (stmt)


State 23

    6 stmt: ddl •

    $default  reduce using rule 6 (stmt)


State 24

    7 stmt: dml •

    $default  reduce using rule 7 (stmt)


State 25

   10 stmt: explainStmt •

    $default  reduce using rule 10 (stmt)


State 26

   15 dbStmt: SHOW TABLES •

    $default  reduce using rule 15 (dbStmt)


State 27

   17 ddl: CREATE TABLE • tbName '(' fieldList ')'

    IDENTIFIER  shift, and go to state 31

    tbName  go to state 50


State 28

   20 ddl: CREATE INDEX • tbName '(' colNameList ')'

    IDENTIFIER  shift, and go to state 31

    tbName  go to state 51


State 29

   18 ddl: DROP TABLE • tbName

    IDENTIFIER  shift, and go to state 31

    tbName  go to state 52


State 30

   21 ddl: DROP INDEX • tbName '(' colNameList ')'

    IDENTIFIER  shift, and go to state 31

    tbName  go to state 53


State 31

   81 tbName: IDENTIFIER •

    $default  reduce using rule 81 (tbName)


State 32

   19 ddl: DESC tbName •

    $default  reduce using rule 19 (ddl)


State 33

   22 dml: INSERT INTO • tbName VALUES '(' valueList ')'

    IDENTIFIER  shift, and go to state 31

    tbName  go to state 54


State 34

   23 dml: DELETE FROM • tbName optWhereClause

    IDENTIFIER  shift, and go to state 31

    tbName  go to state 55


State 35

   27 explainStmt: EXPLAIN ddl •

    $default  reduce using rule 27 (explainStmt)


State 36

   26 explainStmt: EXPLAIN dml •

    $default  reduce using rule 26 (explainStmt)


State 37

   24 dml: UPDATE tbName • SET setClauses optWhereClause

    SET  shift, and go to state 56


State 38

   79 set_knob_type: ENABLE_NESTLOOP •

    $default  reduce using rule 79 (set_knob_type)


State 39

   80 set_knob_type: ENABLE_SORTMERGE •

    $default  reduce using rule 80 (set_knob_type)


State 40

   16 setStmt: SET set_knob_type • '=' VALUE_BOOL

    '='  shift, and go to state 57


State 41

   81 tbName: IDENTIFIER •
   82 colName: IDENTIFIER •

    '.'       reduce using rule 81 (tbName)
    $default  reduce using rule 82 (colName)


State 42

   62 selector: '*' •

    $default  reduce using rule 62 (selector)


State 43

   49 colList: col •

    $default  reduce using rule 49 (colList)


State 44

   50 colList: colList • ',' col
   63 selector: colList •

    ','  shift, and go to state 58

    $default  reduce using rule 63 (selector)


State 45

   25 dml: SELECT selector • FROM tableExpression optWhereClause opt_order_clause

    FROM  shift, and go to state 59


State 46

   47 col: tbName • '.' colName

    '.'  shift, and go to state 60


State 47

   48 col: colName •

    $default  reduce using rule 48 (col)


State 48

    0 $accept: start $end •

    $default  accept


State 49

    1 start: stmt ';' •

    $default  reduce using rule 1 (start)


State 50

   17 ddl: CREATE TABLE tbName • '(' fieldList ')'

    '('  shift, and go to state 61


State 51

   20 ddl: CREATE INDEX tbName • '(' colNameList ')'

    '('  shift, and go to state 62


State 52

   18 ddl: DROP TABLE tbName •

    $default  reduce using rule 18 (ddl)


State 53

   21 ddl: DROP INDEX tbName • '(' colNameList ')'

    '('  shift, and go to state 63


State 54

   22 dml: INSERT INTO tbName • VALUES '(' valueList ')'

    VALUES  shift, and go to state 64


State 55

   23 dml: DELETE FROM tbName • optWhereClause

    WHERE  shift, and go to state 65

    $default  reduce using rule 43 (optWhereClause)

    optWhereClause  go to state 66


State 56

   24 dml: UPDATE tbName SET • setClauses optWhereClause

    IDENTIFIER  shift, and go to state 67

    setClauses  go to state 68
    setClause   go to state 69
    colName     go to state 70


State 57

   16 setStmt: SET set_knob_type '=' • VALUE_BOOL

    VALUE_BOOL  shift, and go to state 71


State 58

   50 colList: colList ',' • col

    IDENTIFIER  shift, and go to state 41

    col      go to state 72
    tbName   go to state 46
    colName  go to state 47


State 59

   25 dml: SELECT selector FROM • tableExpression optWhereClause opt_order_clause

    IDENTIFIER  shift, and go to state 31

    tableExpression  go to state 73
    tableRef         go to state 74
    tbName           go to state 75


State 60

   47 col: tbName '.' • colName

    IDENTIFIER  shift, and go to state 67

    colName  go to state 76


State 61

   17 ddl: CREATE TABLE tbName '(' • fieldList ')'

    IDENTIFIER  shift, and go to state 67

    fieldList  go to state 77
    field      go to state 78
    colName    go to state 79


State 62

   20 ddl: CREATE INDEX tbName '(' • colNameList ')'

    IDENTIFIER  shift, and go to state 67

    colNameList  go to state 80
    colName      go to state 81


State 63

   21 ddl: DROP INDEX tbName '(' • colNameList ')'

    IDENTIFIER  shift, and go to state 67

    colNameList  go to state 82
    colName      go to state 81


State 64

   22 dml: INSERT INTO tbName VALUES • '(' valueList ')'

    '('  shift, and go to state 83


State 65

   44 optWhereClause: WHERE • whereClause

    IDENTIFIER  shift, and go to state 41

    condition    go to state 84
    whereClause  go to state 85
    col          go to state 86
    tbName       go to state 46
    colName      go to state 47


State 66

   23 dml: DELETE FROM tbName optWhereClause •

    $default  reduce using rule 23 (dml)


State 67

   82 colName: IDENTIFIER •

    $default  reduce using rule 82 (colName)


State 68

   24 dml: UPDATE tbName SET setClauses • optWhereClause
   60 setClauses: setClauses • ',' setClause

    WHERE  shift, and go to state 65
    ','    shift, and go to state 87

    $default  reduce using rule 43 (optWhereClause)

    optWhereClause  go to state 88


State 69

   59 setClauses: setClause •

    $default  reduce using rule 59 (setClauses)


State 70

   61 setClause: colName • '=' value

    '='  shift, and go to state 89


State 71

   16 setStmt: SET set_knob_type '=' VALUE_BOOL •

    $default  reduce using rule 16 (setStmt)


State 72

   50 colList: colList ',' col •

    $default  reduce using rule 50 (colList)


State 73

   25 dml: SELECT selector FROM tableExpression • optWhereClause opt_order_clause
   65 tableExpression: tableExpression • ',' tableRef
   66                | tableExpression • JOIN tableRef ON whereClause
   67                | tableExpression • JOIN tableRef optWhereClause

    WHERE  shift, and go to state 65
    JOIN   shift, and go to state 90
    ','    shift, and go to state 91

    $default  reduce using rule 43 (optWhereClause)

    optWhereClause  go to state 92


State 74

   64 tableExpression: tableRef •

    $default  reduce using rule 64 (tableExpression)


State 75

   68 tableRef: tbName •
   69         | tbName • optAlias

    AS          shift, and go to state 93
    IDENTIFIER  shift, and go to state 94

    $default  reduce using rule 68 (tableRef)

    optAlias  go to state 95


State 76

   47 col: tbName '.' colName •

    $default  reduce using rule 47 (col)


State 77

   17 ddl: CREATE TABLE tbName '(' fieldList • ')'
   29 fieldList: fieldList • ',' field

    ')'  shift, and go to state 96
    ','  shift, and go to state 97


State 78

   28 fieldList: field •

    $default  reduce using rule 28 (fieldList)


State 79

   32 field: colName • type

    INT    shift, and go to state 98
    CHAR   shift, and go to state 99
    FLOAT  shift, and go to state 100

    type  go to state 101


State 80

   20 ddl: CREATE INDEX tbName '(' colNameList • ')'
   31 colNameList: colNameList • ',' colName

    ')'  shift, and go to state 102
    ','  shift, and go to state 103


State 81

   30 colNameList: colName •

    $default  reduce using rule 30 (colNameList)


State 82

   21 ddl: DROP INDEX tbName '(' colNameList • ')'
   31 colNameList: colNameList • ',' colName

    ')'  shift, and go to state 104
    ','  shift, and go to state 103


State 83

   22 dml: INSERT INTO tbName VALUES '(' • valueList ')'

    VALUE_STRING  shift, and go to state 105
    VALUE_INT     shift, and go to state 106
    VALUE_FLOAT   shift, and go to state 107
    VALUE_BOOL    shift, and go to state 108

    valueList  go to state 109
    value      go to state 110


State 84

   45 whereClause: condition •

    $default  reduce using rule 45 (whereClause)


State 85

   44 optWhereClause: WHERE whereClause •
   46 whereClause: whereClause • AND condition

    AND  shift, and go to state 111

    $default  reduce using rule 44 (optWhereClause)


State 86

   42 condition: col • op expr

    LEQ  shift, and go to state 112
    NEQ  shift, and go to state 113
    GEQ  shift, and go to state 114
    '='  shift, and go to state 115
    '<'  shift, and go to state 116
    '>'  shift, and go to state 117

    op  go to state 118


State 87

   60 setClauses: setClauses ',' • setClause

    IDENTIFIER  shift, and go to state 67

    setClause  go to state 119
    colName    go to state 70


State 88

   24 dml: UPDATE tbName SET setClauses optWhereClause •

    $default  reduce using rule 24 (dml)


State 89

   61 setClause: colName '=' • value

    VALUE_STRING  shift, and go to state 105
    VALUE_INT     shift, and go to state 106
    VALUE_FLOAT   shift, and go to state 107
    VALUE_BOOL    shift, and go to state 108

    value  go to state 120


State 90

   66 tableExpression: tableExpression JOIN • tableRef ON whereClause
   67                | tableExpression JOIN • tableRef optWhereClause

    IDENTIFIER  shift, and go to state 31

    tableRef  go to state 121
    tbName    go to state 75


State 91

   65 tableExpression: tableExpression ',' • tableRef

    IDENTIFIER  shift, and go to state 31

    tableRef  go to state 122
    tbName    go to state 75


State 92

   25 dml: SELECT selector FROM tableExpression optWhereClause • opt_order_clause

    ORDER  shift, and go to state 123

    $default  reduce using rule 73 (opt_order_clause)

    opt_order_clause  go to state 124


State 93

   70 optAlias: AS • IDENTIFIER

    IDENTIFIER  shift, and go to state 125


State 94

   71 optAlias: IDENTIFIER •

    $default  reduce using rule 71 (optAlias)


State 95

   69 tableRef: tbName optAlias •

    $default  reduce using rule 69 (tableRef)


State 96

   17 ddl: CREATE TABLE tbName '(' fieldList ')' •

    $default  reduce using rule 17 (ddl)


State 97

   29 fieldList: fieldList ',' • field

    IDENTIFIER  shift, and go to state 67

    field    go to state 126
    colName  go to state 79


State 98

   33 type: INT •

    $default  reduce using rule 33 (type)


State 99

   34 type: CHAR • '(' VALUE_INT ')'

    '('  shift, and go to state 127


State 100

   35 type: FLOAT •

    $default  reduce using rule 35 (type)


State 101

   32 field: colName type •

    $default  reduce using rule 32 (field)


State 102

   20 ddl: CREATE INDEX tbName '(' colNameList ')' •

    $default  reduce using rule 20 (ddl)


State 103

   31 colNameList: colNameList ',' • colName

    IDENTIFIER  shift, and go to state 67

    colName  go to state 128


State 104

   21 ddl: DROP INDEX tbName '(' colNameList ')' •

    $default  reduce using rule 21 (ddl)


State 105

   40 value: VALUE_STRING •

    $default  reduce using rule 40 (value)


State 106

   38 value: VALUE_INT •

    $default  reduce using rule 38 (value)


State 107

   39 value: VALUE_FLOAT •

    $default  reduce using rule 39 (value)


State 108

   41 value: VALUE_BOOL •

    $default  reduce using rule 41 (value)


State 109

   22 dml: INSERT INTO tbName VALUES '(' valueList • ')'
   37 valueList: valueList • ',' value

    ')'  shift, and go to state 129
    ','  shift, and go to state 130


State 110

   36 valueList: value •

    $default  reduce using rule 36 (valueList)


State 111

   46 whereClause: whereClause AND • condition

    IDENTIFIER  shift, and go to state 41

    condition  go to state 131
    col        go to state 86
    tbName     go to state 46
    colName    go to state 47


State 112

   55 op: LEQ •

    $default  reduce using rule 55 (op)


State 113

   54 op: NEQ •

    $default  reduce using rule 54 (op)


State 114

   56 op: GEQ •

    $default  reduce using rule 56 (op)


State 115

   51 op: '=' •

    $default  reduce using rule 51 (op)


State 116

   52 op: '<' •

    $default  reduce using rule 52 (op)


State 117

   53 op: '>' •

    $default  reduce using rule 53 (op)


State 118

   42 condition: col op • expr

    IDENTIFIER    shift, and go to state 41
    VALUE_STRING  shift, and go to state 105
    VALUE_INT     shift, and go to state 106
    VALUE_FLOAT   shift, and go to state 107
    VALUE_BOOL    shift, and go to state 108

    value    go to state 132
    col      go to state 133
    expr     go to state 134
    tbName   go to state 46
    colName  go to state 47


State 119

   60 setClauses: setClauses ',' setClause •

    $default  reduce using rule 60 (setClauses)


State 120

   61 setClause: colName '=' value •

    $default  reduce using rule 61 (setClause)


State 121

   66 tableExpression: tableExpression JOIN tableRef • ON whereClause
   67                | tableExpression JOIN tableRef • optWhereClause

    ON     shift, and go to state 135
    WHERE  shift, and go to state 65

    WHERE     [reduce using rule 43 (optWhereClause)]
    $default  reduce using rule 43 (optWhereClause)

    optWhereClause  go to state 136


State 122

   65 tableExpression: tableExpression ',' tableRef •

    $default  reduce using rule 65 (tableExpression)


State 123

   72 opt_order_clause: ORDER • BY order_clause

    BY  shift, and go to state 137


State 124

   25 dml: SELECT selector FROM tableExpression optWhereClause opt_order_clause •

    $default  reduce using rule 25 (dml)


State 125

   70 optAlias: AS IDENTIFIER •

    $default  reduce using rule 70 (optAlias)


State 126

   29 fieldList: fieldList ',' field •

    $default  reduce using rule 29 (fieldList)


State 127

   34 type: CHAR '(' • VALUE_INT ')'

    VALUE_INT  shift, and go to state 138


State 128

   31 colNameList: colNameList ',' colName •

    $default  reduce using rule 31 (colNameList)


State 129

   22 dml: INSERT INTO tbName VALUES '(' valueList ')' •

    $default  reduce using rule 22 (dml)


State 130

   37 valueList: valueList ',' • value

    VALUE_STRING  shift, and go to state 105
    VALUE_INT     shift, and go to state 106
    VALUE_FLOAT   shift, and go to state 107
    VALUE_BOOL    shift, and go to state 108

    value  go to state 139


State 131

   46 whereClause: whereClause AND condition •

    $default  reduce using rule 46 (whereClause)


State 132

   57 expr: value •

    $default  reduce using rule 57 (expr)


State 133

   58 expr: col •

    $default  reduce using rule 58 (expr)


State 134

   42 condition: col op expr •

    $default  reduce using rule 42 (condition)


State 135

   66 tableExpression: tableExpression JOIN tableRef ON • whereClause

    IDENTIFIER  shift, and go to state 41

    condition    go to state 84
    whereClause  go to state 140
    col          go to state 86
    tbName       go to state 46
    colName      go to state 47


State 136

   67 tableExpression: tableExpression JOIN tableRef optWhereClause •

    $default  reduce using rule 67 (tableExpression)


State 137

   72 opt_order_clause: ORDER BY • order_clause

    IDENTIFIER  shift, and go to state 41

    col           go to state 141
    order_clause  go to state 142
    tbName        go to state 46
    colName       go to state 47


State 138

   34 type: CHAR '(' VALUE_INT • ')'

    ')'  shift, and go to state 143


State 139

   37 valueList: valueList ',' value •

    $default  reduce using rule 37 (valueList)


State 140

   46 whereClause: whereClause • AND condition
   66 tableExpression: tableExpression JOIN tableRef ON whereClause •

    AND  shift, and go to state 111

    $default  reduce using rule 66 (tableExpression)


State 141

   74 order_clause: col • opt_asc_desc

    DESC  shift, and go to state 144
    ASC   shift, and go to state 145

    $default  reduce using rule 78 (opt_asc_desc)

    opt_asc_desc  go to state 146


State 142

   72 opt_order_clause: ORDER BY order_clause •
   75 order_clause: order_clause • ',' col opt_asc_desc

    ','  shift, and go to state 147

    $default  reduce using rule 72 (opt_order_clause)


State 143

   34 type: CHAR '(' VALUE_INT ')' •

    $default  reduce using rule 34 (type)


State 144

   77 opt_asc_desc: DESC •

    $default  reduce using rule 77 (opt_asc_desc)


State 145

   76 opt_asc_desc: ASC •

    $default  reduce using rule 76 (opt_asc_desc)


State 146

   74 order_clause: col opt_asc_desc •

    $default  reduce using rule 74 (order_clause)


State 147

   75 order_clause: order_clause ',' • col opt_asc_desc

    IDENTIFIER  shift, and go to state 41

    col      go to state 148
    tbName   go to state 46
    colName  go to state 47


State 148

   75 order_clause: order_clause ',' col • opt_asc_desc

    DESC  shift, and go to state 144
    ASC   shift, and go to state 145

    $default  reduce using rule 78 (opt_asc_desc)

    opt_asc_desc  go to state 149


State 149

   75 order_clause: order_clause ',' col opt_asc_desc •

    $default  reduce using rule 75 (order_clause)
Terminals unused in grammar

    ORDER_BY


Grammar

    0 $accept: start $end

    1 start: stmt ';'
    2      | HELP
    3      | EXIT
    4      | T_EOF

    5 stmt: dbStmt
    6     | ddl
    7     | dml
    8     | txnStmt
    9     | setStmt

   10 txnStmt: TXN_BEGIN
   11        | TXN_COMMIT
   12        | TXN_ABORT
   13        | TXN_ROLLBACK

   14 dbStmt: SHOW TABLES

   15 setStmt: SET set_knob_type '=' VALUE_BOOL

   16 ddl: CREATE TABLE tbName '(' fieldList ')'
   17    | DROP TABLE tbName
   18    | DESC tbName
   19    | CREATE INDEX tbName '(' colNameList ')'
   20    | DROP INDEX tbName '(' colNameList ')'

   21 dml: INSERT INTO tbName VALUES '(' valueList ')'
   22    | DELETE FROM tbName optWhereClause
   23    | UPDATE tbName SET setClauses optWhereClause
   24    | SELECT selector FROM tableList optWhereClause opt_group_by opt_having opt_order_clause

   25 fieldList: field
   26          | fieldList ',' field

   27 colNameList: colName
   28            | colNameList ',' colName

   29 field: colName type

   30 type: INT
   31     | CHAR '(' VALUE_INT ')'
   32     | FLOAT

   33 valueList: value
   34          | valueList ',' value

   35 value: VALUE_INT
   36      | VALUE_FLOAT
   37      | VALUE_STRING
   38      | VALUE_BOOL

   39 condition: col op expr

   40 optWhereClause: %empty
   41               | WHERE whereClause

   42 whereClause: condition
   43            | whereClause AND condition

   44 opt_group_by: GROUP BY colList
   45             | %empty

   46 opt_having: HAVING havingClause
   47           | %empty

   48 havingClause: havingCondition
   49             | havingClause AND havingCondition

   50 havingCondition: aggExpr op expr
   51                | col op expr
   52                | col op aggExpr

   53 aggExpr: COUNT '(' '*' ')'
   54        | COUNT '(' col ')'
   55        | SUM '(' col ')'
   56        | AVG '(' col ')'
   57        | MAX '(' col ')'
   58        | MIN '(' col ')'

   59 col: tbName '.' colName
   60    | colName

   61 colList: col
   62        | colList ',' col

   63 op: '='
   64   | '<'
   65   | '>'
   66   | NEQ
   67   | LEQ
   68   | GEQ

   69 expr: value
   70     | col

   71 setClauses: setClause
   72           | setClauses ',' setClause

   73 setClause: colName '=' value

   74 selector: '*'
   75         | select_item
   76         | selector ',' select_item

   77 select_item: col
   78            | aggExpr opt_alias

   79 opt_alias: %empty
   80          | AS IDENTIFIER
   81          | IDENTIFIER

   82 tableList: tbName
   83          | tableList ',' tbName
   84          | tableList JOIN tbName

   85 opt_order_clause: ORDER BY order_clause
   86                 | %empty

   87 order_clause: col opt_asc_desc
   88             | order_clause ',' col opt_asc_desc

   89 opt_asc_desc: ASC
   90             | DESC
   91             | %empty

   92 set_knob_type: ENABLE_NESTLOOP
   93              | ENABLE_SORTMERGE

   94 tbName: IDENTIFIER

   95 colName: IDENTIFIER


Terminals, with rules where they appear

    $end (0) 0
    '(' (40) 16 19 20 21 31 53 54 55 56 57 58
    ')' (41) 16 19 20 21 31 53 54 55 56 57 58
    '*' (42) 53 74
    ',' (44) 26 28 34 62 72 76 83 88
    '.' (46) 59
    ';' (59) 1
    '<' (60) 64
    '=' (61) 15 63 73
    '>' (62) 65
    error (256)
    SHOW (258) 14
    TABLES (259) 14
    CREATE (260) 16 19
    TABLE (261) 16 17
    DROP (262) 17 20
    DESC (263) 18 90
    INSERT (264) 21
    INTO (265) 21
    VALUES (266) 21
    DELETE (267) 22
    FROM (268) 22 24
    ASC (269) 89
    ORDER (270) 85
    BY (271) 44 85
    COUNT (272) 53 54
    MIN (273) 58
    MAX (274) 57
    AVG (275) 56
    SUM (276) 55
    GROUP (277) 44
    HAVING (278) 46
    AS (279) 80
    WHERE (280) 41
    UPDATE (281) 23
    SET (282) 15 23
    SELECT (283) 24
    INT (284) 30
    CHAR (285) 31
    FLOAT (286) 32
    INDEX (287) 19 20
    AND (288) 43 49
    JOIN (289) 84
    EXIT (290) 3
    HELP (291) 2
    TXN_BEGIN (292) 10
    TXN_COMMIT (293) 11
    TXN_ABORT (294) 12
    TXN_ROLLBACK (295) 13
    ORDER_BY (296)
    ENABLE_NESTLOOP (297) 92
    ENABLE_SORTMERGE (298) 93
    LEQ (299) 67
    NEQ (300) 66
    GEQ (301) 68
    T_EOF (302) 4
    IDENTIFIER <sv_str> (303) 80 81 94 95
    VALUE_STRING <sv_str> (304) 37
    VALUE_INT <sv_int> (305) 31 35
    VALUE_FLOAT <sv_float> (306) 36
    VALUE_BOOL <sv_bool> (307) 15 38


Nonterminals, with rules where they appear

    $accept (62)
        on left: 0
    start (63)
        on left: 1 2 3 4
        on right: 0
    stmt <sv_node> (64)
        on left: 5 6 7 8 9
        on right: 1
    txnStmt <sv_node> (65)
        on left: 10 11 12 13
        on right: 8
    dbStmt <sv_node> (66)
        on left: 14
        on right: 5
    setStmt <sv_node> (67)
        on left: 15
        on right: 9
    ddl <sv_node> (68)
        on left: 16 17 18 19 20
        on right: 6
    dml <sv_node> (69)
        on left: 21 22 23 24
        on right: 7
    fieldList <sv_fields> (70)
        on left: 25 26
        on right: 16 26
    colNameList <sv_strs> (71)
        on left: 27 28
        on right: 19 20 28
    field <sv_field> (72)
        on left: 29
        on right: 25 26
    type <sv_type_len> (73)
        on left: 30 31 32
        on right: 29
    valueList <sv_vals> (74)
        on left: 33 34
        on right: 21 34
    value <sv_val> (75)
        on left: 35 36 37 38
        on right: 33 34 69 73
    condition <sv_cond> (76)
        on left: 39
        on right: 42 43
    optWhereClause <sv_conds> (77)
        on left: 40 41
        on right: 22 23 24
    whereClause <sv_conds> (78)
        on left: 42 43
        on right: 41 43
    opt_group_by <sv_group_by> (79)
        on left: 44 45
        on right: 24
    opt_having <sv_having> (80)
        on left: 46 47
        on right: 24
    havingClause <sv_having> (81)
        on left: 48 49
        on right: 46 49
    havingCondition <sv_having_cond> (82)
        on left: 50 51 52
        on right: 48 49
    aggExpr <sv_agg> (83)
        on left: 53 54 55 56 57 58
        on right: 50 52 78
    col <sv_col> (84)
        on left: 59 60
        on right: 39 51 52 54 55 56 57 58 61 62 70 77 87 88
    colList <sv_cols> (85)
        on left: 61 62
        on right: 44 62
    op <sv_comp_op> (86)
        on left: 63 64 65 66 67 68
        on right: 39 50 51 52
    expr <sv_expr> (87)
        on left: 69 70
        on right: 39 50 51
    setClauses <sv_set_clauses> (88)
        on left: 71 72
        on right: 23 72
    setClause <sv_set_clause> (89)
        on left: 73
        on right: 71 72
    selector <sv_select_items> (90)
        on left: 74 75 76
        on right: 24 76
    select_item <sv_select_item> (91)
        on left: 77 78
        on right: 75 76
    opt_alias <sv_str> (92)
        on left: 79 80 81
        on right: 78
    tableList <sv_strs> (93)
        on left: 82 83 84
        on right: 24 83 84
    opt_order_clause <sv_orderby> (94)
        on left: 85 86
        on right: 24
    order_clause <sv_orderby> (95)
        on left: 87 88
        on right: 85 88
    opt_asc_desc <sv_orderby_dir> (96)
        on left: 89 90 91
        on right: 87 88
    set_knob_type <sv_setKnobType> (97)
        on left: 92 93
        on right: 15
    tbName <sv_str> (98)
        on left: 94
        on right: 16 17 18 19 20 21 22 23 59 82 83 84
    colName <sv_str> (99)
        on left: 95
        on right: 27 28 29 59 60 73


State 0

    0 $accept: . start $end

    SHOW          shift, and go to state 1
    CREATE        shift, and go to state 2
    DROP          shift, and go to state 3
    DESC          shift, and go to state 4
    INSERT        shift, and go to state 5
    DELETE        shift, and go to state 6
    UPDATE        shift, and go to state 7
    SET           shift, and go to state 8
    SELECT        shift, and go to state 9
    EXIT          shift, and go to state 10
    HELP          shift, and go to state 11
    TXN_BEGIN     shift, and go to state 12
    TXN_COMMIT    shift, and go to state 13
    TXN_ABORT     shift, and go to state 14
    TXN_ROLLBACK  shift, and go to state 15
    T_EOF         shift, and go to state 16

    start    go to state 17
    stmt     go to state 18
    txnStmt  go to state 19
    dbStmt   go to state 20
    setStmt  go to state 21
    ddl      go to state 22
    dml      go to state 23


State 1

   14 dbStmt: SHOW . TABLES

    TABLES  shift, and go to state 24


State 2

   16 ddl: CREATE . TABLE tbName '(' fieldList ')'
   19    | CREATE . INDEX tbName '(' colNameList ')'

    TABLE  shift, and go to state 25
    INDEX  shift, and go to state 26


State 3

   17 ddl: DROP . TABLE tbName
   20    | DROP . INDEX tbName '(' colNameList ')'

    TABLE  shift, and go to state 27
    INDEX  shift, and go to state 28


State 4

   18 ddl: DESC . tbName

    IDENTIFIER  shift, and go to state 29

    tbName  go to state 30


State 5

   21 dml: INSERT . INTO tbName VALUES '(' valueList ')'

    INTO  shift, and go to state 31


State 6

   22 dml: DELETE . FROM tbName optWhereClause

    FROM  shift, and go to state 32


State 7

   23 dml: UPDATE . tbName SET setClauses optWhereClause

    IDENTIFIER  shift, and go to state 29

    tbName  go to state 33


State 8

   15 setStmt: SET . set_knob_type '=' VALUE_BOOL

    ENABLE_NESTLOOP   shift, and go to state 34
    ENABLE_SORTMERGE  shift, and go to state 35

    set_knob_type  go to state 36


State 9

   24 dml: SELECT . selector FROM tableList optWhereClause opt_group_by opt_having opt_order_clause

    COUNT       shift, and go to state 37
    MIN         shift, and go to state 38
    MAX         shift, and go to state 39
    AVG         shift, and go to state 40
    SUM         shift, and go to state 41
    IDENTIFIER  shift, and go to state 42
    '*'         shift, and go to state 43

    aggExpr      go to state 44
    col          go to state 45
    selector     go to state 46
    select_item  go to state 47
    tbName       go to state 48
    colName      go to state 49


State 10

    3 start: EXIT .

    $default  reduce using rule 3 (start)


State 11

    2 start: HELP .

    $default  reduce using rule 2 (start)


State 12

   10 txnStmt: TXN_BEGIN .

    $default  reduce using rule 10 (txnStmt)


State 13

   11 txnStmt: TXN_COMMIT .

    $default  reduce using rule 11 (txnStmt)


State 14

   12 txnStmt: TXN_ABORT .

    $default  reduce using rule 12 (txnStmt)


State 15

   13 txnStmt: TXN_ROLLBACK .

    $default  reduce using rule 13 (txnStmt)


State 16

    4 start: T_EOF .

    $default  reduce using rule 4 (start)


State 17

    0 $accept: start . $end

    $end  shift, and go to state 50


State 18

    1 start: stmt . ';'

    ';'  shift, and go to state 51


State 19

    8 stmt: txnStmt .

    $default  reduce using rule 8 (stmt)


State 20

    5 stmt: dbStmt .

    $default  reduce using rule 5 (stmt)


State 21

    9 stmt: setStmt .

    $default  reduce using rule 9 (stmt)


State 22

    6 stmt: ddl .

    $default  reduce using rule 6 (stmt)


State 23

    7 stmt: dml .

    $default  reduce using rule 7 (stmt)


State 24

   14 dbStmt: SHOW TABLES .

    $default  reduce using rule 14 (dbStmt)


State 25

   16 ddl: CREATE TABLE . tbName '(' fieldList ')'

    IDENTIFIER  shift, and go to state 29

    tbName  go to state 52


State 26

   19 ddl: CREATE INDEX . tbName '(' colNameList ')'

    IDENTIFIER  shift, and go to state 29

    tbName  go to state 53


State 27

   17 ddl: DROP TABLE . tbName

    IDENTIFIER  shift, and go to state 29

    tbName  go to state 54


State 28

   20 ddl: DROP INDEX . tbName '(' colNameList ')'

    IDENTIFIER  shift, and go to state 29

    tbName  go to state 55


State 29

   94 tbName: IDENTIFIER .

    $default  reduce using rule 94 (tbName)


State 30

   18 ddl: DESC tbName .

    $default  reduce using rule 18 (ddl)


State 31

   21 dml: INSERT INTO . tbName VALUES '(' valueList ')'

    IDENTIFIER  shift, and go to state 29

    tbName  go to state 56


State 32

   22 dml: DELETE FROM . tbName optWhereClause

    IDENTIFIER  shift, and go to state 29

    tbName  go to state 57


State 33

   23 dml: UPDATE tbName . SET setClauses optWhereClause

    SET  shift, and go to state 58


State 34

   92 set_knob_type: ENABLE_NESTLOOP .

    $default  reduce using rule 92 (set_knob_type)


State 35

   93 set_knob_type: ENABLE_SORTMERGE .

    $default  reduce using rule 93 (set_knob_type)


State 36

   15 setStmt: SET set_knob_type . '=' VALUE_BOOL

    '='  shift, and go to state 59


State 37

   53 aggExpr: COUNT . '(' '*' ')'
   54        | COUNT . '(' col ')'

    '('  shift, and go to state 60


State 38

   58 aggExpr: MIN . '(' col ')'

    '('  shift, and go to state 61


State 39

   57 aggExpr: MAX . '(' col ')'

    '('  shift, and go to state 62


State 40

   56 aggExpr: AVG . '(' col ')'

    '('  shift, and go to state 63


State 41

   55 aggExpr: SUM . '(' col ')'

    '('  shift, and go to state 64


State 42

   94 tbName: IDENTIFIER .
   95 colName: IDENTIFIER .

    '.'       reduce using rule 94 (tbName)
    $default  reduce using rule 95 (colName)


State 43

   74 selector: '*' .

    $default  reduce using rule 74 (selector)


State 44

   78 select_item: aggExpr . opt_alias

    AS          shift, and go to state 65
    IDENTIFIER  shift, and go to state 66

    $default  reduce using rule 79 (opt_alias)

    opt_alias  go to state 67


State 45

   77 select_item: col .

    $default  reduce using rule 77 (select_item)


State 46

   24 dml: SELECT selector . FROM tableList optWhereClause opt_group_by opt_having opt_order_clause
   76 selector: selector . ',' select_item

    FROM  shift, and go to state 68
    ','   shift, and go to state 69


State 47

   75 selector: select_item .

    $default  reduce using rule 75 (selector)


State 48

   59 col: tbName . '.' colName

    '.'  shift, and go to state 70


State 49

   60 col: colName .

    $default  reduce using rule 60 (col)


State 50

    0 $accept: start $end .

    $default  accept


State 51

    1 start: stmt ';' .

    $default  reduce using rule 1 (start)


State 52

   16 ddl: CREATE TABLE tbName . '(' fieldList ')'

    '('  shift, and go to state 71


State 53

   19 ddl: CREATE INDEX tbName . '(' colNameList ')'

    '('  shift, and go to state 72


State 54

   17 ddl: DROP TABLE tbName .

    $default  reduce using rule 17 (ddl)


State 55

   20 ddl: DROP INDEX tbName . '(' colNameList ')'

    '('  shift, and go to state 73


State 56

   21 dml: INSERT INTO tbName . VALUES '(' valueList ')'

    VALUES  shift, and go to state 74


State 57

   22 dml: DELETE FROM tbName . optWhereClause

    WHERE  shift, and go to state 75

    $default  reduce using rule 40 (optWhereClause)

    optWhereClause  go to state 76


State 58

   23 dml: UPDATE tbName SET . setClauses optWhereClause

    IDENTIFIER  shift, and go to state 77

    setClauses  go to state 78
    setClause   go to state 79
    colName     go to state 80


State 59

   15 setStmt: SET set_knob_type '=' . VALUE_BOOL

    VALUE_BOOL  shift, and go to state 81


State 60

   53 aggExpr: COUNT '(' . '*' ')'
   54        | COUNT '(' . col ')'

    IDENTIFIER  shift, and go to state 42
    '*'         shift, and go to state 82

    col      go to state 83
    tbName   go to state 48
    colName  go to state 49


State 61

   58 aggExpr: MIN '(' . col ')'

    IDENTIFIER  shift, and go to state 42

    col      go to state 84
    tbName   go to state 48
    colName  go to state 49


State 62

   57 aggExpr: MAX '(' . col ')'

    IDENTIFIER  shift, and go to state 42

    col      go to state 85
    tbName   go to state 48
    colName  go to state 49


State 63

   56 aggExpr: AVG '(' . col ')'

    IDENTIFIER  shift, and go to state 42

    col      go to state 86
    tbName   go to state 48
    colName  go to state 49


State 64

   55 aggExpr: SUM '(' . col ')'

    IDENTIFIER  shift, and go to state 42

    col      go to state 87
    tbName   go to state 48
    colName  go to state 49


State 65

   80 opt_alias: AS . IDENTIFIER

    IDENTIFIER  shift, and go to state 88


State 66

   81 opt_alias: IDENTIFIER .

    $default  reduce using rule 81 (opt_alias)


State 67

   78 select_item: aggExpr opt_alias .

    $default  reduce using rule 78 (select_item)


State 68

   24 dml: SELECT selector FROM . tableList optWhereClause opt_group_by opt_having opt_order_clause

    IDENTIFIER  shift, and go to state 29

    tableList  go to state 89
    tbName     go to state 90


State 69

   76 selector: selector ',' . select_item

    COUNT       shift, and go to state 37
    MIN         shift, and go to state 38
    MAX         shift, and go to state 39
    AVG         shift, and go to state 40
    SUM         shift, and go to state 41
    IDENTIFIER  shift, and go to state 42

    aggExpr      go to state 44
    col          go to state 45
    select_item  go to state 91
    tbName       go to state 48
    colName      go to state 49


State 70

   59 col: tbName '.' . colName

    IDENTIFIER  shift, and go to state 77

    colName  go to state 92


State 71

   16 ddl: CREATE TABLE tbName '(' . fieldList ')'

    IDENTIFIER  shift, and go to state 77

    fieldList  go to state 93
    field      go to state 94
    colName    go to state 95


State 72

   19 ddl: CREATE INDEX tbName '(' . colNameList ')'

    IDENTIFIER  shift, and go to state 77

    colNameList  go to state 96
    colName      go to state 97


State 73

   20 ddl: DROP INDEX tbName '(' . colNameList ')'

    IDENTIFIER  shift, and go to state 77

    colNameList  go to state 98
    colName      go to state 97


State 74

   21 dml: INSERT INTO tbName VALUES . '(' valueList ')'

    '('  shift, and go to state 99


State 75

   41 optWhereClause: WHERE . whereClause

    IDENTIFIER  shift, and go to state 42

    condition    go to state 100
    whereClause  go to state 101
    col          go to state 102
    tbName       go to state 48
    colName      go to state 49


State 76

   22 dml: DELETE FROM tbName optWhereClause .

    $default  reduce using rule 22 (dml)


State 77

   95 colName: IDENTIFIER .

    $default  reduce using rule 95 (colName)


State 78

   23 dml: UPDATE tbName SET setClauses . optWhereClause
   72 setClauses: setClauses . ',' setClause

    WHERE  shift, and go to state 75
    ','    shift, and go to state 103

    $default  reduce using rule 40 (optWhereClause)

    optWhereClause  go to state 104


State 79

   71 setClauses: setClause .

    $default  reduce using rule 71 (setClauses)


State 80

   73 setClause: colName . '=' value

    '='  shift, and go to state 105


State 81

   15 setStmt: SET set_knob_type '=' VALUE_BOOL .

    $default  reduce using rule 15 (setStmt)


State 82

   53 aggExpr: COUNT '(' '*' . ')'

    ')'  shift, and go to state 106


State 83

   54 aggExpr: COUNT '(' col . ')'

    ')'  shift, and go to state 107


State 84

   58 aggExpr: MIN '(' col . ')'

    ')'  shift, and go to state 108


State 85

   57 aggExpr: MAX '(' col . ')'

    ')'  shift, and go to state 109


State 86

   56 aggExpr: AVG '(' col . ')'

    ')'  shift, and go to state 110


State 87

   55 aggExpr: SUM '(' col . ')'

    ')'  shift, and go to state 111


State 88

   80 opt_alias: AS IDENTIFIER .

    $default  reduce using rule 80 (opt_alias)


State 89

   24 dml: SELECT selector FROM tableList . optWhereClause opt_group_by opt_having opt_order_clause
   83 tableList: tableList . ',' tbName
   84          | tableList . JOIN tbName

    WHERE  shift, and go to state 75
    JOIN   shift, and go to state 112
    ','    shift, and go to state 113

    $default  reduce using rule 40 (optWhereClause)

    optWhereClause  go to state 114


State 90

   82 tableList: tbName .

    $default  reduce using rule 82 (tableList)


State 91

   76 selector: selector ',' select_item .

    $default  reduce using rule 76 (selector)


State 92

   59 col: tbName '.' colName .

    $default  reduce using rule 59 (col)


State 93

   16 ddl: CREATE TABLE tbName '(' fieldList . ')'
   26 fieldList: fieldList . ',' field

    ')'  shift, and go to state 115
    ','  shift, and go to state 116


State 94

   25 fieldList: field .

    $default  reduce using rule 25 (fieldList)


State 95

   29 field: colName . type

    INT    shift, and go to state 117
    CHAR   shift, and go to state 118
    FLOAT  shift, and go to state 119

    type  go to state 120


State 96

   19 ddl: CREATE INDEX tbName '(' colNameList . ')'
   28 colNameList: colNameList . ',' colName

    ')'  shift, and go to state 121
    ','  shift, and go to state 122


State 97

   27 colNameList: colName .

    $default  reduce using rule 27 (colNameList)


State 98

   20 ddl: DROP INDEX tbName '(' colNameList . ')'
   28 colNameList: colNameList . ',' colName

    ')'  shift, and go to state 123
    ','  shift, and go to state 122


State 99

   21 dml: INSERT INTO tbName VALUES '(' . valueList ')'

    VALUE_STRING  shift, and go to state 124
    VALUE_INT     shift, and go to state 125
    VALUE_FLOAT   shift, and go to state 126
    VALUE_BOOL    shift, and go to state 127

    valueList  go to state 128
    value      go to state 129


State 100

   42 whereClause: condition .

    $default  reduce using rule 42 (whereClause)


State 101

   41 optWhereClause: WHERE whereClause .
   43 whereClause: whereClause . AND condition

    AND  shift, and go to state 130

    $default  reduce using rule 41 (optWhereClause)


State 102

   39 condition: col . op expr

    LEQ  shift, and go to state 131
    NEQ  shift, and go to state 132
    GEQ  shift, and go to state 133
    '='  shift, and go to state 134
    '<'  shift, and go to state 135
    '>'  shift, and go to state 136

    op  go to state 137


State 103

   72 setClauses: setClauses ',' . setClause

    IDENTIFIER  shift, and go to state 77

    setClause  go to state 138
    colName    go to state 80


State 104

   23 dml: UPDATE tbName SET setClauses optWhereClause .

    $default  reduce using rule 23 (dml)


State 105

   73 setClause: colName '=' . value

    VALUE_STRING  shift, and go to state 124
    VALUE_INT     shift, and go to state 125
    VALUE_FLOAT   shift, and go to state 126
    VALUE_BOOL    shift, and go to state 127

    value  go to state 139


State 106

   53 aggExpr: COUNT '(' '*' ')' .

    $default  reduce using rule 53 (aggExpr)


State 107

   54 aggExpr: COUNT '(' col ')' .

    $default  reduce using rule 54 (aggExpr)


State 108

   58 aggExpr: MIN '(' col ')' .

    $default  reduce using rule 58 (aggExpr)


State 109

   57 aggExpr: MAX '(' col ')' .

    $default  reduce using rule 57 (aggExpr)


State 110

   56 aggExpr: AVG '(' col ')' .

    $default  reduce using rule 56 (aggExpr)


State 111

   55 aggExpr: SUM '(' col ')' .

    $default  reduce using rule 55 (aggExpr)


State 112

   84 tableList: tableList JOIN . tbName

    IDENTIFIER  shift, and go to state 29

    tbName  go to state 140


State 113

   83 tableList: tableList ',' . tbName

    IDENTIFIER  shift, and go to state 29

    tbName  go to state 141


State 114

   24 dml: SELECT selector FROM tableList optWhereClause . opt_group_by opt_having opt_order_clause

    GROUP  shift, and go to state 142

    $default  reduce using rule 45 (opt_group_by)

    opt_group_by  go to state 143


State 115

   16 ddl: CREATE TABLE tbName '(' fieldList ')' .

    $default  reduce using rule 16 (ddl)


State 116

   26 fieldList: fieldList ',' . field

    IDENTIFIER  shift, and go to state 77

    field    go to state 144
    colName  go to state 95


State 117

   30 type: INT .

    $default  reduce using rule 30 (type)


State 118

   31 type: CHAR . '(' VALUE_INT ')'

    '('  shift, and go to state 145


State 119

   32 type: FLOAT .

    $default  reduce using rule 32 (type)


State 120

   29 field: colName type .

    $default  reduce using rule 29 (field)


State 121

   19 ddl: CREATE INDEX tbName '(' colNameList ')' .

    $default  reduce using rule 19 (ddl)


State 122

   28 colNameList: colNameList ',' . colName

    IDENTIFIER  shift, and go to state 77

    colName  go to state 146


State 123

   20 ddl: DROP INDEX tbName '(' colNameList ')' .

    $default  reduce using rule 20 (ddl)


State 124

   37 value: VALUE_STRING .

    $default  reduce using rule 37 (value)


State 125

   35 value: VALUE_INT .

    $default  reduce using rule 35 (value)


State 126

   36 value: VALUE_FLOAT .

    $default  reduce using rule 36 (value)


State 127

   38 value: VALUE_BOOL .

    $default  reduce using rule 38 (value)


State 128

   21 dml: INSERT INTO tbName VALUES '(' valueList . ')'
   34 valueList: valueList . ',' value

    ')'  shift, and go to state 147
    ','  shift, and go to state 148


State 129

   33 valueList: value .

    $default  reduce using rule 33 (valueList)


State 130

   43 whereClause: whereClause AND . condition

    IDENTIFIER  shift, and go to state 42

    condition  go to state 149
    col        go to state 102
    tbName     go to state 48
    colName    go to state 49


State 131

   67 op: LEQ .

    $default  reduce using rule 67 (op)


State 132

   66 op: NEQ .

    $default  reduce using rule 66 (op)


State 133

   68 op: GEQ .

    $default  reduce using rule 68 (op)


State 134

   63 op: '=' .

    $default  reduce using rule 63 (op)


State 135

   64 op: '<' .

    $default  reduce using rule 64 (op)


State 136

   65 op: '>' .

    $default  reduce using rule 65 (op)


State 137

   39 condition: col op . expr

    IDENTIFIER    shift, and go to state 42
    VALUE_STRING  shift, and go to state 124
    VALUE_INT     shift, and go to state 125
    VALUE_FLOAT   shift, and go to state 126
    VALUE_BOOL    shift, and go to state 127

    value    go to state 150
    col      go to state 151
    expr     go to state 152
    tbName   go to state 48
    colName  go to state 49


State 138

   72 setClauses: setClauses ',' setClause .

    $default  reduce using rule 72 (setClauses)


State 139

   73 setClause: colName '=' value .

    $default  reduce using rule 73 (setClause)


State 140

   84 tableList: tableList JOIN tbName .

    $default  reduce using rule 84 (tableList)


State 141

   83 tableList: tableList ',' tbName .

    $default  reduce using rule 83 (tableList)


State 142

   44 opt_group_by: GROUP . BY colList

    BY  shift, and go to state 153


State 143

   24 dml: SELECT selector FROM tableList optWhereClause opt_group_by . opt_having opt_order_clause

    HAVING  shift, and go to state 154

    $default  reduce using rule 47 (opt_having)

    opt_having  go to state 155


State 144

   26 fieldList: fieldList ',' field .

    $default  reduce using rule 26 (fieldList)


State 145

   31 type: CHAR '(' . VALUE_INT ')'

    VALUE_INT  shift, and go to state 156


State 146

   28 colNameList: colNameList ',' colName .

    $default  reduce using rule 28 (colNameList)


State 147

   21 dml: INSERT INTO tbName VALUES '(' valueList ')' .

    $default  reduce using rule 21 (dml)


State 148

   34 valueList: valueList ',' . value

    VALUE_STRING  shift, and go to state 124
    VALUE_INT     shift, and go to state 125
    VALUE_FLOAT   shift, and go to state 126
    VALUE_BOOL    shift, and go to state 127

    value  go to state 157


State 149

   43 whereClause: whereClause AND condition .

    $default  reduce using rule 43 (whereClause)


State 150

   69 expr: value .

    $default  reduce using rule 69 (expr)


State 151

   70 expr: col .

    $default  reduce using rule 70 (expr)


State 152

   39 condition: col op expr .

    $default  reduce using rule 39 (condition)


State 153

   44 opt_group_by: GROUP BY . colList

    IDENTIFIER  shift, and go to state 42

    col      go to state 158
    colList  go to state 159
    tbName   go to state 48
    colName  go to state 49


State 154

   46 opt_having: HAVING . havingClause

    COUNT       shift, and go to state 37
    MIN         shift, and go to state 38
    MAX         shift, and go to state 39
    AVG         shift, and go to state 40
    SUM         shift, and go to state 41
    IDENTIFIER  shift, and go to state 42

    havingClause     go to state 160
    havingCondition  go to state 161
    aggExpr          go to state 162
    col              go to state 163
    tbName           go to state 48
    colName          go to state 49


State 155

   24 dml: SELECT selector FROM tableList optWhereClause opt_group_by opt_having . opt_order_clause

    ORDER  shift, and go to state 164

    $default  reduce using rule 86 (opt_order_clause)

    opt_order_clause  go to state 165


State 156

   31 type: CHAR '(' VALUE_INT . ')'

    ')'  shift, and go to state 166


State 157

   34 valueList: valueList ',' value .

    $default  reduce using rule 34 (valueList)


State 158

   61 colList: col .

    $default  reduce using rule 61 (colList)


State 159

   44 opt_group_by: GROUP BY colList .
   62 colList: colList . ',' col

    ','  shift, and go to state 167

    $default  reduce using rule 44 (opt_group_by)


State 160

   46 opt_having: HAVING havingClause .
   49 havingClause: havingClause . AND havingCondition

    AND  shift, and go to state 168

    $default  reduce using rule 46 (opt_having)


State 161

   48 havingClause: havingCondition .

    $default  reduce using rule 48 (havingClause)


State 162

   50 havingCondition: aggExpr . op expr

    LEQ  shift, and go to state 131
    NEQ  shift, and go to state 132
    GEQ  shift, and go to state 133
    '='  shift, and go to state 134
    '<'  shift, and go to state 135
    '>'  shift, and go to state 136

    op  go to state 169


State 163

   51 havingCondition: col . op expr
   52                | col . op aggExpr

    LEQ  shift, and go to state 131
    NEQ  shift, and go to state 132
    GEQ  shift, and go to state 133
    '='  shift, and go to state 134
    '<'  shift, and go to state 135
    '>'  shift, and go to state 136

    op  go to state 170


State 164

   85 opt_order_clause: ORDER . BY order_clause

    BY  shift, and go to state 171


State 165

   24 dml: SELECT selector FROM tableList optWhereClause opt_group_by opt_having opt_order_clause .

    $default  reduce using rule 24 (dml)


State 166

   31 type: CHAR '(' VALUE_INT ')' .

    $default  reduce using rule 31 (type)


State 167

   62 colList: colList ',' . col

    IDENTIFIER  shift, and go to state 42

    col      go to state 172
    tbName   go to state 48
    colName  go to state 49


State 168

   49 havingClause: havingClause AND . havingCondition

    COUNT       shift, and go to state 37
    MIN         shift, and go to state 38
    MAX         shift, and go to state 39
    AVG         shift, and go to state 40
    SUM         shift, and go to state 41
    IDENTIFIER  shift, and go to state 42

    havingCondition  go to state 173
    aggExpr          go to state 162
    col              go to state 163
    tbName           go to state 48
    colName          go to state 49


State 169

   50 havingCondition: aggExpr op . expr

    IDENTIFIER    shift, and go to state 42
    VALUE_STRING  shift, and go to state 124
    VALUE_INT     shift, and go to state 125
    VALUE_FLOAT   shift, and go to state 126
    VALUE_BOOL    shift, and go to state 127

    value    go to state 150
    col      go to state 151
    expr     go to state 174
    tbName   go to state 48
    colName  go to state 49


State 170

   51 havingCondition: col op . expr
   52                | col op . aggExpr

    COUNT         shift, and go to state 37
    MIN           shift, and go to state 38
    MAX           shift, and go to state 39
    AVG           shift, and go to state 40
    SUM           shift, and go to state 41
    IDENTIFIER    shift, and go to state 42
    VALUE_STRING  shift, and go to state 124
    VALUE_INT     shift, and go to state 125
    VALUE_FLOAT   shift, and go to state 126
    VALUE_BOOL    shift, and go to state 127

    value    go to state 150
    aggExpr  go to state 175
    col      go to state 151
    expr     go to state 176
    tbName   go to state 48
    colName  go to state 49


State 171

   85 opt_order_clause: ORDER BY . order_clause

    IDENTIFIER  shift, and go to state 42

    col           go to state 177
    order_clause  go to state 178
    tbName        go to state 48
    colName       go to state 49


State 172

   62 colList: colList ',' col .

    $default  reduce using rule 62 (colList)


State 173

   49 havingClause: havingClause AND havingCondition .

    $default  reduce using rule 49 (havingClause)


State 174

   50 havingCondition: aggExpr op expr .

    $default  reduce using rule 50 (havingCondition)


State 175

   52 havingCondition: col op aggExpr .

    $default  reduce using rule 52 (havingCondition)


State 176

   51 havingCondition: col op expr .

    $default  reduce using rule 51 (havingCondition)


State 177

   87 order_clause: col . opt_asc_desc

    DESC  shift, and go to state 179
    ASC   shift, and go to state 180

    $default  reduce using rule 91 (opt_asc_desc)

    opt_asc_desc  go to state 181


State 178

   85 opt_order_clause: ORDER BY order_clause .
   88 order_clause: order_clause . ',' col opt_asc_desc

    ','  shift, and go to state 182

    $default  reduce using rule 85 (opt_order_clause)


State 179

   90 opt_asc_desc: DESC .

    $default  reduce using rule 90 (opt_asc_desc)


State 180

   89 opt_asc_desc: ASC .

    $default  reduce using rule 89 (opt_asc_desc)


State 181

   87 order_clause: col opt_asc_desc .

    $default  reduce using rule 87 (order_clause)


State 182

   88 order_clause: order_clause ',' . col opt_asc_desc

    IDENTIFIER  shift, and go to state 42

    col      go to state 183
    tbName   go to state 48
    colName  go to state 49


State 183

   88 order_clause: order_clause ',' col . opt_asc_desc

    DESC  shift, and go to state 179
    ASC   shift, and go to state 180

    $default  reduce using rule 91 (opt_asc_desc)

    opt_asc_desc  go to state 184


State 184

   88 order_clause: order_clause ',' col opt_asc_desc .

    $default  reduce using rule 88 (order_clause)
