/* Copyright (c) 2023 Renmin University of China
RMDB is licensed under Mulan PSL v2.
You can use this software according to the terms and conditions of the Mulan PSL v2.
You may obtain a copy of Mulan PSL v2 at:
        http://license.coscl.org.cn/MulanPSL2
THIS SOFTWARE IS PROVIDED ON AN "AS IS" BASIS, WITHOUT WARRANTIES OF ANY KIND,
EITHER EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO NON-INFRINGEMENT,
MERCHANTABILITY OR FIT FOR A PARTICULAR PURPOSE.
See the Mulan PSL v2 for more details. */

#pragma once

#include <cassert>
#include <cstring>
#include <memory>
#include <string>
#include <vector>
#include "defs.h"
#include "record/rm_defs.h"
#include "system/sm_meta.h"
#include "parser/ast.h"


struct TabCol {
    std::string tab_name;
    std::string col_name;
    bool have_alia{false};
    std::string alia_name;

    friend bool operator<(const TabCol &x, const TabCol &y) {
        return std::make_pair(x.tab_name, x.col_name) < std::make_pair(y.tab_name, y.col_name);
    }
};

struct Value {
    ColType type;  // type of value
    union {
        int int_val;      // int value
        float float_val;  // float value
    };
    std::string str_val;  // string value
    bool is_null;         // NULL value flag

    std::shared_ptr<RmRecord> raw;  // raw record buffer

    // 默认构造函数
    Value() : type(TYPE_INT), int_val(0), is_null(false) {}

    void set_int(int int_val_) {
        type = TYPE_INT;
        int_val = int_val_;
        is_null = false;
    }

    void set_float(float float_val_) {
        type = TYPE_FLOAT;
        float_val = float_val_;
        is_null = false;
    }

    void set_str(std::string str_val_) {
        type = TYPE_STRING;
        str_val = std::move(str_val_);
        is_null = false;
    }

    void set_null() {
        is_null = true;
        // 保持type不变，但值无意义
    }

    void init_raw(int len) {
        assert(raw == nullptr);
        raw = std::make_shared<RmRecord>(len);
        if (type == TYPE_INT) {
            assert(len == sizeof(int));
            *(int *)(raw->data) = int_val;
        } else if (type == TYPE_FLOAT) {
            assert(len == sizeof(float));
            *(float *)(raw->data) = float_val;
        } else if (type == TYPE_STRING) {
            if (len < (int)str_val.size()) {
                throw StringOverflowError();
            }
            memset(raw->data, 0, len);
            memcpy(raw->data, str_val.c_str(), str_val.size());
        }
    }
};

enum CompOp { OP_EQ, OP_NE, OP_LT, OP_GT, OP_LE, OP_GE ,OP_ADD , OP_SUB, OP_MUL, OP_DIV, OP_NONE};

struct Condition {
    TabCol lhs_col;   // left-hand side column
    bool is_lhs_agg;    //左条件是否是聚合
    ast::AggExpr lhs_agg; //聚合表达式
    CompOp op;        // comparison operator
    bool is_rhs_val;  // true if right-hand side is a value (not a column)
    TabCol rhs_col;   // right-hand side column
    Value rhs_val;    // right-hand side value

    // 添加默认构造函数
    Condition() 
        : is_lhs_agg(false), 
          op(OP_EQ), 
          is_rhs_val(false),
          lhs_agg(AGG_NONE, nullptr) {  // 初始化聚合表达式为无聚合
    }

    bool RecordIsEqual(RmRecord first, RmRecord second, TabMeta& firstTab, TabMeta& secondTab)
    {
        auto firstCol = firstTab.get_col(lhs_col.col_name);
        auto secondCol = secondTab.get_col(rhs_col.col_name);
        std::unique_ptr<Value> firstValue = ValueConstruct(firstCol,first);
        std::unique_ptr<Value> secondValue = ValueConstruct(secondCol,second);
        return ValueIsEqual(*firstValue, *secondValue, op);
    }

    bool RecordIsEqual(RmRecord first, RmRecord second, const std::vector<ColMeta>& firstTab, const std::vector<ColMeta>& secondTab)
    {
        std::vector<ColMeta>::const_iterator first_it = firstTab.begin();
        std::vector<ColMeta>::const_iterator second_it = secondTab.begin();
        while(first_it != firstTab.end())
        {
            if(first_it->name == lhs_col.col_name)
            {
                break;
            }
            first_it++;
        }
        while(second_it != secondTab.end())
        {
            if(second_it->name == rhs_col.col_name)
            {
                break;
            }
            second_it++;
        }
        auto firstCol = first_it;
        auto secondCol = second_it;
        std::unique_ptr<Value> firstValue = ValueConstruct(firstCol,first);
        std::unique_ptr<Value> secondValue = ValueConstruct(secondCol,second);
        return ValueIsEqual(*firstValue, *secondValue, op);
    }

    bool ValueIsEqual(Value first, Value second, CompOp op) 
    {
        // 处理类型不匹配的情况（仅支持 INT 和 FLOAT 之间的转换比较）
        if (first.type != second.type) {
            if ((first.type == TYPE_INT && second.type == TYPE_FLOAT) || 
                (first.type == TYPE_FLOAT && second.type == TYPE_INT)) {
                // 转换为 double 进行比较（避免精度损失）
                double v1 = (first.type == TYPE_INT) ? first.int_val : first.float_val;
                double v2 = (second.type == TYPE_INT) ? second.int_val : second.float_val;
                
                switch (op) {
                    case OP_EQ: return v1 == v2;
                    case OP_NE: return v1 != v2;
                    case OP_LT: return v1 < v2;
                    case OP_GT: return v1 > v2;
                    case OP_LE: return v1 <= v2;
                    case OP_GE: return v1 >= v2;
                    default: return false;
                }
            }
            // 其他类型不匹配的情况，直接返回 false
            return false;
        }
        // 类型相同的情况，根据类型进行比较
        switch (first.type) {
            case TYPE_INT: {
                int v1 = first.int_val;
                int v2 = second.int_val;
                
                switch (op) {
                    case OP_EQ: return v1 == v2;
                    case OP_NE: return v1 != v2;
                    case OP_LT: return v1 < v2;
                    case OP_GT: return v1 > v2;
                    case OP_LE: return v1 <= v2;
                    case OP_GE: return v1 >= v2;
                    default: return false;
                }
            }
            
            case TYPE_FLOAT: {
                float v1 = first.float_val;
                float v2 = second.float_val;
                
                // 浮点数直接比较可能有精度问题，可考虑使用误差范围
                // 但这里保持与原始代码一致
                switch (op) {
                    case OP_EQ: return v1 == v2;
                    case OP_NE: return v1 != v2;
                    case OP_LT: return v1 < v2;
                    case OP_GT: return v1 > v2;
                    case OP_LE: return v1 <= v2;
                    case OP_GE: return v1 >= v2;
                    default: return false;
                }
            }
            
            case TYPE_STRING: {
                const std::string& s1 = first.str_val;
                const std::string& s2 = second.str_val;

                // const std::string& s1 = first.raw->data;
                // const std::string& s2 = second.raw->data;
                
                switch (op) {
                    case OP_EQ: return s1 == s2;
                    case OP_NE: return s1 != s2;
                    case OP_LT: return s1 < s2;
                    case OP_GT: return s1 > s2;
                    case OP_LE: return s1 <= s2;
                    case OP_GE: return s1 >= s2;
                    default: return false;
                }
            }
            
            default:
                // 不支持的类型
                return false;
        }
    }

    std::unique_ptr<Value> ValueConstruct(std::vector<ColMeta>::const_iterator firstCol,RmRecord first)
    {
        auto value = std::make_unique<Value>();
        std::string str(first.data, firstCol->len);
        switch(firstCol->type)
        {
            case TYPE_INT:
                value->set_int(*reinterpret_cast<const int*>(first.data + firstCol->offset));
                break;
            case TYPE_FLOAT:
                value->set_float(*reinterpret_cast<const float*>(first.data + firstCol->offset));
                break;
            case TYPE_STRING:
                value->set_str(str);
                break;
            default:
                throw InternalError("invalid type\n");
        }
        return value;
    }
};

struct SetValue
{
    /*set条件的右值，op代表加减乘除*/
    TabCol first_col;
    Value first_val;
    bool first_is_col{true};
    bool first_is_val{false};
    CompOp op;
    TabCol second_col;
    Value second_val;
    bool second_is_col{true};
    bool second_is_val{false};

    bool is_expr{false};
};

struct SetClause {
    TabCol lhs;
    SetValue rhs;
};

struct JoinRef
{
    std::string left_table;
    std::string right_table;
    std::vector<Condition> join_conds;
    JoinType type;
};

