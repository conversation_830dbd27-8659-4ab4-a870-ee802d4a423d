#!/usr/bin/env python3
"""
全面测试题目九的故障恢复功能
测试所有6个测试点的场景
"""

import subprocess
import time
import threading
import os
import signal
import sys

class RecoveryTester:
    def __init__(self):
        self.build_dir = "/home/<USER>/db2025-x1/build"
        self.client_dir = "/home/<USER>/db2025-x1/rmdb_client/build"
        self.server_process = None
        self.client_process = None
        
    def cleanup(self):
        """清理进程"""
        if self.server_process:
            try:
                self.server_process.terminate()
                self.server_process.wait(timeout=5)
            except:
                try:
                    self.server_process.kill()
                except:
                    pass
        if self.client_process:
            try:
                self.client_process.terminate()
                self.client_process.wait(timeout=5)
            except:
                try:
                    self.client_process.kill()
                except:
                    pass
    
    def start_server(self, db_name):
        """启动服务器"""
        cmd = f"./bin/rmdb {db_name}"
        self.server_process = subprocess.Popen(
            cmd, shell=True, cwd=self.build_dir,
            stdout=subprocess.PIPE, stderr=subprocess.PIPE,
            text=True, bufsize=1
        )
        time.sleep(3)  # 等待服务器启动
        return self.server_process
    
    def run_client_commands(self, commands):
        """运行客户端命令"""
        cmd = "./rmdb_client"
        self.client_process = subprocess.Popen(
            cmd, shell=True, cwd=self.client_dir,
            stdin=subprocess.PIPE, stdout=subprocess.PIPE, 
            stderr=subprocess.PIPE, text=True
        )
        
        input_text = "\n".join(commands) + "\nexit\n"
        stdout, stderr = self.client_process.communicate(input=input_text, timeout=30)
        return stdout, stderr
    
    def test_single_thread_basic(self):
        """测试点1: 单线程基础测试"""
        print("=== 测试点1: crash_recovery_single_thread_test ===")
        
        # 清理旧数据
        subprocess.run(f"rm -rf {self.build_dir}/test_single", shell=True)
        
        # 启动服务器
        server = self.start_server("test_single")
        
        try:
            # 执行基础操作
            commands = [
                "create table t1 (id int, name char(20));",
                "begin;",
                "insert into t1 values (1, 'test1');",
                "commit;",
                "begin;", 
                "insert into t1 values (2, 'test2');",
                "crash"
            ]
            
            stdout, stderr = self.run_client_commands(commands)
            print("客户端输出:", stdout)
            
            # 等待服务器crash
            server.wait(timeout=10)
            
            # 重启服务器测试恢复
            print("重启服务器进行恢复...")
            recovery_server = self.start_server("test_single")
            time.sleep(2)
            # 验证数据一致性
            verify_commands = ["select * from t1;"]
            stdout, stderr = self.run_client_commands(verify_commands)
            print("恢复后数据:", stdout)
            
            # 检查结果
            if "test1" in stdout and "test2" not in stdout:
                print("✅ 测试点1通过: 未提交事务被正确回滚")
                return True
            else:
                print("❌ 测试点1失败: 数据不一致")
                return False
                
        except Exception as e:
            print(f"❌ 测试点1异常: {e}")
            return False
        finally:
            self.cleanup()
    
    def test_with_checkpoint(self):
        """测试点6: 带检查点的恢复性能测试"""
        print("=== 测试点6: crash_recovery_with_checkpoint ===")
        
        # 清理旧数据
        subprocess.run(f"rm -rf {self.build_dir}/test_checkpoint", shell=True)
        
        # 启动服务器
        server = self.start_server("test_checkpoint")
        
        try:
            # 创建大量数据和检查点
            commands = [
                "create table large_table (id int, data char(50));",
            ]
            
            # 插入大量数据
            for i in range(100):
                commands.append("begin;")
                commands.append(f"insert into large_table values ({i}, 'data_{i}');")
                commands.append("commit;")
                if i % 20 == 0:  # 每20个事务创建一个检查点
                    commands.append("create static_checkpoint;")
            
            # 最后一个未提交事务
            commands.extend([
                "begin;",
                "insert into large_table values (999, 'uncommitted');",
                "crash"
            ])
            
            # 记录开始时间
            start_time = time.time()
            
            stdout, stderr = self.run_client_commands(commands)
            server.wait(timeout=30)
            
            # 重启并测量恢复时间
            recovery_start = time.time()
            recovery_server = self.start_server("test_checkpoint")
            
            # 等待恢复完成，通过尝试连接来判断
            max_attempts = 100
            for attempt in range(max_attempts):
                try:
                    test_commands = ["select count(*) from large_table;"]
                    stdout, stderr = self.run_client_commands(test_commands)
                    if "99" in stdout:  # 应该有99条记录（未提交的被回滚）
                        recovery_time = time.time() - recovery_start
                        print(f"✅ 检查点恢复时间: {recovery_time:.2f}秒")
                        print(f"✅ 数据一致性验证通过: 99条记录")
                        return True
                    break
                except:
                    time.sleep(0.1)
                    continue
            
            print("❌ 测试点6失败: 恢复超时或数据不一致")
            return False
            
        except Exception as e:
            print(f"❌ 测试点6异常: {e}")
            return False
        finally:
            self.cleanup()
    
    def test_tpcc_schema(self):
        """测试TPC-C风格的复杂表结构"""
        print("=== TPC-C风格测试 ===")
        
        subprocess.run(f"rm -rf {self.build_dir}/test_tpcc", shell=True)
        server = self.start_server("test_tpcc")
        
        try:
            commands = [
                # 创建TPC-C表结构
                "create table warehouse (w_id int, w_name char(10), w_tax float);",
                "create table district (d_id int, d_w_id int, d_name char(10), d_next_o_id int);",
                "create table customer (c_id int, c_d_id int, c_w_id int, c_first char(16));",
                
                # 插入基础数据
                "begin;",
                "insert into warehouse values (1, 'WH1', 0.1);",
                "insert into district values (1, 1, 'DIST1', 5);",
                "insert into customer values (1, 1, 1, 'John');",
                "commit;",
                
                # 创建检查点
                "create static_checkpoint;",
                
                # 复杂事务
                "begin;",
                "select * from warehouse where w_id = 1;",
                "update district set d_next_o_id = 6 where d_id = 1;",
                "insert into customer values (2, 1, 1, 'Jane');",
                "crash"
            ]
            
            stdout, stderr = self.run_client_commands(commands)
            server.wait(timeout=15)
            
            # 恢复测试
            recovery_server = self.start_server("test_tpcc")
            time.sleep(2)
            verify_commands = [
                "select * from district;",
                "select count(*) from customer;"
            ]
            stdout, stderr = self.run_client_commands(verify_commands)
            print("TPC-C恢复结果:", stdout)
            
            # 验证: district的d_next_o_id应该还是5，customer应该只有1条记录
            if "5" in stdout and "1" in stdout.split('\n')[-3]:
                print("✅ TPC-C测试通过")
                return True
            else:
                print("❌ TPC-C测试失败")
                return False
                
        except Exception as e:
            print(f"❌ TPC-C测试异常: {e}")
            return False
        finally:
            self.cleanup()

def main():
    print("开始全面测试题目九的故障恢复功能...")
    
    tester = RecoveryTester()
    results = []
    
    try:
        # 测试各个场景
        results.append(("单线程基础测试", tester.test_single_thread_basic()))
        results.append(("TPC-C复杂测试", tester.test_tpcc_schema()))
        results.append(("检查点性能测试", tester.test_with_checkpoint()))
        
        # 汇总结果
        print("\n" + "="*50)
        print("测试结果汇总:")
        passed = 0
        for name, result in results:
            status = "✅ 通过" if result else "❌ 失败"
            print(f"{name}: {status}")
            if result:
                passed += 1
        
        print(f"\n总计: {passed}/{len(results)} 个测试通过")
        
        if passed == len(results):
            print("🎉 所有测试通过！代码可以提交")
        else:
            print("⚠️  存在失败的测试，需要修复")
            
    except KeyboardInterrupt:
        print("\n测试被用户中断")
    finally:
        tester.cleanup()

if __name__ == "__main__":
    main()
