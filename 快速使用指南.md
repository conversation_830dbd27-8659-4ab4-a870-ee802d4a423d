# db2025-x1 测试套件快速使用指南

## 🚀 快速开始

### 1. 运行所有测试（推荐）
```bash
./run_all_tests.sh
```

### 2. 快速测试主要题目
```bash
python3 quick_test.py
```

### 3. 测试指定题目
```bash
# 测试题目5（聚合函数）
python3 quick_test.py --topic 5

# 或使用综合测试脚本
python3 comprehensive_test.py --topic 5
```

## 📋 可用脚本

| 脚本名称 | 用途 | 特点 |
|---------|------|------|
| `run_all_tests.sh` | 主测试脚本 | 全面测试，Shell脚本 |
| `quick_test.py` | 快速测试 | 简化版，主要题目 |
| `comprehensive_test.py` | 综合测试 | 完整功能，灵活配置 |
| `aggregate_test_suite.py` | 聚合函数专项 | 题目5专用 |
| `crash_recovery_test_suite.py` | 故障恢复专项 | 题目9专用 |

## 🎯 按题目测试

### 题目1: 存储管理
```bash
# 单元测试
python3 quick_test.py --topic 1
```

### 题目2: 查询执行
```bash
# SQL测试
python3 quick_test.py --topic 2
```

### 题目5: 聚合函数与分组统计
```bash
# 专项测试（推荐）
python3 aggregate_test_suite.py

# 或快速测试
python3 quick_test.py --topic 5
```

### 题目7: 事务控制语句
```bash
# SQL测试
python3 quick_test.py --topic 7
```

### 题目9: 基于静态检查点的故障恢复
```bash
# 专项测试（推荐）
python3 crash_recovery_test_suite.py

# 或快速测试
python3 quick_test.py --topic 9
```

## ⚙️ 常用选项

### Shell脚本选项
```bash
./run_all_tests.sh --help              # 显示帮助
./run_all_tests.sh --unit-only         # 仅运行单元测试
./run_all_tests.sh --sql-only          # 仅运行SQL测试
./run_all_tests.sh --topic 5           # 测试指定题目
./run_all_tests.sh --no-build          # 跳过构建步骤
```

### Python脚本选项
```bash
python3 comprehensive_test.py --help           # 显示帮助
python3 comprehensive_test.py --list-tests     # 列出所有测试
python3 comprehensive_test.py --topic 5        # 测试指定题目
python3 comprehensive_test.py --unit-only      # 仅单元测试
python3 comprehensive_test.py --sql-only       # 仅SQL测试
python3 comprehensive_test.py --no-build       # 跳过构建

python3 quick_test.py --topic 5                # 快速测试指定题目
python3 quick_test.py --no-build               # 跳过构建检查
```

## 🔧 环境要求

### 必需
- Linux操作系统
- Python 3.6+
- pexpect包：`pip3 install pexpect`

### 预配置
- `build/` 目录已配置
- `rmdb_client/build/` 目录已配置
- 无需手动CMake配置

## 📊 输出说明

### 成功标识
- `✓` 绿色：测试通过
- `[SUCCESS]` 绿色：操作成功

### 警告标识
- `[WARNING]` 黄色：警告信息
- 部分测试失败但可继续

### 错误标识
- `✗` 红色：测试失败
- `[ERROR]` 红色：严重错误

### 日志文件
- `comprehensive_test_log.txt`：详细测试日志
- `build/{db_name}/output.txt`：数据库输出

## 🚨 故障排除

### 1. 构建失败
```bash
# 检查构建目录
ls -la build/ rmdb_client/build/

# 重新构建
cd build && make rmdb -j4
cd rmdb_client/build && make rmdb_client -j4
```

### 2. 服务器启动失败
```bash
# 检查端口占用
netstat -tlnp | grep :8765

# 杀死残留进程
pkill -f rmdb
```

### 3. 权限问题
```bash
chmod +x *.py *.sh
```

### 4. Python依赖问题
```bash
pip3 install pexpect
python3 -c "import pexpect"  # 验证安装
```

## 📈 性能测试

### 题目3: 索引性能
- 自动验证索引查询性能提升
- 要求：建立索引后查询时间 ≤ 70% 原时间

### 题目9: 检查点性能
- 自动测量恢复时间对比
- 要求：检查点恢复时间 ≤ 70% 无检查点恢复时间

## 🎯 推荐使用流程

### 日常开发测试
```bash
# 1. 快速验证主要功能
python3 quick_test.py

# 2. 测试特定题目
python3 quick_test.py --topic 5

# 3. 全面测试（提交前）
./run_all_tests.sh
```

### 专项功能测试
```bash
# 聚合函数详细测试
python3 aggregate_test_suite.py

# 故障恢复详细测试
python3 crash_recovery_test_suite.py
```

### 调试和验证
```bash
# 列出所有发现的测试
python3 comprehensive_test.py --list-tests

# 验证测试框架
python3 test_framework_validation.py
```

## 💡 提示

1. **首次使用**：建议先运行 `python3 test_framework_validation.py` 验证环境
2. **快速测试**：日常开发使用 `quick_test.py` 即可
3. **全面测试**：提交前使用 `run_all_tests.sh` 确保完整性
4. **专项测试**：特定功能问题使用对应的专项测试脚本
5. **日志查看**：测试失败时查看 `comprehensive_test_log.txt` 获取详细信息

## 📞 支持

如遇问题，请：
1. 查看日志文件获取详细错误信息
2. 确认环境配置正确
3. 检查构建目录和可执行文件
4. 验证Python依赖完整性

---

**快速命令参考**：
- 全面测试：`./run_all_tests.sh`
- 快速测试：`python3 quick_test.py`
- 题目5测试：`python3 aggregate_test_suite.py`
- 题目9测试：`python3 crash_recovery_test_suite.py`
- 验证框架：`python3 test_framework_validation.py`
