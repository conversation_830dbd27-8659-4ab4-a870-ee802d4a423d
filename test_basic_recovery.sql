-- 测试基础故障恢复（不带检查点）
-- 这个测试对应题目九的测试示例

-- 1. 创建测试表
create table t1 (id int, num int);

-- 2. 第一个事务：插入数据并提交
begin;
insert into t1 values(1, 1);
commit;

-- 3. 第二个事务：插入数据但不提交（模拟crash前的未完成事务）
begin;
insert into t1 values(2, 2);
-- 这里不commit，模拟系统crash

-- 4. 查看当前数据（crash前）
select * from t1;

-- 测试说明：
-- 1. 执行到这里后，手动停止服务器（模拟crash）
-- 2. 重新启动服务器
-- 3. 执行 select * from t1; 
-- 4. 预期结果：只有 (1,1) 这一条记录，因为第二个事务未提交应该被回滚
