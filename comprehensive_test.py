#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
全面测试整合脚本 - db2025-x1 决赛优化阶段
该脚本整合了所有9个题目的测试用例，包括单元测试和SQL集成测试
"""

import os
import sys
import time
import subprocess
import signal
import argparse
import pexpect
import re
import glob
from datetime import datetime

# 全局配置
SERVER_EXEC = "./build/bin/rmdb"
CLIENT_EXEC = "./rmdb_client/build/rmdb_client"
DB_NAME = "comprehensive_test_db"
TEST_DIR = "./test"
OUTPUT_DIR = f"./build/{DB_NAME}"
LOG_FILE = "comprehensive_test_log.txt"

def discover_unit_tests():
    """自动发现单元测试"""
    unit_tests = []
    test_dirs = ["storage", "index", "query", "transaction", "concurrency"]

    for test_dir in test_dirs:
        test_path = os.path.join(TEST_DIR, test_dir)
        if os.path.exists(test_path):
            # 查找C++测试文件
            cpp_files = glob.glob(os.path.join(test_path, "*_test.cpp"))
            for cpp_file in cpp_files:
                test_name = os.path.basename(cpp_file).replace(".cpp", "")
                unit_tests.append(f"{test_dir}/{test_name}")

    return unit_tests

def discover_sql_tests():
    """自动发现SQL测试"""
    sql_tests = {}

    # 查找所有SQL测试文件
    sql_patterns = [
        "test/**/test*.sql",
        "test/**/*_test.sql",
        "test/**/*test*.sql",
        "*.sql"  # 根目录下的SQL文件
    ]

    all_sql_files = []
    for pattern in sql_patterns:
        all_sql_files.extend(glob.glob(pattern, recursive=True))

    # 按题目分类
    for sql_file in all_sql_files:
        # 跳过一些特殊文件
        if any(skip in sql_file for skip in ["Zone.Identifier", "backup", "temp"]):
            continue

        # 根据文件名和路径推断题目
        topic_id = classify_sql_test(sql_file)
        if topic_id:
            if topic_id not in sql_tests:
                sql_tests[topic_id] = []
            sql_tests[topic_id].append(sql_file)

    return sql_tests

def classify_sql_test(sql_file):
    """根据SQL文件路径和名称分类到对应题目"""
    file_lower = sql_file.lower()

    # 题目2: 查询执行
    if any(keyword in file_lower for keyword in ["basic_query", "execution"]):
        return 2

    # 题目3: 唯一索引
    if any(keyword in file_lower for keyword in ["index", "storage_test"]):
        return 3

    # 题目4: 查询优化 (与查询执行共用一些测试)
    if "optimization" in file_lower or "explain" in file_lower:
        return 4

    # 题目5: 聚合函数
    if any(keyword in file_lower for keyword in ["agg", "aggregate", "group", "having"]):
        return 5

    # 题目6: 半连接
    if "semi" in file_lower or "join" in file_lower:
        return 6

    # 题目7: 事务控制
    if any(keyword in file_lower for keyword in ["transaction", "commit", "abort", "begin"]):
        return 7

    # 题目8: MVCC
    if any(keyword in file_lower for keyword in ["concurrency", "mvcc", "dirty", "phantom", "unrepeatable"]):
        return 8

    # 题目9: 故障恢复
    if any(keyword in file_lower for keyword in ["recovery", "crash", "checkpoint"]):
        return 9

    # 默认归类到查询执行
    return 2

# 测试题目配置 - 动态发现
def get_topics_config():
    """获取测试题目配置"""
    # 发现测试文件
    unit_tests = discover_unit_tests()
    sql_tests = discover_sql_tests()

    # 基础配置
    topics = {
        1: {"name": "存储管理", "unit_tests": [], "sql_tests": []},
        2: {"name": "查询执行", "unit_tests": [], "sql_tests": []},
        3: {"name": "唯一索引", "unit_tests": [], "sql_tests": []},
        4: {"name": "查询优化", "unit_tests": [], "sql_tests": []},
        5: {"name": "聚合函数与分组统计", "unit_tests": [], "sql_tests": []},
        6: {"name": "半连接（Semi Join）", "unit_tests": [], "sql_tests": []},
        7: {"name": "事务控制语句", "unit_tests": [], "sql_tests": []},
        8: {"name": "多版本并发控制（MVCC）", "unit_tests": [], "sql_tests": []},
        9: {"name": "基于静态检查点的故障恢复", "unit_tests": [], "sql_tests": []}
    }

    # 分配单元测试
    for unit_test in unit_tests:
        if "storage" in unit_test:
            topics[1]["unit_tests"].append(unit_test)
        elif "index" in unit_test:
            topics[3]["unit_tests"].append(unit_test)
        elif "query" in unit_test:
            topics[2]["unit_tests"].append(unit_test)
        elif "transaction" in unit_test:
            topics[7]["unit_tests"].append(unit_test)
        elif "concurrency" in unit_test:
            topics[8]["unit_tests"].append(unit_test)

    # 分配SQL测试
    for topic_id, sql_files in sql_tests.items():
        if topic_id in topics:
            topics[topic_id]["sql_tests"] = sql_files

    return topics

def log_message(message, level="INFO"):
    """记录日志消息到控制台和日志文件"""
    timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    log_line = f"[{timestamp}] [{level}] {message}"
    print(log_line)
    
    with open(LOG_FILE, "a") as f:
        f.write(log_line + "\n")

def run_command(command, timeout=None, shell=True, cwd=None):
    """运行命令并返回结果"""
    try:
        result = subprocess.run(
            command,
            shell=shell,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            timeout=timeout,
            text=True,
            cwd=cwd
        )
        return result.returncode, result.stdout, result.stderr
    except subprocess.TimeoutExpired:
        return -1, "", "Command timed out"

def start_rmdb_server(db_name):
    """启动RMDB服务器"""
    log_message(f"启动RMDB服务器: {SERVER_EXEC} {db_name}")
    
    # 确保输出目录存在
    os.makedirs(f"./build/{db_name}", exist_ok=True)
    
    try:
        server_process = subprocess.Popen(
            f"{SERVER_EXEC} {db_name}",
            shell=True,
            stdout=subprocess.DEVNULL,
            stderr=subprocess.DEVNULL,
            preexec_fn=os.setsid  # 创建新的进程组
        )
        # 等待服务器启动
        time.sleep(2)
        return server_process
    except Exception as e:
        log_message(f"启动服务器失败: {e}", "ERROR")
        return None

def stop_rmdb_server(server_process):
    """停止RMDB服务器"""
    if server_process:
        log_message("关闭RMDB服务器...")
        try:
            # 向进程组发送SIGTERM信号
            os.killpg(os.getpgid(server_process.pid), signal.SIGTERM)
            server_process.wait(timeout=5)
            log_message("RMDB服务器已关闭")
        except subprocess.TimeoutExpired:
            log_message("服务器关闭超时，强制终止", "WARNING")
            os.killpg(os.getpgid(server_process.pid), signal.SIGKILL)
        except Exception as e:
            log_message(f"关闭服务器时出错: {e}", "ERROR")

def run_unit_test(test_name):
    """运行单元测试"""
    test_executable = f"./build/bin/{test_name}"
    
    if not os.path.exists(test_executable):
        log_message(f"测试可执行文件不存在: {test_executable}", "ERROR")
        return False
    
    log_message(f"运行单元测试: {test_name}")
    returncode, stdout, stderr = run_command(test_executable, timeout=300)
    
    if returncode == 0:
        log_message(f"单元测试 {test_name} 通过", "SUCCESS")
        return True
    else:
        log_message(f"单元测试 {test_name} 失败 (返回码: {returncode})", "ERROR")
        log_message(f"错误输出: {stderr}", "ERROR")
        return False

def compare_output_with_expected(db_name, expected_file):
    """比较实际输出与期望输出"""
    output_file = f"./build/{db_name}/output.txt"

    if not os.path.exists(output_file):
        log_message(f"输出文件不存在: {output_file}", "ERROR")
        return False

    if not os.path.exists(expected_file):
        log_message(f"期望输出文件不存在: {expected_file}", "WARNING")
        return True  # 如果没有期望输出文件，认为测试通过

    try:
        with open(output_file, 'r') as f:
            actual_lines = [line.strip() for line in f.readlines()]

        with open(expected_file, 'r') as f:
            expected_lines = [line.strip() for line in f.readlines()]

        # 比较输出
        if actual_lines == expected_lines:
            log_message("输出与期望一致", "SUCCESS")
            return True
        else:
            log_message("输出与期望不一致", "ERROR")
            log_message(f"实际输出行数: {len(actual_lines)}, 期望输出行数: {len(expected_lines)}")

            # 显示前几行差异
            for i in range(min(5, max(len(actual_lines), len(expected_lines)))):
                actual = actual_lines[i] if i < len(actual_lines) else "(空行)"
                expected = expected_lines[i] if i < len(expected_lines) else "(空行)"
                if actual != expected:
                    log_message(f"第{i+1}行差异 - 实际: '{actual}', 期望: '{expected}'", "ERROR")

            return False

    except Exception as e:
        log_message(f"比较输出时出错: {e}", "ERROR")
        return False

def run_sql_test(sql_file, db_name):
    """运行SQL测试"""
    if not os.path.exists(sql_file):
        log_message(f"SQL文件不存在: {sql_file}", "ERROR")
        return False

    # 启动服务器
    server_process = start_rmdb_server(db_name)
    if not server_process:
        return False

    try:
        log_message(f"运行SQL测试: {sql_file}")

        # 读取SQL命令
        with open(sql_file, 'r') as f:
            sql_commands = [line.strip() for line in f if line.strip() and not line.strip().startswith('--')]

        if not sql_commands:
            log_message(f"SQL文件为空: {sql_file}", "WARNING")
            return False

        # 启动客户端
        child = pexpect.spawn(CLIENT_EXEC, encoding='utf-8')
        child.logfile = None  # 不直接输出到stdout

        try:
            # 等待客户端连接
            child.expect('Rucbase> ', timeout=10)
            log_message("成功连接到数据库客户端")

            # 执行SQL命令
            for idx, sql in enumerate(sql_commands, 1):
                log_message(f"执行SQL命令 {idx}/{len(sql_commands)}: {sql}")
                child.sendline(sql)

                try:
                    child.expect('Rucbase> ', timeout=30)
                except pexpect.TIMEOUT:
                    log_message(f"SQL命令执行超时: {sql}", "ERROR")
                    continue

                # 检查输出是否有错误
                output = child.before
                if "ERROR" in output or "failed" in output.lower():
                    log_message(f"SQL命令执行失败: {sql}", "ERROR")
                    log_message(f"错误输出: {output}", "ERROR")

            # 检查是否有期望输出文件
            expected_file = sql_file.replace('.sql', '_output.txt')
            if not os.path.exists(expected_file):
                # 尝试其他可能的命名方式
                expected_file = sql_file.replace('.sql', '_answer.txt')

            # 比较输出
            output_match = compare_output_with_expected(db_name, expected_file)

            log_message(f"SQL测试 {sql_file} 完成")
            return output_match

        except pexpect.TIMEOUT:
            log_message("客户端连接超时", "ERROR")
            return False
        except pexpect.EOF:
            log_message("客户端意外退出", "ERROR")
            return False
        finally:
            child.close()

    finally:
        # 停止服务器
        stop_rmdb_server(server_process)

def run_topic_tests(topic_id, topic_info):
    """运行指定题目的所有测试"""
    log_message(f"\n===== 开始测试题目 {topic_id}: {topic_info['name']} =====")
    
    # 运行单元测试
    unit_tests_passed = 0
    unit_tests_total = len(topic_info['unit_tests'])
    
    if unit_tests_total > 0:
        log_message(f"运行 {unit_tests_total} 个单元测试...")
        
        for test_name in topic_info['unit_tests']:
            if run_unit_test(test_name):
                unit_tests_passed += 1
        
        log_message(f"单元测试结果: {unit_tests_passed}/{unit_tests_total} 通过")
    
    # 运行SQL测试
    sql_tests_passed = 0
    sql_tests_total = len(topic_info['sql_tests'])
    
    if sql_tests_total > 0:
        log_message(f"运行 {sql_tests_total} 个SQL测试...")
        
        # 为每个题目创建单独的数据库
        topic_db_name = f"{DB_NAME}_topic{topic_id}"
        
        for sql_file in topic_info['sql_tests']:
            full_sql_path = os.path.join(TEST_DIR, sql_file)
            if run_sql_test(full_sql_path, topic_db_name):
                sql_tests_passed += 1
        
        log_message(f"SQL测试结果: {sql_tests_passed}/{sql_tests_total} 通过")
    
    # 总结
    total_passed = unit_tests_passed + sql_tests_passed
    total_tests = unit_tests_total + sql_tests_total
    
    log_message(f"题目 {topic_id} 测试总结: {total_passed}/{total_tests} 通过")
    return total_passed, total_tests

def build_project():
    """构建项目"""
    log_message("开始构建项目...")

    # 构建服务端
    log_message("构建服务端...")
    returncode, stdout, stderr = run_command("make rmdb -j4", timeout=300, cwd="./build")
    if returncode != 0:
        log_message(f"服务端编译失败: {stderr}", "ERROR")
        return False

    # 构建客户端
    log_message("构建客户端...")
    returncode, stdout, stderr = run_command("make rmdb_client -j4", timeout=300, cwd="./rmdb_client/build")
    if returncode != 0:
        log_message(f"客户端编译失败: {stderr}", "ERROR")
        return False

    log_message("项目构建完成", "SUCCESS")
    return True

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="db2025-x1 全面测试整合脚本")
    parser.add_argument("--topic", type=int, help="仅测试指定题目 (1-9)")
    parser.add_argument("--unit-only", action="store_true", help="仅运行单元测试")
    parser.add_argument("--sql-only", action="store_true", help="仅运行SQL测试")
    parser.add_argument("--no-build", action="store_true", help="跳过构建步骤")
    parser.add_argument("--list-tests", action="store_true", help="列出所有发现的测试")
    args = parser.parse_args()

    # 初始化日志文件
    with open(LOG_FILE, "w") as f:
        f.write(f"db2025-x1 全面测试整合脚本 - 开始时间: {datetime.now()}\n\n")

    log_message("开始全面测试...")

    # 获取测试配置
    topics = get_topics_config()

    # 如果只是列出测试，则显示并退出
    if args.list_tests:
        log_message("发现的测试:")
        for topic_id, topic_info in topics.items():
            log_message(f"\n题目 {topic_id}: {topic_info['name']}")
            if topic_info['unit_tests']:
                log_message(f"  单元测试 ({len(topic_info['unit_tests'])}个):")
                for test in topic_info['unit_tests']:
                    log_message(f"    - {test}")
            if topic_info['sql_tests']:
                log_message(f"  SQL测试 ({len(topic_info['sql_tests'])}个):")
                for test in topic_info['sql_tests']:
                    log_message(f"    - {test}")
        return 0

    # 确定要测试的题目
    topics_to_test = [args.topic] if args.topic else range(1, 10)

    # 运行测试
    total_passed = 0
    total_tests = 0

    for topic_id in topics_to_test:
        if topic_id not in topics:
            log_message(f"无效的题目ID: {topic_id}", "ERROR")
            continue

        topic_info = topics[topic_id]

        # 根据命令行参数过滤测试类型
        if args.unit_only:
            topic_info = {**topic_info, "sql_tests": []}
        if args.sql_only:
            topic_info = {**topic_info, "unit_tests": []}

        # 如果需要构建且有相关测试
        if not args.no_build and (topic_info['unit_tests'] or topic_info['sql_tests']):
            if not build_project():
                log_message("项目构建失败，跳过此题目", "WARNING")
                continue

        # 运行测试
        passed, tests = run_topic_tests(topic_id, topic_info)
        total_passed += passed
        total_tests += tests

    # 总结
    log_message(f"\n===== 测试完成 =====")
    log_message(f"总体结果: {total_passed}/{total_tests} 通过")

    if total_passed == total_tests:
        log_message("所有测试通过!", "SUCCESS")
        return 0
    else:
        log_message(f"有 {total_tests - total_passed} 个测试失败", "WARNING")
        return 1

if __name__ == "__main__":
    sys.exit(main())
