# 第一题
2025题目一：存储管理
题目种类：内核代码填充

推荐知识点：存储管理的基本原理、文件存储组织、记录存储组织、缓冲区管理

代码框架：https://gitlab.eduxiji.net/csc1/csc-db/db2025/-/tree/main/rmdb



题目描述：

在大赛提供的代码框架的基础上，提供相关接口的实现，完成指定功能。本题目包含若干子任务，每个任务对应不同的测试点，只有通过相应测试点才可以获得相应的分数。

提示：

l 测试代码会调用指定接口来进行测试，参赛队伍不能修改已有的接口，也不能删除已有的数据结构及数据结构中的变量，但是可以增添新的接口、数据结构、变量。

l 大赛提供了项目结构文档来帮助参赛队伍理解代码框架，同时在代码注释中，也对数据结构及接口进行了说明，参赛队伍可以通过阅读代码注释来辅助理解代码框架。

1、磁盘管理器

在本任务中，参赛队伍需要实现磁盘管理器DiskManager的相关接口，磁盘管理器负责文件操作、读写页面等。在完成本任务之前，参赛队伍需要阅读项目结构文档中磁盘管理器的相关说明，以及代码框架中src/errors.h、src/storage/disk_manager.h、src/storage/disk_manager.cpp、src/common/config.h文件。

参赛队伍需要实现以下接口：

（1）void DiskManager::create_file(const std::string &path);

该接口的参数path为文件名，该接口的功能是创建文件，其文件名为path参数指定的文件名。

（2）void DiskManager::open_file(const std::string &path);

该接口的参数path为文件名，该接口的功能是打开文件名参数path指定的文件。

（3）void DiskManager::close_file(const std::string &path);

该接口的参数path为文件名，该接口的功能是关闭文件名参数path指定的文件。

（4）void DiskManager::destroy_file(const std::string &path);

该接口的参数path为文件名，该接口的功能是删除文件名参数path指定的文件。

（5）void DiskManager::write_page(int fd, page_id_t page_no, const char *offset, int num_bytes);

该接口负责在文件的指定页面写入指定长度的数据，该接口从指定页面的起始位置开始写入数据。

（6）void DiskManager::read_page(int fd, page_id_t page_no, char *offset, int num_bytes);

该接口需要从文件的指定页面读取指定长度的数据，该接口从指定页面的起始位置开始读取数据。

 

2、缓冲池管理器

在本任务中，参赛队伍需要实现缓冲池管理器BufferPoolManager和缓冲池替换策略Replacer相关的接口，缓冲池管理器负责管理缓冲池中的页面在内外存的交换，缓冲池替换策略主要负责缓冲区页面的淘汰和查找。在完成本任务之前，参赛队伍需要首先阅读项目结构文档中缓冲池管理器的相关说明，以及代码框架中src/storage和src/replacer文件夹下的代码文件。

 

对于缓冲池替换策略Replacer，参赛队伍需要实现一个Replacer的子类LRUReplacer，LRUReplacer实现了缓冲池页面替换的LRU策略，需要实现的接口如下：

(1) bool LRUReplacer::victim(frame_id_t* frame_id);

该接口的功能是选择并淘汰缓冲池中一个页面。如果成功找到要淘汰的页面，则函数返回值为true；否则，返回值为false。被淘汰的页面所在的帧由参数frame_id返回。

(2) void LRUReplacer::pin(frame_id_t frame_id);

该接口的功能是固定住一个帧中的页面，代表该页面正在使用，不可被换出，参数frame_id指定了帧的编号。

(3) void LRUReplacer::unpin(frame_id_t frame_id);

该接口的功能是取消固定一个帧中的页面，当该页面使用完毕时调用unpin函数取消对该页面的固定，参数frame_id指定了帧的编号。

对于缓冲池管理器BufferPoolManager，参赛队伍需要管理缓冲池中的页面，并对缓冲池进行并发控制，需要实现的接口如下：

(1) Page *BufferPoolManager::new_page(PageId *page_id);

该成员函数用于在缓冲池中申请创建一个新页面。如果创建新页面成功，则返回指向该页面的指针，同时通过参数page_id返回新建页面的编号。

(2) Page *BufferPoolManager::fetch_page(PageId page_id);

该成员函数用于获取缓冲池中的指定页面。待获取页面的编号由参数page_id给出。

(3) bool BufferPoolManager::find_victim_page(frame_id_t *frame_id);

当缓冲池中没有可用的空闲帧时，该成员函数用于寻找需要淘汰的页面。如果成功找到要淘汰的页面，则函数返回值为true；否则，返回值为false。被淘汰的页面所在的帧由参数frame_id返回。在实现这个成员函数时，需要调用LRUReplacer::victim函数。

(4) void BufferPoolManager::update_page(Page *page, PageId new_page_id, frame_id_t new_frame_id);

当缓冲池想要把某个帧中的页面置换为新页面或者删除该帧中的页面时，会调用update_page函数，该函数把指定帧中的原有页面刷入磁盘中，并将新页面和该帧建立映射，即更新页表。

(5) bool BufferPoolManager::unpin_page(PageId page_id, bool is_dirty);

当某个操作完成某个页面的使用之后，需要调用该函数将取消该操作对该页面的固定。当所有操作都完成该页面的使用之后，需要在Replacer中调用Unpin函数取消该页面的固定。

(6) bool BufferPoolManager::delete_page(PageId page_id);

用于在缓冲池中删除指定页面，同时将该页面所在的帧置为空闲帧。如果当前页面正在被某个操作使用，则该页面不能被删除。

(7) bool BufferPoolManager::flush_page(PageId page_id);

用于强制刷新缓冲池中的指定页面到磁盘上，无论该页是否正在被使用，或者是否为脏页，都需要把该页面的数据刷入磁盘中。

(8) void BufferPoolManager::flush_all_pages(int fd);

用于将指定文件中的存在于缓冲池的所有页面都刷新到磁盘。

提示：在缓冲池中，需要淘汰某个脏页时，需要将脏页写入磁盘。



3、记录管理器

在本任务中，参赛队伍需要填充记录管理器涉及的RmFileHandle类和RmScan类，RmFileHandle类负责对表的记录进行操作，RmScan类用于遍历表文件中存放的记录。

对于RmFileHandle类。在完成本文任务之前，参赛队伍需要阅读项目结构文档中记录管理器的相关说明，以及代码框架中src/record文件夹下的代码文件。

参赛队伍需要实现的接口如下：

(1) std::unique_ptr<RmRecord> RmFileHandle::get_record(const Rid &rid, Context *context) const;

该函数用于获取表中某一条指定位置的记录，每条记录由Rid来进行唯一标识。

(2) Rid RmFileHandle::insert_record(char *buf, Context *context);

该函数负责向表中插入一条新记录，在该函数中未指定记录的插入位置，参赛队伍需要选择一个空闲位置插入记录并同步更新表的元数据信息。

(3) void RmFileHandle::insert_record(const Rid &rid, char *buf);

该函数负责向表中的指定位置插入一条记录，该函数主要用于事务的回滚和系统故障恢复。

(4) void RmFileHandle::delete_record(const Rid &rid, Context *context);

该函数负责删除表中指定位置的记录。

(5) void RmFileHandle::update_record(const Rid &rid, char *buf, Context *context);

该函数负责把表中指定位置的记录更新为新的值。

对于RmScan类，参赛队伍需要实现的接口如下：

(1) RmScan(const RmFileHandle *file_handle);

该函数为RmScan类的构造函数，需要初始化相关成员变量。

(2) void RmScan::next() override;

该函数负责找到表文件中下一个存放了合法记录的位置。

(3) bool RmScan::is_end() const override;

该函数负责判断是否已经扫描到文件的末尾位置。

 

测试示例：

unit_test.cpp中提供了参考测试示例，最终测试包括但不限于unit_test.cpp中提供的测试。

# 第二题
2025题目二：查询执行
前置题目：存储管理

题目种类：基础功能

推荐知识点：逻辑查询优化、查询解析、基本算子实现、查询执行框架、元数据存储组织

代码框架：https://gitlab.eduxiji.net/csc1/csc-db/db2025/-/tree/main/rmdb



题目描述：
在实现题目一功能的基础上增加查询执行模块，使得系统支持通过测试需要的元数据管理、DDL语句、DQL语句、DML语句。

提示：

l 本题目提供了空缺的样例代码，参赛队伍可以选择进行补全，也可以选择自行构建。如果选择补全空缺的样例代码，需要实现“元数据管理与DDL语句”和“DQL语句和DML语句”。

l 本题目通过sql进行测试，分为五个测试点，它们依次是“尝试建表”、“单表插入与条件查询”、“单表更新与条件查询”、“单表删除与条件查询”、“连接查询”。

 

1、元数据管理与DDL语句

本任务要求参赛队伍对数据库的元数据进行管理，并实现基本的DDL语句，包括create table和drop table两种语句。大赛提供的框架中，与元数据管理和DDL语句相关的代码文件位于src/system文件夹下，代码框架中提供了create table语句的实现方式。

 

2、DQL语句和DML语句

DML语句要求实现基本的增删改，即insert、delete、update语句。DQL语句要求实现select语句。在完成本任务之前，参赛队伍需要首先阅读项目结构文档中查询解析、查询优化、查询执行的相关说明，以及代码框架中src/analyze、src/optimizer、src/execution文件夹下的代码文件，参赛队伍需要实现代码框架中未提供的功能，包括语义检查、查询执行计划的生成、执行算子等。

 

本题目通过SQL来进行测试，因此参赛队伍可以对代码进行重构。



测试示例：

测试点1: 尝试建表

测试示例：

create table t1(id int,name char(4));

show tables;

create table t2(id int);

show tables;

drop table t1;

show tables;

drop table t2;

show tables;

期待输出：

| Tables |

| t1 |

| Tables |

| t1 |

| t2 |

| Tables |

| t2 |

| Tables |

 

测试点2: 单表插入与条件查询

测试示例：

create table grade (name char(20),id int,score float);

insert into grade values ('Data Structure', 1, 90.5);

insert into grade values ('Data Structure', 2, 95.0);

insert into grade values ('Calculus', 2, 92.0);

insert into grade values ('Calculus', 1, 88.5);

select * from grade;

select score,name,id from grade where score > 90;

select id from grade where name = 'Data Structure';

select name from grade where id = 2 and score > 90;  

期待输出：

| name | id | score |

| Data Structure | 1 | 90.500000 |

| Data Structure | 2 | 95.000000 |

| Calculus | 2 | 92.000000 |

| Calculus | 1 | 88.500000 |

| score | name | id |

| 90.500000 | Data Structure | 1 |

| 95.000000 | Data Structure | 2 |

| 92.000000 | Calculus | 2 |

| id |

| 1 |

| 2 |

| name |

| Data Structure |

| Calculus |

 

测试点3：单表更新与条件查询

测试示例：

create table grade (name char(20),id int,score float);

insert into grade values ('Data Structure', 1, 90.5);

insert into grade values ('Data Structure', 2, 95.0);

insert into grade values ('Calculus', 2, 92.0);

insert into grade values ('Calculus', 1, 88.5);

select * from grade;

update grade set score =  90 where name = 'Calculus' ;

select * from grade;

update grade set name = 'Error name' where name > 'A';

select * from grade;

update grade set name = 'Error' ,id = -1,score = 0 where name = 'Error name' and score >= 90;

select * from grade;

期待输出：

| name | id | score |

| Data Structure | 1 | 90.500000 |

| Data Structure | 2 | 95.000000 |

| Calculus | 2 | 92.000000 |

| Calculus | 1 | 88.500000 |

| name | id | score |

| Data Structure | 1 | 90.500000 |

| Data Structure | 2 | 95.000000 |

| Calculus | 2 | 90.000000 |

| Calculus | 1 | 90.000000 |

| name | id | score |

| Error name | 1 | 90.500000 |

| Error name | 2 | 95.000000 |

| Error name | 2 | 90.000000 |

| Error name | 1 | 90.000000 |

| name | id | score |

| Error | -1 | 0.000000 |

| Error | -1 | 0.000000 |

| Error | -1 | 0.000000 |

| Error | -1 | 0.000000 |

 

测试点4：单表删除与条件查询

测试示例：

create table grade (name char(20),id int,score float);

insert into grade values ('Data Structure', 1, 90.5);

select * from grade;

delete from grade where score > 90;

select * from grade;

期待输出：

| name | id | score|

| Data Structure | 1 | 90.500000 |

| name | id | score|

 

测试点5：连接查询

测试示例：

create table t ( id int , t_name char (3));

create table d (d_name char(5),id int);

insert into t values (1,'aaa');

insert into t values (2,'baa');

insert into t values (3,'bba');

insert into d values ('12345',1);

insert into d values ('23456',2);

select * from t, d;

select t.id,t_name,d_name from t,d where t.id = d.id;

select t.id,t_name,d_name from t join d where t.id = d.id;

期待输出：

| id | t_name | d_name | id |

| 1 | aaa | 23456 | 2 |

| 1 | aaa | 12345 | 1 |

| 2 | baa | 23456 | 2 |

| 2 | baa | 12345 | 1 |

| 3 | bba | 23456 | 2 |

| 3 | bba | 12345 | 1 |

| id | t_name | d_name |

| 1 | aaa | 12345 |

| 2 | baa | 23456 |

| id | t_name | d_name |

| 1 | aaa | 12345 |

| 2 | baa | 23456 |

 

测试输出要求：

本题目的输出要求写入数据库文件夹下的output.txt文件中，例如测试数据库名称为execution_test_db，则在测试时使用./bin/rmdb execution_test_db命令来启动服务端，对应输出应写入buid/execution_test_db/output.txt文件中。

# 第三题
2025题目三：唯一索引
前置题目：查询执行

题目种类：基础功能

推荐知识点：B+树索引

代码框架：https://gitlab.eduxiji.net/csc1/csc-db/db2025/-/tree/main/rmdb



题目描述：
在现有系统的基础上增添索引功能，该索引为唯一索引，要求系统能够支持创建索引、删除索引、展示某个表上的索引、单点查询和范围查询，实现索引与基表的同步。

如果一个表上的某个字段建有唯一索引，则该表中任意两个记录中该字段的值不相同。

 

提示：

（1）功能题目对代码框架没有限制，参赛队伍可以修改、增添、删除数据结构及接口，也可以对框架进行重构。

（2）磁盘数据库中常用的索引为B+树索引，推荐使用B+树来实现索引功能，参赛队伍也可以使用其他类型的索引，但需要能够支持题目中要求的功能。

（3）对于测试点2、3，即“索引的查询”和“维护索引的插入、删除、更新”，会进行单独的是否真正使用了索引的测试——对于数千条查询语句，建立索引后的执行时间应该小于建立索引前的执行时间的70%，才可以视为真正使用了索引的测试。

（4）改题目不要求使用B+树作为索引，所以对于参赛队伍测试点2、3的输出结果不要求行的顺序与期待输出的行的顺序完全一致，但列的顺序要求完全一致。

 

1、创建、删除、展示索引

（1）支持单个字段索引的创建和删除；

（2）支持多个字段索引的创建和删除；

（3）查看某个表上的索引信息；

（4）show index from table_name的输出格式为 | table_name | unique | (column_name, column_name) |。参考下方测试用例和期待输出

 

测试示例：

create table warehouse (id int, name char(8));

create index warehouse (id);

show index from warehouse;

create index warehouse (id,name);

show index from warehouse;

drop index warehouse (id);

drop index warehouse (id,name);

show index from warehouse;

期望输出： 

| warehouse | unique | (id) | // 第一个show index的输出

| warehouse | unique | (id) | // 第二个show index的输出

| warehouse | unique | (id,name) | // 第二个show index的输出

// 第三个show index没有输出

 

2、索引查询

在创建索引后，能够使用索引进行单点查询和范围查询。

 

提示：

在大赛提供的框架中，只有查询条件与索引完全一致，并且是单点查询时，才使用索引来进行查询，你需要对索引匹配规则进行修改，要求使用最左匹配原则。例如，表A的(id, name, score)三个属性上有一个联合索引，对于以下几种查询，都需要使用索引来进行查询：

l select * from A where id = 1 and name = 'abcd' and score = 99.0;

l select * from A where id = 1 and name = 'abcd' and score > 90.0;

l select * from A where id = 1 and name = 'abcd';

l select * from A where name = 'abcd' and id = 1;  // 在进行查询计划生成时，应能够自动对顺序进行调整

l select * from A where id = 1;

l select * from A where id > 1;

 

测试示例：

create table warehouse (w_id int, name char(8));

insert into warehouse values (10 , 'qweruiop');

insert into warehouse values (534, 'asdfhjkl');

insert into warehouse values (100,'qwerghjk');

insert into warehouse values (500,'bgtyhnmj');

create index warehouse(w_id);

select * from warehouse where w_id = 10;

select * from warehouse where w_id < 534 and w_id > 100;

drop index warehouse(w_id);

create index warehouse(name);

select * from warehouse where name = 'qweruiop';

select * from warehouse where name > 'qwerghjk';

select * from warehouse where name > 'aszdefgh' and name < 'qweraaaa';

drop index warehouse(name);

create index warehouse(w_id,name);

select * from warehouse where w_id = 100 and name = 'qwerghjk';

select * from warehouse where w_id < 600 and name > 'bztyhnmj';

期待输出：

| w_id | name |

| 10 | qweruiop |

| w_id | name |

| 500 | bgtyhnmj |

| w_id | name |

| 10 | qweruiop |

| w_id | name |

| 10 | qweruiop |

| w_id | name |

| 500 | bgtyhnmj |

| w_id | name |

| 100 | qwerghjk |

| w_id | name |

| 10 | qweruiop |

| 100 | qwerghjk |

 

 

3、索引维护

在建有索引的表中插入、删除、更新数据时，能够根据表数据的变化同步对表上对索引进行更新，保证索引的正确性。同时，在索引被更新时，需要检查唯一性约束。

 

测试示例：

create table warehouse (w_id int, name char(8));

insert into warehouse values (10 , 'qweruiop');

insert into warehouse values (534, 'asdfhjkl');

select * from warehouse where w_id = 10;

select * from warehouse where w_id < 534 and w_id > 100;

create index warehouse(w_id);

insert into warehouse values (500, 'lastdanc');

update warehouse set w_id = 507 where w_id = 534;

select * from warehouse where w_id = 10;

select * from warehouse where w_id < 534 and w_id > 100;

期待输出：

| w_id | name |

| 10 | qweruiop |

| w_id | name |

| w_id | name |

| 10 | qweruiop |

| w_id | name |

| 500 | lastdanc |

| 507 | asdfhjkl |

 

测试输出要求：

本题目的输出要求写入数据库文件夹下的output.txt文件中，例如测试数据库名称为index_test_db，则在测试时使用./bin/rmdb index_test_db命令来启动服务端，对应输出应写入buid/index_test_db/output.txt文件中。

# 第四题
2025题目四：查询优化
前置题目：查询执行
题目种类：基础功能

代码框架：https://gitlab.eduxiji.net/csc1/csc-db/db2025/-/tree/main/rmdb



题目描述:
查询优化是数据库系统中的重要组成部分，其目标是在保证查询结果正确性的前提下，通过改写 SQL 语句或调整执行计划，减少中间数据量、降低计算开销，从而提高查询执行效率。本题要求实现一个简化版的查询优化器，能够生成规范的查询计划树。

具体来说，我们要求实现以下三种优化：

1. 基于规则的选择运算下推（谓词下推）。选择运算下推要求在查询计划树中，利用选择运算与投影运算，以及选择运算与笛卡尔积的交换律等，通过交换节点在树中的位置，尽可能将选择运算对应的节点放到查询计划树的底层。其目的是：尽可能早地过滤掉与后续操作无关的行，避免中间结果生成后再进行过滤所带来的额外开销。选择运算下推有助于减少中间数据中不必要的行，降低中间数据的传递和处理量，从而提高查询效率。

2. 基于规则的投影运算下推。同上，投影运算下推要求在查询计划树中，利用投影运算与笛卡尔积的分配率，以及投影的串接定律等，通过交换节点在树中的位置，尽可能地先执行查询中的投影操作。其目的是：尽可能早地去除与后续操作无关的列，避免中间结果生成后再进行投影所带来的额外开销。投影运算下推有助于减少中间数据中不必要的列，可以显著降低数据传输和处理的开销。

3. 基于简单代价估计的连接顺序优化。连接顺序优化是指在涉及多表连接的查询中，通过调整表的连接顺序，减小中间结果的大小，以降低查询的执行代价。本题要求实现以下连接顺序优化策略：

a. 采用左深树结构组织多表连接

b. 使用贪心算法选择连接顺序，具体规则如下：

    - 首先选择基数最小的两个表作为初始连接

    - 之后每次从剩余表中选择一个表加入，使得连接后的中间结果基数最小

    - 表的基数指表中的行数，通过存储模块获取，需自行设计

c. 在连接顺序确定后，仍需应用选择运算下推和投影下推规则进行优化

 

对于本题，我们默认采用嵌套循环连接算法(Nested Loop Join)，并假设数据均匀分布。

 

要求：在现有SQL查询语句的基础上，增加对EXPLAIN关键字的支持，基于下列所述的规范格式，显示优化后的查询计划树内容。题目的输入为EXPLAIN + SQL查询语句，输出需要采用树状结构展示SQL查询计划，利用缩进 (在输出中为\t) 表示节点层级关系，每个节点包括算子类型和相关属性。

 

本题目共需实现四种查询计划节点：

- Scan：表扫描节点；- Filter：过滤节点（对应选择运算）；- Project：投影节点；- Join：连接节点

具体算子规范
节点类型

格式

备注

Scan节点

Scan(table=表名)

即叶节点

Filter节点

Filter(condition=[条件1,条件2,...])

1.确保谓词的左值是表的列，多个条件按字典序排序

2.条件中的列名必须包含表名前缀（多表查询时）

Project节点

Project(columns=[表名1.列名1,表名2.列名2,...])

1.列名按字母顺序排序

2.多表查询时使用表名前缀

3.需要保留全部的列则输出Project(columns=[*])

4.对于select语句根节点一定是Project节点

Join节点

Join(tables=[表名1,表名2,...], condition=[条件1,条件2,...])

1.表名按字母顺序排序，表名为此节点下所有表的集合

2.连接条件按字典序排序

 

同一层级下的多个节点，按照字典升序输出，即先按照Filter Join Project Scan的顺序输出，同类型的节点，Scan节点和Join节点按照table升序输出，Filter按照条件的字典序升序输出，Project节点按照“表名.列名”升序输出。所有的列输出时均要先输出各自的表名，如果表有别名，则使用别名。Condition的输出格式和原始的SQL语句保持一致。

 


测试示例:
输入：

EXPLAIN SELECT a, b FROM t WHERE a > 1 AND b < 10;

输出：

Project(columns=[t.a,t.b])

    Filter(condition=[t.a>1,t.b<10])

        Scan(table=t)

 



测试点1：选择运算下推

测试示例：

CREATE TABLE orders (

    order_id int,

    customer_id int,

    order_date char(40),

    total_amount float

);

CREATE TABLE customers (

    customer_id int,

    name char(50),

    email char(100),

    address char(200)

);

EXPLAIN SELECT * FROM customers c

JOIN orders o ON c.customer_id = o.customer_id

WHERE o.total_amount > 1000;

 

期望输出：

Project(columns=[*])

    Join(tables=[customers,orders],condition=[c.customer_id=o.customer_id])

       Filter(condition=[o.total_amount>1000])

            Scan(table=orders) 

Scan(table=customers)

        

 

测试点2：投影下推 

测试示例：

CREATE TABLE orders (

    order_id int,

    customer_id int,

    order_date char(40),

    total_amount float

);

CREATE TABLE customers (

    customer_id int,

    name char(50),

    email char(100),

    address char(200)

);

EXPLAIN SELECT c.name, o.order_id 

FROM customers c 

JOIN orders o ON c.customer_id = o.customer_id;

 

期望输出：

Project(columns=[c.name,o.order_id])

    Join(tables=[customers,orders],condition=[c.customer_id=o.customer_id])

        Project(columns=[c.customer_id,c.name])

            Scan(table=customers)

        Project(columns=[o.customer_id,o.order_id])

            Scan(table=orders)

 

测试点3：连接顺序优化（在进行连接顺序优化后，需要支持选择运算下推和投影下推）

测试示例：

CREATE TABLE orders (

    order_id int,

    customer_id int,

    order_date char(40),

    total_amount float

);

CREATE TABLE products (

    product_id int,

    name char(50),

    price float

);

CREATE TABLE order_items (

    order_id int,

    product_id int,

    quantity int

);

 

注：此处省略表插入的完整过程，插入后的metadata如下

customers: 50 行记录

orders: 200 行记录

order_items: 1000 行记录

products: 20000 行记录

 

EXPLAIN SELECT * FROM customers c

JOIN orders o ON c.customer_id = o.customer_id

JOIN order_items oi ON o.order_id = oi.order_id

JOIN products p ON oi.product_id = p.product_id

WHERE o.total_amount > 200;

 

期望输出：

由于采用嵌套循环连接算法并假设数据均匀分布，选择根据表的条目从低到高进行连接。确定连接顺序后应用规则选择运算下推。

Project(columns=[*])

    Join(tables=[customers,order_items,orders,products],condition=[oi.product_id=p.product_id])

        Join(tables=[customers,order_items,orders],condition=[o.order_id=oi.order_id])

            Join(tables=[customers,orders],condition=[c.customer_id=o.customer_id])

                Filter(condition=[o.total_amount>200])

                    Scan(table=orders)

                Scan(table=customers)   

            Scan(table=order_items)

        Scan(table=products)


# 第五题
2025题目五：聚合函数与分组统计
前置题目：查询执行

题目种类：基础功能

代码框架：https://gitlab.eduxiji.net/csc1/csc-db/db2025/-/tree/main/rmdb



题目描述:

本题目要求实现聚合函数与分组统计。聚合函数：对一组值进行计算并返回单一的值，通过使用SQL聚合函数，可以确定数值集合的各种统计值。分组统计：将查询结果按照1个或者多个字段进行分组，字段值相同的为同一组。

 

要求：


l 聚合函数COUNT(expr)：其中expr可以是列名、"*"。count(*)统计元组个数，count(列名)统计一列中值的个数。该函数仅需支持int、float、char类型的字段。

l 聚合函数MAX(expr)：返回一列中的最大值。该函数仅需支持int、float类型的字段。

l 聚合函数MIN(expr)：返回一列中的最小值。该函数仅需支持int、float类型的字段。

l 聚合函数SUM(expr)：返回数值列的总数。该函数仅需支持int、float类型的字段。

l 聚合函数AVG(expr): 返回一列中的平均值。该函数仅需支持int、float类型的字段。

l 分组统计(group by、having)：group by子句根据一个或多个列对结果集进行分组，可以使用having子句对每一个分组按条件进行过滤。分组属性仅需支持int、float、char类型的字段。


l 排序统计：order by子句对最终结果按照一个或多个列进行排序，并可指定升序ASC或降序DESC，默认采用ASC。可以使用limit关键字限制查询结果的数量。




测试示例：

测试点1：单独使用聚合函数


测试示例:

create table grade (course char(20),id int,score float);

insert into grade values('DataStructure',1,95);

insert into grade values('DataStructure',2,93.5);

insert into grade values('DataStructure',4,87);

insert into grade values('DataStructure',3,85);

insert into grade values('DB',1,94);

insert into grade values('DB',2,74.5);

insert into grade values('DB',4,83);

insert into grade values('DB',3,87);

select MAX(id) as max_id from grade;

select MIN(score) as min_score from grade where course = 'DB';

select AVG(score) as avg_score from grade where course = 'DataStructure';

select COUNT(course) as course_num from grade;

select COUNT(*) as row_num from grade;

select COUNT(*) as row_num from grade where score < 60;

select SUM(score) as sum_score from grade where id = 1;

drop table grade;

期待输出：

| max_id |

| 4 |

| min_score |

| 74.500000 |

| avg_score |

| 90.125000 |

| course_num |

| 8 |

| row_num |

| 8 |

| row_num |

| 0 |

| sum_score |

| 189.000000 |

 

测试点2：聚合函数加分组统计


测试示例:

create table grade (course char(20),id int,score float);

insert into grade values('DataStructure',1,95);

insert into grade values('DataStructure',2,93.5);

insert into grade values('DataStructure',3,94.5);

insert into grade values('ComputerNetworks',1,99);

insert into grade values('ComputerNetworks',2,88.5);

insert into grade values('ComputerNetworks',3,92.5);

insert into grade values('C++',1,92);

insert into grade values('C++',2,89);

insert into grade values('C++',3,89.5);

select id,MAX(score) as max_score,MIN(score) as min_score,SUM(score) as sum_score from grade group by id;

select id,MAX(score) as max_score from grade group by id having COUNT(*) >  3;

insert into grade values ('ParallelCompute',1,100);

select id,MAX(score) as max_score from grade group by id having COUNT(*) >  3;

select id,MAX(score) as max_score,MIN(score) as min_score from grade group by id having COUNT(*) > 1 and MIN(score) > 88;

select course ,COUNT(*) as row_num , COUNT(id) as student_num , MAX(score) as top_score, MIN(score) as lowest_score from grade group by course;

select course, id, score from grade order by score desc;

drop table grade;

期待输出：

| id | max_score | min_score | sum_score |

| 1 |  99.000000 | 92.000000 | 286.000000 |

| 2 |  93.500000 | 88.500000 | 271.000000 |

| 3 |  94.500000 | 89.500000 | 276.500000 |

| id | max_score |

| id | max_score |

| 1 | 100.000000 |

| id | max_score | min_score |

| 1 | 100.000000 | 92.000000 |

| 2 | 93.500000 | 88.500000 |

| 3 | 94.500000 | 89.500000 |

| course | row_num | student_num | top_score | lowest_score |

| DataStructure | 3 | 3 | 95.000000 | 93.500000 |

| ComputerNetworks | 3 | 3 | 99.000000 | 88.500000 |

| C++ | 3 | 3 | 92.000000 | 89.000000 |

| ParallelCompute | 1 | 1 | 100.000000 | 100.000000 |

| course | id | score |

| ParallelCompute | 1 | 100.000000 |

| ComputerNetworks | 1 | 99.000000 |

| DataStructure | 1 | 95.000000 |

| DataStructure | 3 | 94.500000 |

| DataStructure | 2 | 93.500000 |

| ComputerNetworks | 3 | 92.500000 |

| C++ | 1 | 92.000000 |

| C++ | 3 | 89.500000 |

| C++ | 2 | 89.000000 |

| ComputerNetworks | 2 | 88.500000 |



测试点3：健壮性测试

测试示例:

create table grade (course char(20),id int,score float);

insert into grade values('DataStructure',1,95);

insert into grade values('DataStructure',2,93.5);

insert into grade values('DataStructure',3,94.5);

insert into grade values('ComputerNetworks',1,99);

insert into grade values('ComputerNetworks',2,88.5);

insert into grade values('ComputerNetworks',3,92.5);

-- SELECT 列表中不能出现没有在 GROUP BY 子句中的非聚集列

select id , score from grade group by course;

-- WHERE 子句中不能用聚集函数作为条件表达式

select id, MAX(score) as max_score from grade where MAX(score) > 90 group by id;

期待输出：

failure

failure

 

测试输出要求：

本题目的输出要求写入数据库文件夹下的output.txt文件中，例如测试数据库名称为execution_test_db，则在测试时使用./bin/rmdb execution_test_db命令来启动服务端，对应输出应写入buid/execution_test_db/output.txt文件中。


# 第六题
2025题目六：半连接 Semi Join
前置题目：查询执行

题目种类：基础功能

推荐知识点：连接操作、集合操作、关系代数（半连接概念）

代码框架：https://gitlab.eduxiji.net/csc1/csc-db/db2025/-/tree/main/rmdb


题目描述：
Semi Join（半连接）是一种特殊的连接操作。它返回左表中与右表基于连接条件有匹配的行，但最终结果只包含左表的列，并且对于左表中的每一行，即使在右表中有多个匹配项，也只返回一次（即不产生重复行）。Semi Join 主要用于检查左表中的行在右表中是否存在匹配记录，而不关心右表记录的具体内容或数量，也无需将右表列包含在最终结果中。

 

本任务要求参赛队伍实现支持SEMI JOIN关键字的查询。其基本语法形式为：

SELECT ... FROM table1 SEMI JOIN table2 ON join_condition;

 

需要注意以下几点：

1.  列选择：SELECT子句中只能选择 table1 (左表) 的列。如果尝试选择 table2(右表) 的列，应视为错误。

2.  空集处理：如果 table1 或 table2 为空，或者连接条件在 table2 中找不到任何匹配 table1 的行，则结果集为空。


测试示例：
测试数据准备：

create table departments (dept_id int, dept_name char(20));

create table employees (emp_id int, emp_name char(20), dept_id int, salary int);

 

insert into departments values(1, 'HR');

insert into departments values(2, 'Engineering');

insert into departments values(3, 'Sales');

insert into departments values(4, 'Marketing');

 

insert into employees values(101, 'Alice', 1, 70000);

insert into employees values(102, 'Bob', 2, 80000);

insert into employees values(103, 'Charlie', 2, 90000); -- Engineering has two employees

insert into employees values(104, 'David', 1, 75000); -- HR has two employees

-- Sales (dept_id 3) and Marketing (dept_id 4) have no employees in this setup

 

测试点1：基本的 Semi Join (查询有员工的部门)

测试示例：

select dept_id, dept_name from departments SEMI JOIN employees ON departments.dept_id = employees.dept_id;

期待输出：



| dept_id | dept_name |

| 1 | HR |

| 2 | Engineering |



*(说明：HR和Engineering部门因为在employees表中有对应的员工记录而被选中。即使一个部门有多个员工，该部门也只在结果中出现一次。Sales和Marketing部门因为没有员工，所以不出现在结果中。)*

 

测试点2：Semi Join 结果不受右表重复匹配影响 (仍然是查询有员工的部门)

测试示例：

(此测试点与测试点1使用相同的查询，旨在强调即使右表（employees）中一个部门有多个员工，左表（departments）的对应行也只输出一次。)

select dept_id, dept_name from departments SEMI JOIN employees ON departments.dept_id = employees.dept_id;

期待输出：

| dept_id | dept_name |

| 1 | HR |

| 2 | Engineering |

 

测试点3：Semi Join 右表为空或无匹配

测试示例：

create table projects (proj_id int, dept_id_assigned int);

-- projects 表为空

select dept_name from departments SEMI JOIN projects ON departments.dept_id = projects.dept_id_assigned;

 

insert into projects values(1001, 99); -- 插入一个与任何部门都无法匹配的项目

select dept_name from departments SEMI JOIN projects ON departments.dept_id = projects.dept_id_assigned;

 

期待输出：

| dept_name |

| dept_name |

(说明：两次查询结果均为空，只显示表头，因为projects表初始为空，或其后的数据无法与departments表匹配。)



测试点4：健壮性测试 - 选择右表列

测试示例：

select dept_name, emp_name from departments SEMI JOIN employees ON departments.dept_id = employees.dept_id;

期待输出：

failure

(说明：尝试选择右表 `employees` 的 `emp_name` 列，这在 `SEMI JOIN` 中是不允许的。)



测试点5：健壮性测试 - 左表为空

测试示例：

create table empty_departments (dept_id int, dept_name char(20));

select dept_name from empty_departments SEMI JOIN employees ON empty_departments.dept_id = employees.dept_id;

期待输出：

| dept_name |

(说明：左表 `empty_departments` 为空，因此 Semi Join 结果也为空。)

# 第七题
2025题目七：事务控制语句
前置题目：查询执行、唯一索引(部分测试点涉及索引)

题目种类：基本功能

推荐知识点：事务的基本概念

代码框架：https://gitlab.eduxiji.net/csc1/csc-db/db2025/-/tree/main/rmdb



题目描述：

系统需要支持显示开启事务（begin）、提交事务（commit）、回滚事务（abort）三条事务控制语句，显式事务中只包含增删改查四种语句，不包含DDL语句。在大赛提供的框架中，单条语句被包装成一个单独的事务进行执行，参赛队伍可以自行更改。在本任务中，不需要考虑并发事务的执行，测试数据中不包含并发事务。

在完成本任务之前，参赛队伍可以首先阅读项目结构文档中事务管理器的相关说明，以及代码框架中src/transaction文件夹下的代码文件。

 

提示：功能题目对代码框架没有限制，参赛队伍可以在原有框架的基础上进行实现，也可以修改、增添、删除数据结构及接口，或者对框架进行重构。

 

测试示例：

本测试通过包含四个测试点，分别测试有索引和无索引情况下事务的提交与回滚，其中有索引的测试数据中包含时间类型，但不包含不合法的时间类型，测试数据为TPC-C中的NewOrder事务。测试语句格式如下：

create table student (id int, name char(8), score float);

insert into student values (1, 'xiaohong', 90.0);

begin;

insert into student values (2, 'xiaoming', 99.0);

delete from student where id = 2;

abort;

select * from student;

期待输出：

| id | name | score |

| 1 | xiaohong | 90.000000 |

 

测试输出要求：

本题目的输出要求写入数据库文件夹下的output.txt文件中，例如测试数据库名称为transaction_test_db，则在测试时使用./bin/rmdb transaction_test_db命令来启动服务端，对应输出应写入buid/transaction_test_db/output.txt文件中。


# 第八题
2025题目八：多版本并发控制(MVCC)
前置题目：查询执行、事务控制语句

题目种类：基础功能

推荐知识点：多版本并发控制的实现原理

代码框架：https://gitlab.eduxiji.net/csc1/csc-db/db2025/-/tree/main/rmdb



题目描述：

多版本并发控制(MVCC)是一种数据库并发控制技术，通过为每个事务维护数据的多个历史版本，实现快照隔离(Snapshot Isolation)级别的并发访问。MVCC意图解决读写锁造成的多个、长时间的读操作饿死写操作问题。每个事务读到的数据项都是一个历史快照，写操作不覆盖已有数据项，而是创建一个新的版本，直至所在操作提交时才变为可见。在MVCC模式下，读操作可以无锁地并发进行，每个事务只看到在其开始时已提交的数据版本，以及它自己写入的新版本。MVCC能够有效地减少传统基于锁的并发控制机制中读写操作之间的冲突和等待。

在本题中，你需要在RMDB中实现多版本并发控制(MVCC)方法，该方法需要满足以下要求：

1. 支持快照隔离(Snapshot Isolation)级别:

每个事务在开始时获得一个快照版本，该隔离级别保证事务的读操作将看到一个一致的数据库的版本快照。仅当基于该快照的任何并发修改与该事务的修改没有冲突时，该事务的写操作成功提交。

2. 解决写入冲突:在更新或删除tuple之前，需要检查是否存在以下的写入冲突情况：

(1) 事务A尝试更新一条元组时，发现该元组的最新时间戳属于另一个未提交的事务B。

(2) 事务A尝试更新一条元组时，发现该元组的最新时间戳属于另一个已提交的事务B，且该事务B的提交时间戳大于事务A的读时间戳。

注：1 关于MVCC的更多介绍可以参考教材《数据库管理系统原理与实现》第十一章以及CMU 15-445课程。本题目对MVCC的实现方式不作限制，代码仓库以及下面给出的提示针对增量表(delta table)的实现方式，如想采用其他方式实现，参赛队伍可以在原有框架的基础上进行实现，也可以修改、增添、删除数据结构及接口，或者对框架进行重构。

       2  在完成该题目之前，参赛队伍需要补充update语句功能，使得update语句支持如下表达式操作：

update student set score=score+5.5 where id<3;



实现方法提示：

1. 事务管理与时间戳分配：

(1) 实现事务的开始、提交、回滚。

(2) 事务开始时为每个事务分配该事务的读取时间戳，该时间戳为比该事务早的最后一次提交时间戳。

(3) 记录事务的提交时间戳，用于可见性判断。

2. 实现撤销日志(Undo Log)：

(1) 采用版本链的方式记录旧版本，更新或删除数据时，将可将撤销日志作为“版本增量”插入相应数据版本链当中以备回滚或并发可见性使用。

(2) 当事务需要读取该数据的旧版本时，便可以通过逐个回放撤销日志以达到此目的。

3. 算子支持： 提供并支持以下基本操作算子：

(1) 顺序扫描(Sequential Scan)：扫描表中所有记录。

(2) 插入(Insert)：向表中插入新记录。

(3) 更新(Update)：修改满足条件的记录的字段值。

(4) 删除(Delete)：删除满足条件的记录。

 



测试示例：

下面给出参考示例帮助了解MVCC的实现方式。假设所有SQL语句顺序在同一个会话中执行（系统可在内部模拟并发事务），结果输出写入 database/output.txt。输出表格仅为示例，实际输出应与格式要求一致。

示例 SQL（下面按逻辑分为 T1 和 T2 两个并发事务，实际执行顺序交错）：

-- 初始表数据

INSERT INTO student VALUES (1, 80);  -- 添加第一行

INSERT INTO student VALUES (2, 90);  -- 添加第二行


-- 事务 T1 开始

BEGIN;

SELECT * FROM student;

UPDATE student SET score=82 WHERE id=1;


-- 事务 T2 开始（T2 在 T1 未提交时启动）

BEGIN;

SELECT * FROM student;

UPDATE student SET score=95 WHERE id=2;

SELECT * FROM student WHERE id=2;

COMMIT;  -- 提交 T2


-- 回到 T1 继续

SELECT * FROM student;  -- T2 已提交，但T1由于快照隔离要求不能看见T2的提交修改

COMMIT;  -- 提交 T1


-- 最后查询

SELECT * FROM student;

说明：先往表中插入两行 (id=1,2)，然后启动事务T1修改id=1的score、同时启动事务T2修改id=2的score。T2提交后，T1还未提交，T1再次查询时仍应看到未包含 T2 的更改的版本。最后提交 T1 后，再次查询应看到所有改动。

期望输出表格： T1 初始 SELECT * 结果（T1 启动时表状态）：

 

+------------------+------------------+

|               id |            score |

+------------------+------------------+

|                1 |               80 |

|                2 |               90 |

+------------------+------------------+

Total record(s): 2

 

T2 初始 SELECT * 结果（T2 启动时，T1 的更新尚未提交）：

+------------------+------------------+

|               id |            score |

+------------------+------------------+

|                1 |               80 |

|                2 |               90 |

+------------------+------------------+

Total record(s): 2

 

T2 更新 id=2 后的 SELECT * WHERE id=2 结果（T2 事务内部）：

+------------------+------------------+

|               id |            score |

+------------------+------------------+

|                2 |               95 |

+------------------+------------------+

Total record(s): 1

 

T2 提交后，T1 再次 SELECT * 结果（T1 事务中，由于快照隔离，仍旧看到旧值）：

+------------------+------------------+

|               id |            score |

+------------------+------------------+

|                1 |               82 |

|                2 |               90 |

+------------------+------------------+

Total record(s): 2

 

所有事务提交后，最终 SELECT * 结果（全局最新状态）：

+------------------+------------------+

|               id |            score |

+------------------+------------------+

|                1 |               82 |

|                2 |               95 |

+------------------+------------------+

Total record(s): 2

 

上述测试展示了MVCC在并发事务下的正确性：事务只能读到某个特定时间点的历史版本（验证隔离性），提交后的更改对后续事务可见，而回滚则会丢弃修改。

# 第九题
2025题目九：基于静态检查点的故障恢复
前置题目：多版本并发控制(MVCC)

题目种类：基础功能

推荐知识点：故障恢复概述、基于REDO/UNDO日志的恢复算法、恢复算法ARIES

代码框架：https://gitlab.eduxiji.net/csc1/csc-db/db2025/-/tree/main/rmdb



题目描述：
本题目要求实现基于静态检查点的系统故障恢复。包含以下功能：

1. 日志管理器

2. WAL机制

3. 基础故障恢复

4. 语法功能：create static_checkpoint;命令创建静态检查点

5. 基于静态检查点的系统故障恢复

 

基础故障恢复功能需要实现日志管理器，在系统运行过程中把写操作的日志通过日志缓冲区写入磁盘中，参赛队伍需要实现WAL机制（Wirte-Ahead Log，即先写日志后写数据）和REDO/UNDO日志。在系统发生故障并重启之后，系统可以通过REDO/UNDO日志对系统进行故障恢复，让数据库系统恢复到一致性状态，基础故障恢复在系统重启恢复时从磁盘中的第一条日志开始扫描。

 

基于检查点的故障恢复是在基础故障恢复的一个优化，在创建检查点之后，系统故障恢复时从最新的一个检查点开始扫描日志。

 

具体的说，检查点技术是将数据库中的脏数据（即缓冲区中已经被修改但还没有写入磁盘的数据）写入磁盘，同时更新数据库的控制文件和日志文件，以便在数据库恢复时能够正确地恢复数据。检查点有多种实现方法，在本题目中，要求参赛队伍实现静态检查点。创建静态检查点时，系统需要停止接收新事务、停止正在运行的事务，并将当前数据库缓冲区中的脏页和日志刷入磁盘。当系统崩溃重启后，能够找到最近的检查点，并从该检查点开始将数据库恢复到一致性状态。

 

创建静态检查点的具体步骤如下：

（1）停止接收新事务和正在运行事务

（2）将仍保留在日志缓冲区中的内容写到日志文件中；

（3）在日志文件中写入一个“检查点记录”；

（4）将当前数据库缓冲区中的内容写到数据库中；

（5）把日志文件中检查点记录的地址写到“重新启动文件”中。

 

使用静态检查点做故障恢复的步骤如下：

（1）在重新开始文件中找到最后一个检查点在日志文件中的地址。将创建检查点时刻的所有事务加入undo list，而redo list暂时为空

（2）从检查点开始扫描各个日志，如果有新开始的事务则加入undo list，如果有事务提交，则从undo list删除，并加入redo list

（3）对于undo list中的所有事务做undo操作，对于redo list中的事务做redo操作

关于静态检查点的详细内容可以参考教材《数据库管理系统原理与实现》第12章故障恢复

 

测试示例：

不带检查点的系统故障恢复：

create table t1 (id int, num int);

begin;

insert into t1 values(1, 1);

commit;

begin;

insert into t1 values(2, 2);

crash // 系统接收到终止信号

...

重启系统

select * from t1;

 

带检查点的系统故障恢复：

create table t1 (id int, num int);

begin;

insert into t1 values(1, 1);

commit;

create static_checkpoint; // 创建静态检查点

begin;

insert into t1 values(2, 2);

crash // 系统接收到终止信号

...

重启系统

select * from t1;



测试点说明：


测试点	详细说明
crash_recovery_single_thread_test	单线程发送事务，数据量较小，不包括建立检查点
crash_recovery_multi_thread_test	多线程发送事务，数据量较小，不包括建立检查点
crash_recovery_index_test	单线程发送事务，包含建立索引，数据量较大，不包括建立检查点
crash_recovery_large_data_test	多线程发送事务，数据量较大，不包括建立检查点
crash_recovery_without_checkpoint	单线程发送事务，数据量巨大，不包括建立检查点，会记录系统故障恢复时间t1（注：恢复时间指从server重启开始，到server能够提供服务为止所需的时间）
crash_recovery_with_checkpoint	单线程发送事务，数据量同crash_recovery_without_checkpoint测试，在执行过程中，会不定时发送create static_checkpoint建立检查点，在系统crash之后会记录系统恢复时间t2，其中t2不超过t1的70%并且通过本题目的一致性检测，可认为通过本测试点


测试过程说明：

2.1 创建表，并插入一定量的数据

create table warehouse (w_id int, w_name char(10), w_street_1 char(20),w_street_2 char(20), w_city char(20), w_state char(2),
 w_zip char(9), w_tax float, w_ytd float);
create table district (d_id int, d_w_id int, d_name char(10), d_street_1 char(20), d_street_2 char(20),
 d_city char(20), d_state char(2), d_zip char(9),d_tax float, d_ytd float, d_next_o_id int);
create table customer (c_id int, c_d_id int, c_w_id int, c_first char(16),c_middle char(2),
 c_last char(16), c_street_1 char(20), c_street_2 char(20),c_city char(20), c_state char(2), 
 c_zip char(9), c_phone char(16), c_since char(30), c_credit char(2), 
 c_credit_lim int, c_discount float, c_balance float,c_ytd_payment float, 
 c_payment_cnt int, c_delivery_cnt int, c_data char(50));
create table history (h_c_id int, h_c_d_id int, h_c_w_id int, h_d_id int,
 h_w_id int, h_date char(19), h_amount float, h_data char(24));
create table new_orders (no_o_id int, no_d_id int, no_w_id int);
create table orders (o_id int, o_d_id int, o_w_id int, o_c_id int, o_entry_d char(19),
 o_carrier_id int, o_ol_cnt int, o_all_local int);
create table order_line ( ol_o_id int, ol_d_id int, ol_w_id int, ol_number int,
ol_i_id int, ol_supply_w_id int, ol_delivery_d char(30), ol_quantity int, ol_amount float, ol_dist_info char(24));
create table item (i_id int, i_im_id int, i_name char(24), i_price float, i_data char(50));
create table stock (s_i_id int, s_w_id int, s_quantity int, s_dist_01 char(24),s_dist_02 char(24),
 s_dist_03 char(24), s_dist_04 char(24), s_dist_05 char(24),s_dist_06 char(24), s_dist_07 char(24),
 s_dist_08 char(24), s_dist_09 char(24),s_dist_10 char(24), s_ytd float, s_order_cnt int, s_remote_cnt int, s_data char(50));
insert ...
insert ...
insert ...
2.2 执行事务


begin;
select c_discount, c_last, c_credit, w_tax from customer, warehouse where w_id=1 and c_w_id=w_id and c_d_id=1 and c_id=2;
select d_next_o_id, d_tax from district where d_id=1 and d_w_id=1;
update district set d_next_o_id=5 where d_id=1 and d_w_id=1;
insert into orders values (4, 1, 1, 2, '2023-06-03 19:25:47', 26, 5, 1);
insert into new_orders values (4, 1, 1);
select i_price, i_name, i_data from item where i_id=10;
select s_quantity, s_data, s_dist_01, s_dist_02, s_dist_03, s_dist_04, s_dist_05,s_dist_06, s_dist_07, s_dist_08, s_dist_09, s_dist_10 from stock where s_i_id=10 and s_w_id=1;
update stock set s_quantity=7 where s_i_id=10 and s_w_id=1;
insert into order_line values (4, 1, 1, 1, 10, 1, '2023-06-03 19:25:47', 7,286.625000, 'VF2uQHlDhtxa5dKhPwWyCqgY');
select i_price, i_name, i_data from item where i_id=10;
测试点6会随机创建checkpoint

create static_checkpoint
2.3 发生 crash

crash
2.4 重启server

./bin/rmdb checkpoint_test
2.5 由另外一个线程一直尝试重连，统计恢复时间（仅最后两个测试点需要）



std::string query = "select * from district;";
time_t start_time = now();
while(true) {
    ret = connect_database("rmdb"); // 尝试重连
    if(ret == 0) {
        if(run_query(query)) { // 如果重连成功并且执行事务成功，视为系统已经恢复完成
        break;
    }
    sleep(0.05); // 否则等待0.05s之后再次尝试
}
time_t end_time = now()
time_t recovery_time = end_time - start_time // 统计恢复时间
2.6 一致性检测（详见根目录下文档"数据一致性检验规则.md"），前五个测试点通过一致性测试即可拿到分数，最后一个测试点除了通过一致性测试之外，还需要故障恢复时间t2小于测试点5的故障恢复时间t1的70%