#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
调试SQL测试脚本
"""

import pexpect
import sys
import time
import subprocess
import signal
import os

def start_server(db_name):
    """启动服务器"""
    server_cmd = f"./build/bin/rmdb {db_name}"
    print(f"启动服务器: {server_cmd}")
    
    try:
        server_process = subprocess.Popen(
            server_cmd,
            shell=True,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            preexec_fn=os.setsid
        )
        time.sleep(3)
        return server_process
    except Exception as e:
        print(f"启动服务器失败: {e}")
        return None

def stop_server(server_process):
    """停止服务器"""
    if server_process:
        try:
            os.killpg(os.getpgid(server_process.pid), signal.SIGTERM)
            server_process.wait(timeout=3)
        except:
            try:
                os.killpg(os.getpgid(server_process.pid), signal.SIGKILL)
            except:
                pass

def test_sql_commands():
    """测试SQL命令"""
    db_name = "debug_test"
    
    # 清理旧数据库
    if os.path.exists(f"build/{db_name}"):
        os.system(f"rm -rf build/{db_name}")
    
    # 启动服务器
    server = start_server(db_name)
    if not server:
        print("无法启动服务器")
        return False
    
    try:
        # 连接客户端
        client = pexpect.spawn("./rmdb_client/build/rmdb_client", encoding='utf-8')
        client.logfile = sys.stdout
        
        try:
            # 等待连接
            client.expect('Rucbase> ', timeout=10)
            print("\n成功连接到客户端")
            
            # 测试简单的SQL命令
            test_commands = [
                "show tables;",
                "create table t1(id int, name char(4));",
                "show tables;",
                "create table t2(id int);", 
                "show tables;",
                "drop table t1;",
                "show tables;",
                "drop table t2;",
                "show tables;"
            ]
            
            for i, cmd in enumerate(test_commands, 1):
                print(f"\n===== 执行命令 {i}: {cmd} =====")
                client.sendline(cmd)
                
                try:
                    client.expect('Rucbase> ', timeout=10)
                    print(f"命令 {i} 执行成功")
                except pexpect.TIMEOUT:
                    print(f"命令 {i} 执行超时")
                    break
                except pexpect.EOF:
                    print(f"命令 {i} 执行后客户端退出")
                    break
            
            # 退出客户端
            client.sendline("exit;")
            client.close()
            
            print("\n测试完成")
            return True
            
        except pexpect.TIMEOUT:
            print("客户端连接超时")
            return False
        except pexpect.EOF:
            print("客户端意外退出")
            return False
        finally:
            client.close()
    
    finally:
        # 停止服务器
        stop_server(server)
        
        # 检查输出文件
        output_file = f"build/{db_name}/output.txt"
        if os.path.exists(output_file):
            print(f"\n输出文件内容:")
            with open(output_file, 'r') as f:
                print(f.read())
        else:
            print(f"输出文件不存在: {output_file}")

if __name__ == "__main__":
    test_sql_commands()
