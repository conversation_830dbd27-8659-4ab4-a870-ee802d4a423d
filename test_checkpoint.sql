-- 测试静态检查点功能
-- 创建测试表
create table test_recovery (id int, name char(20), value int);

-- 开始事务并插入数据
begin;
insert into test_recovery values (1, 'first', 100);
insert into test_recovery values (2, 'second', 200);
commit;

-- 创建静态检查点
create static_checkpoint;

-- 再次插入数据（这些数据在检查点之后）
begin;
insert into test_recovery values (3, 'third', 300);
insert into test_recovery values (4, 'fourth', 400);
commit;

-- 查看所有数据
select * from test_recovery;
