# 题目九：基于静态检查点的故障恢复 - 详细测试样例

## 测试点概述

根据题目描述，题目九共有6个测试点：

1. **crash_recovery_single_thread_test** - 单线程，小数据量，无检查点
2. **crash_recovery_multi_thread_test** - 多线程，小数据量，无检查点  
3. **crash_recovery_index_test** - 单线程，大数据量，有索引，无检查点
4. **crash_recovery_large_data_test** - 多线程，大数据量，无检查点
5. **crash_recovery_without_checkpoint** - 单线程，巨大数据量，无检查点，记录恢复时间t1
6. **crash_recovery_with_checkpoint** - 单线程，巨大数据量，有检查点，记录恢复时间t2，要求t2 ≤ 0.7*t1

## 详细测试样例

### 测试样例1：基础故障恢复（不带检查点）

#### 操作步骤：
```bash
# 1. 启动服务器
cd build
./bin/rmdb test_basic_recovery

# 2. 启动客户端
cd rmdb_client/build
./rmdb_client
```

#### SQL操作序列：
```sql
-- 创建测试表
create table t1 (id int, num int);

-- 第一个事务：插入数据并提交
begin;
insert into t1 values(1, 1);
commit;

-- 第二个事务：插入数据但不提交（模拟crash前的未完成事务）
begin;
insert into t1 values(2, 2);
-- 这里不执行commit，直接模拟crash
```

#### 模拟Crash：
```bash
# 强制终止服务器进程（模拟系统crash）
kill -9 <server_pid>
```

#### 重启和恢复：
```bash
# 重新启动服务器
./bin/rmdb test_basic_recovery
```

#### 期待输出：
```
Starting crash recovery...
No checkpoint found, starting from beginning
Analyzing log records...
Redoing committed transactions...
Undoing uncommitted transactions...
Crash recovery completed successfully in X ms
```

#### 验证结果：
```sql
select * from t1;
```

#### 期待查询结果：
```
+------------------+------------------+
|               id |              num |
+------------------+------------------+
|                1 |                1 |
+------------------+------------------+
Total record(s): 1
```
**说明**: 只有第一个已提交的事务数据被恢复，第二个未提交的事务被回滚。

---

### 测试样例2：带检查点的故障恢复

#### SQL操作序列：
```sql
-- 创建测试表
create table t1 (id int, num int);

-- 第一个事务：插入数据并提交
begin;
insert into t1 values(1, 1);
commit;

-- 创建静态检查点
create static_checkpoint;

-- 第二个事务：插入数据但不提交
begin;
insert into t1 values(2, 2);
-- 不commit，模拟crash
```

#### 期待检查点创建输出：
```
Static checkpoint created successfully.
```

#### 模拟Crash和重启后的期待输出：
```
Starting crash recovery...
Found checkpoint at LSN: X
Analyzing log records...
Redoing committed transactions...
Undoing uncommitted transactions...
Crash recovery completed successfully in Y ms
```

#### 验证结果：
```sql
select * from t1;
```

#### 期待查询结果：
```
+------------------+------------------+
|               id |              num |
+------------------+------------------+
|                1 |                1 |
+------------------+------------------+
Total record(s): 1
```
**说明**: 检查点前的已提交数据被恢复，检查点后的未提交事务被回滚。

---

### 测试样例3：复杂事务场景（题目提供的标准测试）

#### 创建完整表结构：
```sql
create table warehouse (w_id int, w_name char(10), w_street_1 char(20), w_street_2 char(20), w_city char(20), w_state char(2), w_zip char(9), w_tax float, w_ytd float);

create table district (d_id int, d_w_id int, d_name char(10), d_street_1 char(20), d_street_2 char(20), d_city char(20), d_state char(2), d_zip char(9), d_tax float, d_ytd float, d_next_o_id int);

create table customer (c_id int, c_d_id int, c_w_id int, c_first char(16), c_middle char(2), c_last char(16), c_street_1 char(20), c_street_2 char(20), c_city char(20), c_state char(2), c_zip char(9), c_phone char(16), c_since char(30), c_credit char(2), c_credit_lim int, c_discount float, c_balance float, c_ytd_payment float, c_payment_cnt int, c_delivery_cnt int, c_data char(50));

create table history (h_c_id int, h_c_d_id int, h_c_w_id int, h_d_id int, h_w_id int, h_date char(19), h_amount float, h_data char(24));

create table new_orders (no_o_id int, no_d_id int, no_w_id int);

create table orders (o_id int, o_d_id int, o_w_id int, o_c_id int, o_entry_d char(19), o_carrier_id int, o_ol_cnt int, o_all_local int);

create table order_line (ol_o_id int, ol_d_id int, ol_w_id int, ol_number int, ol_i_id int, ol_supply_w_id int, ol_delivery_d char(30), ol_quantity int, ol_amount float, ol_dist_info char(24));

create table item (i_id int, i_im_id int, i_name char(24), i_price float, i_data char(50));

create table stock (s_i_id int, s_w_id int, s_quantity int, s_dist_01 char(24), s_dist_02 char(24), s_dist_03 char(24), s_dist_04 char(24), s_dist_05 char(24), s_dist_06 char(24), s_dist_07 char(24), s_dist_08 char(24), s_dist_09 char(24), s_dist_10 char(24), s_ytd float, s_order_cnt int, s_remote_cnt int, s_data char(50));
```

#### 插入初始数据：
```sql
begin;
insert into warehouse values (1, 'warehouse1', 'street1', 'street2', 'city1', 'CA', '12345', 0.08, 300000.0);
insert into district values (1, 1, 'district1', 'street1', 'street2', 'city1', 'CA', '12345', 0.1, 30000.0, 4);
insert into customer values (2, 1, 1, 'John', 'OE', 'Doe', 'street1', 'street2', 'city1', 'CA', '12345', '1234567890', '2023-06-03 19:25:47', 'GC', 50000, 0.1, 1000.0, 500.0, 10, 5, 'customer data');
insert into item values (10, 1, 'item name', 28.66, 'item data');
insert into stock values (10, 1, 15, 'dist01', 'dist02', 'dist03', 'dist04', 'dist05', 'dist06', 'dist07', 'dist08', 'dist09', 'dist10', 100.0, 5, 2, 'stock data');
commit;
```

#### 创建检查点：
```sql
create static_checkpoint;
```

#### 执行复杂事务：
```sql
begin;
select c_discount, c_last, c_credit, w_tax from customer, warehouse where w_id=1 and c_w_id=w_id and c_d_id=1 and c_id=2;
select d_next_o_id, d_tax from district where d_id=1 and d_w_id=1;
update district set d_next_o_id=5 where d_id=1 and d_w_id=1;
insert into orders values (4, 1, 1, 2, '2023-06-03 19:25:47', 26, 5, 1);
insert into new_orders values (4, 1, 1);
select i_price, i_name, i_data from item where i_id=10;
select s_quantity, s_data, s_dist_01, s_dist_02, s_dist_03, s_dist_04, s_dist_05, s_dist_06, s_dist_07, s_dist_08, s_dist_09, s_dist_10 from stock where s_i_id=10 and s_w_id=1;
update stock set s_quantity=7 where s_i_id=10 and s_w_id=1;
insert into order_line values (4, 1, 1, 1, 10, 1, '2023-06-03 19:25:47', 7, 286.625000, 'VF2uQHlDhtxa5dKhPwWyCqgY');
select i_price, i_name, i_data from item where i_id=10;
commit;
```

#### 期待的查询结果示例：
```sql
select d_next_o_id, d_tax from district where d_id=1 and d_w_id=1;
```
期待输出：
```
+------------------+------------------+
|      d_next_o_id |            d_tax |
+------------------+------------------+
|                5 |         0.100000 |
+------------------+------------------+
```

---

### 测试样例4：性能对比测试

#### 测试点5（无检查点）操作：
1. 创建大量数据（数千条记录）
2. 执行大量事务操作
3. 模拟crash
4. 记录恢复时间t1

#### 测试点6（有检查点）操作：
1. 创建相同数量的数据
2. 在中间过程中创建多个检查点
3. 模拟crash
4. 记录恢复时间t2

#### 期待性能结果：
```
测试点5恢复时间: t1 = X ms
测试点6恢复时间: t2 = Y ms
性能提升: t2 ≤ 0.7 * t1 (至少30%提升)
```

---

## 关键验证点

### 1. 语法功能验证
```sql
create static_checkpoint;
```
期待输出：`Static checkpoint created successfully.`

### 2. 检查点文件验证
```bash
# 检查重启文件是否创建
ls -la <db_name>/db.restart
# 检查日志文件是否有内容
ls -la <db_name>/db.log
```

### 3. 恢复时间验证
- 系统启动时显示恢复时间
- 检查点恢复应该比全日志恢复快

### 4. 数据一致性验证
- 已提交事务的数据应该被恢复
- 未提交事务的数据应该被回滚
- 检查点前后的数据状态应该正确

## 快速测试步骤

### 最简单的验证方法：

#### 步骤1：基础功能测试
```bash
# 1. 启动服务器
cd build
./bin/rmdb quick_test

# 2. 启动客户端（新终端）
cd rmdb_client/build
./rmdb_client

# 3. 执行基础测试
create table test (id int, name char(10));
begin;
insert into test values(1, 'test1');
commit;
create static_checkpoint;  # 应该显示成功信息
begin;
insert into test values(2, 'test2');
# 不要commit，直接Ctrl+C关闭服务器

# 4. 重启服务器，观察恢复信息
./bin/rmdb quick_test
# 应该显示: Found checkpoint at LSN: X

# 5. 验证数据
select * from test;
# 应该只有(1, 'test1')一条记录
```

#### 步骤2：性能对比测试
```bash
# 测试无检查点恢复时间
# 创建大量数据后crash，记录恢复时间t1

# 测试有检查点恢复时间
# 创建相同数据，中间建检查点，crash后记录恢复时间t2
# 验证: t2 < 0.7 * t1
```

## 测试执行建议

1. **按顺序执行测试点**：从简单到复杂
2. **验证每个功能点**：确保语法、检查点、恢复都正常
3. **记录性能数据**：特别是最后两个测试点的恢复时间
4. **检查日志文件**：确认WAL机制正常工作
5. **验证数据一致性**：每次恢复后检查数据正确性

## 使用提供的测试脚本

```bash
# 使用shell脚本测试
chmod +x run_test_case_9.sh
./run_test_case_9.sh 1  # 测试点1

# 使用Python交互式测试
python3 interactive_test_9.py
```
