[1mdiff --git a/build/CMakeCache.txt b/build/CMakeCache.txt[m
[1mindex 9a7ecf6..b57e86e 100644[m
[1m--- a/build/CMakeCache.txt[m
[1m+++ b/build/CMakeCache.txt[m
[36m@@ -1,5 +1,5 @@[m
 # This is the CMakeCache file.[m
[31m-# For build in directory: /root/dbms5/db2025-x1/build[m
[32m+[m[32m# For build in directory: /home/<USER>/db2025-x1/build[m
 # It was generated by CMake: /usr/bin/cmake[m
 # You can edit this file to change values found and used by cmake.[m
 # If you do not want to change any of the values, simply exit the editor.[m
[36m@@ -28,7 +28,7 @@[m [mCMAKE_AR:FILEPATH=/usr/bin/ar[m
 [m
 //Choose the type of build, options are: None Debug Release RelWithDebInfo[m
 // MinSizeRel ...[m
[31m-CMAKE_BUILD_TYPE:STRING=Debug[m
[32m+[m[32mCMAKE_BUILD_TYPE:STRING=[m
 [m
 //Enable/Disable color output during build.[m
 CMAKE_COLOR_MAKEFILE:BOOL=ON[m
[36m@@ -292,16 +292,16 @@[m [mFL_LIBRARY:FILEPATH=/usr/lib/x86_64-linux-gnu/libfl.so[m
 INSTALL_GTEST:BOOL=ON[m
 [m
 //Value Computed by CMake[m
[31m-RMDB_BINARY_DIR:STATIC=/root/dbms5/db2025-x1/build[m
[32m+[m[32mRMDB_BINARY_DIR:STATIC=/home/<USER>/db2025-x1/build[m
 [m
 //Value Computed by CMake[m
 RMDB_IS_TOP_LEVEL:STATIC=ON[m
 [m
 //Value Computed by CMake[m
[31m-RMDB_SOURCE_DIR:STATIC=/root/dbms5/db2025-x1[m
[32m+[m[32mRMDB_SOURCE_DIR:STATIC=/home/<USER>/db2025-x1[m
 [m
 //Value Computed by CMake[m
[31m-gmock_BINARY_DIR:STATIC=/root/dbms5/db2025-x1/build/deps/googletest/googlemock[m
[32m+[m[32mgmock_BINARY_DIR:STATIC=/home/<USER>/db2025-x1/build/deps/googletest/googlemock[m
 [m
 //Value Computed by CMake[m
 gmock_IS_TOP_LEVEL:STATIC=OFF[m
[36m@@ -310,7 +310,7 @@[m [mgmock_IS_TOP_LEVEL:STATIC=OFF[m
 gmock_LIB_DEPENDS:STATIC=general;gtest;[m
 [m
 //Value Computed by CMake[m
[31m-gmock_SOURCE_DIR:STATIC=/root/dbms5/db2025-x1/deps/googletest/googlemock[m
[32m+[m[32mgmock_SOURCE_DIR:STATIC=/home/<USER>/db2025-x1/deps/googletest/googlemock[m
 [m
 //Build all of Google Mock's own tests.[m
 gmock_build_tests:BOOL=OFF[m
[36m@@ -319,22 +319,22 @@[m [mgmock_build_tests:BOOL=OFF[m
 gmock_main_LIB_DEPENDS:STATIC=general;gmock;[m
 [m
 //Value Computed by CMake[m
[31m-googletest-distribution_BINARY_DIR:STATIC=/root/dbms5/db2025-x1/build/deps/googletest[m
[32m+[m[32mgoogletest-distribution_BINARY_DIR:STATIC=/home/<USER>/db2025-x1/build/deps/googletest[m
 [m
 //Value Computed by CMake[m
 googletest-distribution_IS_TOP_LEVEL:STATIC=OFF[m
 [m
 //Value Computed by CMake[m
[31m-googletest-distribution_SOURCE_DIR:STATIC=/root/dbms5/db2025-x1/deps/googletest[m
[32m+[m[32mgoogletest-distribution_SOURCE_DIR:STATIC=/home/<USER>/db2025-x1/deps/googletest[m
 [m
 //Value Computed by CMake[m
[31m-gtest_BINARY_DIR:STATIC=/root/dbms5/db2025-x1/build/deps/googletest/googletest[m
[32m+[m[32mgtest_BINARY_DIR:STATIC=/home/<USER>/db2025-x1/build/deps/googletest/googletest[m
 [m
 //Value Computed by CMake[m
 gtest_IS_TOP_LEVEL:STATIC=OFF[m
 [m
 //Value Computed by CMake[m
[31m-gtest_SOURCE_DIR:STATIC=/root/dbms5/db2025-x1/deps/googletest/googletest[m
[32m+[m[32mgtest_SOURCE_DIR:STATIC=/home/<USER>/db2025-x1/deps/googletest/googletest[m
 [m
 //Build gtest's sample programs.[m
 gtest_build_samples:BOOL=OFF[m
[36m@@ -367,7 +367,7 @@[m [mCMAKE_ADDR2LINE-ADVANCED:INTERNAL=1[m
 //ADVANCED property for variable: CMAKE_AR[m
 CMAKE_AR-ADVANCED:INTERNAL=1[m
 //This is the directory where this CMakeCache.txt was created[m
[31m-CMAKE_CACHEFILE_DIR:INTERNAL=/root/dbms5/db2025-x1/build[m
[32m+[m[32mCMAKE_CACHEFILE_DIR:INTERNAL=/home/<USER>/db2025-x1/build[m
 //Major version of cmake used to create the current loaded cache[m
 CMAKE_CACHE_MAJOR_VERSION:INTERNAL=3[m
 //Minor version of cmake used to create the current loaded cache[m
[36m@@ -446,7 +446,7 @@[m [mCMAKE_HAVE_LIBC_PTHREAD:INTERNAL=1[m
 CMAKE_HAVE_PTHREAD_H:INTERNAL=1[m
 //Source directory with the top level CMakeLists.txt file for this[m
 // project[m
[31m-CMAKE_HOME_DIRECTORY:INTER