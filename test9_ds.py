#!/usr/bin/env python3
"""
评测脚本：题目九 - 基于静态检查点的故障恢复测试 (聚合函数版本 + 完整性增强)
- 2个隐藏测试点（abort / 未提交事务）
- 使用聚合函数检测一致性
- 支持日志输出方便调试
"""

import subprocess
import time
import os
import threading
import datetime

class FullRecoveryTester:
    def __init__(self):
        self.build_dir = "/home/<USER>/db2025-x2/db2025-x1-q9_zyy/db2025-x1-q9_zyy/build"
        self.client_dir = "/home/<USER>/db2025-x2/db2025-x1-q9_zyy/db2025-x1-q9_zyy/rmdb_client/build"
        self.server_process = None
        self.log_dir = "recovery_logs"
        os.makedirs(self.log_dir, exist_ok=True)

    def log_path(self, name):
        return os.path.join(self.log_dir, f"{name}.log")

    def cleanup(self):
        if self.server_process:
            try:
                self.server_process.terminate()
                self.server_process.wait(timeout=5)
            except:
                try:
                    self.server_process.kill()
                except:
                    pass
        try:
            subprocess.run("pkill -9 -x rmdb", shell=True, timeout=3)
            subprocess.run("pkill -9 -x rmdb_client", shell=True, timeout=3)
        except:
            pass
        time.sleep(1)

    def start_server(self, db_name, tag="run"):
        self.cleanup()
        subprocess.run(f"rm -rf {self.build_dir}/{db_name}", shell=True)
        cmd = f"./bin/rmdb {db_name}"
        log = open(self.log_path(f"server_{tag}"), "w")
        self.server_process = subprocess.Popen(
            cmd, shell=True, cwd=self.build_dir,
            stdout=log, stderr=log, text=True, bufsize=1
        )
        time.sleep(2)
        if self.server_process.poll() is not None:
            print(f"❌ 服务器启动失败 [{db_name}]，日志保存在 {log.name}")
            return False
        return True

    def run_client(self, commands, tag="client", timeout=30):
        cmd = "./rmdb_client"
        input_text = "\n".join(commands) + "\nexit\n"
        log_file = self.log_path(f"client_{tag}")
        with open(log_file, "w") as f:
            proc = subprocess.Popen(
                cmd, shell=True, cwd=self.client_dir,
                stdin=subprocess.PIPE, stdout=subprocess.PIPE,
                stderr=subprocess.STDOUT, text=True
            )
            try:
                out, _ = proc.communicate(input=input_text, timeout=timeout)
                f.write(out)
                return out
            except subprocess.TimeoutExpired:
                proc.kill()
                f.write("TIMEOUT\n")
                return "TIMEOUT"

    def check_consistency(self, tag="verify"):
        print("执行一致性校验（使用聚合函数）")
        cmd = [
            "select count(*) from new_orders;",
            "select max(no_o_id), min(no_o_id) from new_orders;",
            "select count(*) from order_line;",
            "select sum(o_ol_cnt) from orders;"
        ]
        results = self.run_client(cmd, tag=tag)
        if "ERROR" in results or "TIMEOUT" in results:
            print("❌ 查询执行失败，日志中可能有错误")
            return False
        try:
            lines = [l for l in results.splitlines() if "|" in l]
            count_new_orders = int(lines[0].split("|")[0].strip())
            max_no = int(lines[1].split("|")[0].strip())
            min_no = int(lines[1].split("|")[1].strip())
            count_order_line = int(lines[2].split("|")[0].strip())
            sum_ol_cnt = int(lines[3].split("|")[0].strip())
            if max_no - min_no + 1 != count_new_orders:
                print("❌ new_orders ID 不连续")
                return False
            if count_order_line != sum_ol_cnt:
                print("❌ order_line 总数不等于订单声明项数")
                return False
            print("✅ 一致性检查通过")
            return True
        except Exception as e:
            print(f"❌ 解析聚合结果失败: {e}")
            return False

    def test_abort_recovery(self):
        print("\n=== 测试点：abort 事务恢复 ===")
        if not self.start_server("test_abort", tag="abort_init"):
            return False
        setup = [
            "create table orders (o_id int, o_ol_cnt int);"
        ]
        self.run_client(setup, tag="abort_setup")
        tx = [
            "begin;",
            "insert into orders values (1, 2);",
            "abort;",
            "crash"
        ]
        self.run_client(tx, tag="abort_crash")
        if self.server_process:
            self.server_process.wait(timeout=5)
        if not self.start_server("test_abort", tag="abort_recover"):
            return False
        result = self.run_client(["select * from orders;"], tag="abort_verify")
        if "1" in result:
            print("❌ abort 后数据未被撤销")
            return False
        print("✅ abort 事务恢复正确")
        return True

    def test_uncommitted_crash(self):
        print("\n=== 测试点：未提交事务 crash 恢复 ===")
        if not self.start_server("test_uncommit", tag="uncommit_init"):
            return False
        setup = [
            "create table orders (o_id int, o_ol_cnt int);"
        ]
        self.run_client(setup, tag="uncommit_setup")
        tx = [
            "begin;",
            "insert into orders values (2, 3);",
            "crash"
        ]
        self.run_client(tx, tag="uncommit_crash")
        if self.server_process:
            self.server_process.wait(timeout=5)
        if not self.start_server("test_uncommit", tag="uncommit_recover"):
            return False
        result = self.run_client(["select * from orders;"], tag="uncommit_verify")
        if "2" in result:
            print("❌ 未提交事务数据未被撤销")
            return False
        print("✅ 未提交事务恢复正确")
        return True

    def run_all_tests(self):
        results = []
        results.append(("abort事务恢复", self.test_abort_recovery()))
        results.append(("未提交事务恢复", self.test_uncommitted_crash()))
        print("\n=== 测试结果汇总 ===")
        for name, passed in results:
            print(f"{name}: {'✅ 通过' if passed else '❌ 失败'}")

if __name__ == "__main__":
    tester = FullRecoveryTester()
    try:
        tester.run_all_tests()
    except Exception as e:
        print("测试执行异常:", e)
    finally:
        tester.cleanup()
