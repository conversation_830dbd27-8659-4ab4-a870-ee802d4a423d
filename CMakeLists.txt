include_directories(${CMAKE_CURRENT_SOURCE_DIR})
include_directories(${CMAKE_CURRENT_SOURCE_DIR}/src)

cmake_minimum_required(VERSION 3.16)
project(RMDB)

set(CMAKE_ARCHIVE_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/lib)
set(CMAKE_LIBRARY_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/lib)
set(CMAKE_RUNTIME_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/bin)

set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_FLAGS "-Wall -O0 -g -ggdb3")
# set(CMAKE_CXX_FLAGS "-Wall -O3")

set(CMAKE_CXX_FLAGS_DEBUG "${CMAKE_CXX_FLAGS_DEBUG} -O0 -g")
set(CMAKE_C_FLAGS_DEBUG "${CMAKE_C_FLAGS_DEBUG} -O0 -g")


enable_testing()
add_subdirectory(src)
add_subdirectory(deps)
# 添加测试用可执行文件

add_executable(b_plus_tree_insert_test src/b_plus_tree_insert_test.cpp)
target_link_libraries(b_plus_tree_insert_test lru_replacer record index gtest_main)


add_executable(b_plus_tree_delete_test src/b_plus_tree_delete_test.cpp)
target_link_libraries(b_plus_tree_delete_test lru_replacer record index gtest_main)

add_executable(b_plus_tree_concurrent_test src/b_plus_tree_concurrent_test.cpp)
target_link_libraries(b_plus_tree_concurrent_test lru_replacer record index gtest_main)