[{"directory": "/home/<USER>/db2025-x1/rmdb_client", "command": "/usr/bin/c++  -I/home/<USER>/db2025-x1 -I/home/<USER>/db2025-x1/src -isystem /home/<USER>/db2025-x1/deps/googletest/googletest/include -isystem /home/<USER>/db2025-x1/deps/googletest/googletest -Wall -O0 -g -ggdb3 -g -O0 -g -o CMakeFiles/b_plus_tree_insert_test.dir/src/b_plus_tree_insert_test.cpp.o -c /home/<USER>/db2025-x1/src/b_plus_tree_insert_test.cpp", "file": "/home/<USER>/db2025-x1/src/b_plus_tree_insert_test.cpp"}, {"directory": "/home/<USER>/db2025-x1/rmdb_client", "command": "/usr/bin/c++  -I/home/<USER>/db2025-x1 -I/home/<USER>/db2025-x1/src -isystem /home/<USER>/db2025-x1/deps/googletest/googletest/include -isystem /home/<USER>/db2025-x1/deps/googletest/googletest -Wall -O0 -g -ggdb3 -g -O0 -g -o CMakeFiles/b_plus_tree_delete_test.dir/src/b_plus_tree_delete_test.cpp.o -c /home/<USER>/db2025-x1/src/b_plus_tree_delete_test.cpp", "file": "/home/<USER>/db2025-x1/src/b_plus_tree_delete_test.cpp"}, {"directory": "/home/<USER>/db2025-x1/rmdb_client", "command": "/usr/bin/c++  -I/home/<USER>/db2025-x1 -I/home/<USER>/db2025-x1/src -isystem /home/<USER>/db2025-x1/deps/googletest/googletest/include -isystem /home/<USER>/db2025-x1/deps/googletest/googletest -Wall -O0 -g -ggdb3 -g -O0 -g -o CMakeFiles/b_plus_tree_concurrent_test.dir/src/b_plus_tree_concurrent_test.cpp.o -c /home/<USER>/db2025-x1/src/b_plus_tree_concurrent_test.cpp", "file": "/home/<USER>/db2025-x1/src/b_plus_tree_concurrent_test.cpp"}, {"directory": "/home/<USER>/db2025-x1/rmdb_client/src", "command": "/usr/bin/c++  -I/home/<USER>/db2025-x1 -I/home/<USER>/db2025-x1/src -Wall -O0 -g -ggdb3 -g -O0 -g -o CMakeFiles/rmdb.dir/rmdb.cpp.o -c /home/<USER>/db2025-x1/src/rmdb.cpp", "file": "/home/<USER>/db2025-x1/src/rmdb.cpp"}, {"directory": "/home/<USER>/db2025-x1/rmdb_client/src", "command": "/usr/bin/c++  -I/home/<USER>/db2025-x1 -I/home/<USER>/db2025-x1/src -isystem /home/<USER>/db2025-x1/deps/googletest/googletest/include -isystem /home/<USER>/db2025-x1/deps/googletest/googletest -Wall -O0 -g -ggdb3 -g -O0 -g -o CMakeFiles/unit_test.dir/unit_test.cpp.o -c /home/<USER>/db2025-x1/src/unit_test.cpp", "file": "/home/<USER>/db2025-x1/src/unit_test.cpp"}, {"directory": "/home/<USER>/db2025-x1/rmdb_client/src/analyze", "command": "/usr/bin/c++  -I/home/<USER>/db2025-x1 -I/home/<USER>/db2025-x1/src -Wall -O0 -g -ggdb3 -g -O0 -g -o CMakeFiles/analyze.dir/analyze.cpp.o -c /home/<USER>/db2025-x1/src/analyze/analyze.cpp", "file": "/home/<USER>/db2025-x1/src/analyze/analyze.cpp"}, {"directory": "/home/<USER>/db2025-x1/rmdb_client/src/record", "command": "/usr/bin/c++  -I/home/<USER>/db2025-x1 -I/home/<USER>/db2025-x1/src -Wall -O0 -g -ggdb3 -g -O0 -g -o CMakeFiles/record.dir/rm_file_handle.cpp.o -c /home/<USER>/db2025-x1/src/record/rm_file_handle.cpp", "file": "/home/<USER>/db2025-x1/src/record/rm_file_handle.cpp"}, {"directory": "/home/<USER>/db2025-x1/rmdb_client/src/record", "command": "/usr/bin/c++  -I/home/<USER>/db2025-x1 -I/home/<USER>/db2025-x1/src -Wall -O0 -g -ggdb3 -g -O0 -g -o CMakeFiles/record.dir/rm_scan.cpp.o -c /home/<USER>/db2025-x1/src/record/rm_scan.cpp", "file": "/home/<USER>/db2025-x1/src/record/rm_scan.cpp"}, {"directory": "/home/<USER>/db2025-x1/rmdb_client/src/record", "command": "/usr/bin/c++ -<PERSON><PERSON><PERSON><PERSON>_EXPORTS -I/home/<USER>/db2025-x1 -I/home/<USER>/db2025-x1/src -Wall -O0 -g -ggdb3 -g -O0 -g -fPIC -o CMakeFiles/records.dir/rm_file_handle.cpp.o -c /home/<USER>/db2025-x1/src/record/rm_file_handle.cpp", "file": "/home/<USER>/db2025-x1/src/record/rm_file_handle.cpp"}, {"directory": "/home/<USER>/db2025-x1/rmdb_client/src/record", "command": "/usr/bin/c++ -<PERSON><PERSON><PERSON><PERSON>_EXPORTS -I/home/<USER>/db2025-x1 -I/home/<USER>/db2025-x1/src -Wall -O0 -g -ggdb3 -g -O0 -g -fPIC -o CMakeFiles/records.dir/rm_scan.cpp.o -c /home/<USER>/db2025-x1/src/record/rm_scan.cpp", "file": "/home/<USER>/db2025-x1/src/record/rm_scan.cpp"}, {"directory": "/home/<USER>/db2025-x1/rmdb_client/src/index", "command": "/usr/bin/c++  -I/home/<USER>/db2025-x1 -I/home/<USER>/db2025-x1/src -Wall -O0 -g -ggdb3 -g -O0 -g -o CMakeFiles/index.dir/ix_index_handle.cpp.o -c /home/<USER>/db2025-x1/src/index/ix_index_handle.cpp", "file": "/home/<USER>/db2025-x1/src/index/ix_index_handle.cpp"}, {"directory": "/home/<USER>/db2025-x1/rmdb_client/src/index", "command": "/usr/bin/c++  -I/home/<USER>/db2025-x1 -I/home/<USER>/db2025-x1/src -Wall -O0 -g -ggdb3 -g -O0 -g -o CMakeFiles/index.dir/ix_scan.cpp.o -c /home/<USER>/db2025-x1/src/index/ix_scan.cpp", "file": "/home/<USER>/db2025-x1/src/index/ix_scan.cpp"}, {"directory": "/home/<USER>/db2025-x1/rmdb_client/src/system", "command": "/usr/bin/c++  -I/home/<USER>/db2025-x1 -I/home/<USER>/db2025-x1/src -Wall -O0 -g -ggdb3 -g -O0 -g -o CMakeFiles/system.dir/sm_manager.cpp.o -c /home/<USER>/db2025-x1/src/system/sm_manager.cpp", "file": "/home/<USER>/db2025-x1/src/system/sm_manager.cpp"}, {"directory": "/home/<USER>/db2025-x1/rmdb_client/src/execution", "command": "/usr/bin/c++  -I/home/<USER>/db2025-x1 -I/home/<USER>/db2025-x1/src -Wall -O0 -g -ggdb3 -g -O0 -g -o CMakeFiles/execution.dir/execution_manager.cpp.o -c /home/<USER>/db2025-x1/src/execution/execution_manager.cpp", "file": "/home/<USER>/db2025-x1/src/execution/execution_manager.cpp"}, {"directory": "/home/<USER>/db2025-x1/rmdb_client/src/execution", "command": "/usr/bin/c++  -I/home/<USER>/db2025-x1 -I/home/<USER>/db2025-x1/src -Wall -O0 -g -ggdb3 -g -O0 -g -o CMakeFiles/execution.dir/execution_explain.cpp.o -c /home/<USER>/db2025-x1/src/execution/execution_explain.cpp", "file": "/home/<USER>/db2025-x1/src/execution/execution_explain.cpp"}, {"directory": "/home/<USER>/db2025-x1/rmdb_client/src/execution", "command": "/usr/bin/c++  -I/home/<USER>/db2025-x1 -I/home/<USER>/db2025-x1/src -Wall -O0 -g -ggdb3 -g -O0 -g -o CMakeFiles/execution.dir/executor_aggregation.cpp.o -c /home/<USER>/db2025-x1/src/execution/executor_aggregation.cpp", "file": "/home/<USER>/db2025-x1/src/execution/executor_aggregation.cpp"}, {"directory": "/home/<USER>/db2025-x1/rmdb_client/src/parser", "command": "/usr/bin/c++  -I/home/<USER>/db2025-x1 -I/home/<USER>/db2025-x1/src -Wall -O0 -g -ggdb3 -g -O0 -g -o CMakeFiles/parser.dir/yacc.tab.cpp.o -c /home/<USER>/db2025-x1/src/parser/yacc.tab.cpp", "file": "/home/<USER>/db2025-x1/src/parser/yacc.tab.cpp"}, {"directory": "/home/<USER>/db2025-x1/rmdb_client/src/parser", "command": "/usr/bin/c++  -I/home/<USER>/db2025-x1 -I/home/<USER>/db2025-x1/src -Wall -O0 -g -ggdb3 -g -O0 -g -o CMakeFiles/parser.dir/lex.yy.cpp.o -c /home/<USER>/db2025-x1/src/parser/lex.yy.cpp", "file": "/home/<USER>/db2025-x1/src/parser/lex.yy.cpp"}, {"directory": "/home/<USER>/db2025-x1/rmdb_client/src/parser", "command": "/usr/bin/c++  -I/home/<USER>/db2025-x1 -I/home/<USER>/db2025-x1/src -Wall -O0 -g -ggdb3 -g -O0 -g -o CMakeFiles/parser.dir/ast.cpp.o -c /home/<USER>/db2025-x1/src/parser/ast.cpp", "file": "/home/<USER>/db2025-x1/src/parser/ast.cpp"}, {"directory": "/home/<USER>/db2025-x1/rmdb_client/src/parser", "command": "/usr/bin/c++  -I/home/<USER>/db2025-x1 -I/home/<USER>/db2025-x1/src -Wall -O0 -g -ggdb3 -g -O0 -g -o CMakeFiles/test_parser.dir/test_parser.cpp.o -c /home/<USER>/db2025-x1/src/parser/test_parser.cpp", "file": "/home/<USER>/db2025-x1/src/parser/test_parser.cpp"}, {"directory": "/home/<USER>/db2025-x1/rmdb_client/src/optimizer", "command": "/usr/bin/c++  -I/home/<USER>/db2025-x1 -I/home/<USER>/db2025-x1/src -Wall -O0 -g -ggdb3 -g -O0 -g -o CMakeFiles/planner.dir/planner.cpp.o -c /home/<USER>/db2025-x1/src/optimizer/planner.cpp", "file": "/home/<USER>/db2025-x1/src/optimizer/planner.cpp"}, {"directory": "/home/<USER>/db2025-x1/rmdb_client/src/storage", "command": "/usr/bin/c++  -I/home/<USER>/db2025-x1 -I/home/<USER>/db2025-x1/src -Wall -O0 -g -ggdb3 -g -O0 -g -o CMakeFiles/storage.dir/disk_manager.cpp.o -c /home/<USER>/db2025-x1/src/storage/disk_manager.cpp", "file": "/home/<USER>/db2025-x1/src/storage/disk_manager.cpp"}, {"directory": "/home/<USER>/db2025-x1/rmdb_client/src/storage", "command": "/usr/bin/c++  -I/home/<USER>/db2025-x1 -I/home/<USER>/db2025-x1/src -Wall -O0 -g -ggdb3 -g -O0 -g -o CMakeFiles/storage.dir/buffer_pool_manager.cpp.o -c /home/<USER>/db2025-x1/src/storage/buffer_pool_manager.cpp", "file": "/home/<USER>/db2025-x1/src/storage/buffer_pool_manager.cpp"}, {"directory": "/home/<USER>/db2025-x1/rmdb_client/src/storage", "command": "/usr/bin/c++  -I/home/<USER>/db2025-x1 -I/home/<USER>/db2025-x1/src -Wall -O0 -g -ggdb3 -g -O0 -g -o CMakeFiles/storage.dir/__/replacer/lru_replacer.cpp.o -c /home/<USER>/db2025-x1/src/replacer/lru_replacer.cpp", "file": "/home/<USER>/db2025-x1/src/replacer/lru_replacer.cpp"}, {"directory": "/home/<USER>/db2025-x1/rmdb_client/src/replacer", "command": "/usr/bin/c++  -I/home/<USER>/db2025-x1 -I/home/<USER>/db2025-x1/src -Wall -O0 -g -ggdb3 -g -O0 -g -o CMakeFiles/lru_replacer.dir/lru_replacer.cpp.o -c /home/<USER>/db2025-x1/src/replacer/lru_replacer.cpp", "file": "/home/<USER>/db2025-x1/src/replacer/lru_replacer.cpp"}, {"directory": "/home/<USER>/db2025-x1/rmdb_client/src/transaction", "command": "/usr/bin/c++  -I/home/<USER>/db2025-x1 -I/home/<USER>/db2025-x1/src -Wall -O0 -g -ggdb3 -g -O0 -g -o CMakeFiles/transaction.dir/concurrency/lock_manager.cpp.o -c /home/<USER>/db2025-x1/src/transaction/concurrency/lock_manager.cpp", "file": "/home/<USER>/db2025-x1/src/transaction/concurrency/lock_manager.cpp"}, {"directory": "/home/<USER>/db2025-x1/rmdb_client/src/transaction", "command": "/usr/bin/c++  -I/home/<USER>/db2025-x1 -I/home/<USER>/db2025-x1/src -Wall -O0 -g -ggdb3 -g -O0 -g -o CMakeFiles/transaction.dir/transaction_manager.cpp.o -c /home/<USER>/db2025-x1/src/transaction/transaction_manager.cpp", "file": "/home/<USER>/db2025-x1/src/transaction/transaction_manager.cpp"}, {"directory": "/home/<USER>/db2025-x1/rmdb_client/src/transaction", "command": "/usr/bin/c++  -I/home/<USER>/db2025-x1 -I/home/<USER>/db2025-x1/src -Wall -O0 -g -ggdb3 -g -O0 -g -o CMakeFiles/transaction.dir/watermark.cpp.o -c /home/<USER>/db2025-x1/src/transaction/watermark.cpp", "file": "/home/<USER>/db2025-x1/src/transaction/watermark.cpp"}, {"directory": "/home/<USER>/db2025-x1/rmdb_client/src/recovery", "command": "/usr/bin/c++  -I/home/<USER>/db2025-x1 -I/home/<USER>/db2025-x1/src -Wall -O0 -g -ggdb3 -g -O0 -g -o CMakeFiles/recovery.dir/log_manager.cpp.o -c /home/<USER>/db2025-x1/src/recovery/log_manager.cpp", "file": "/home/<USER>/db2025-x1/src/recovery/log_manager.cpp"}, {"directory": "/home/<USER>/db2025-x1/rmdb_client/src/recovery", "command": "/usr/bin/c++  -I/home/<USER>/db2025-x1 -I/home/<USER>/db2025-x1/src -Wall -O0 -g -ggdb3 -g -O0 -g -o CMakeFiles/recovery.dir/log_recovery.cpp.o -c /home/<USER>/db2025-x1/src/recovery/log_recovery.cpp", "file": "/home/<USER>/db2025-x1/src/recovery/log_recovery.cpp"}, {"directory": "/home/<USER>/db2025-x1/rmdb_client/src/recovery", "command": "/usr/bin/c++ -Dr<PERSON>overys_EXPORTS -I/home/<USER>/db2025-x1 -I/home/<USER>/db2025-x1/src -Wall -O0 -g -ggdb3 -g -O0 -g -fPIC -o CMakeFiles/recoverys.dir/log_manager.cpp.o -c /home/<USER>/db2025-x1/src/recovery/log_manager.cpp", "file": "/home/<USER>/db2025-x1/src/recovery/log_manager.cpp"}, {"directory": "/home/<USER>/db2025-x1/rmdb_client/src/recovery", "command": "/usr/bin/c++ -Dr<PERSON>over<PERSON>_EXPORTS -I/home/<USER>/db2025-x1 -I/home/<USER>/db2025-x1/src -Wall -O0 -g -ggdb3 -g -O0 -g -fPIC -o CMakeFiles/recoverys.dir/log_recovery.cpp.o -c /home/<USER>/db2025-x1/src/recovery/log_recovery.cpp", "file": "/home/<USER>/db2025-x1/src/recovery/log_recovery.cpp"}, {"directory": "/home/<USER>/db2025-x1/rmdb_client/deps/googletest/googlemock", "command": "/usr/bin/c++  -I/home/<USER>/db2025-x1 -I/home/<USER>/db2025-x1/src -I/home/<USER>/db2025-x1/deps/googletest/googlemock/include -I/home/<USER>/db2025-x1/deps/googletest/googlemock -isystem /home/<USER>/db2025-x1/deps/googletest/googletest/include -isystem /home/<USER>/db2025-x1/deps/googletest/googletest -Wall -O0 -g -ggdb3 -g -O0 -g -Wall -Wshadow -Wno-error=dangling-else -DGTEST_HAS_PTHREAD=1 -fexceptions -Wextra -Wno-unused-parameter -Wno-missing-field-initializers -o CMakeFiles/gmock.dir/src/gmock-all.cc.o -c /home/<USER>/db2025-x1/deps/googletest/googlemock/src/gmock-all.cc", "file": "/home/<USER>/db2025-x1/deps/googletest/googlemock/src/gmock-all.cc"}, {"directory": "/home/<USER>/db2025-x1/rmdb_client/deps/googletest/googlemock", "command": "/usr/bin/c++  -I/home/<USER>/db2025-x1 -I/home/<USER>/db2025-x1/src -isystem /home/<USER>/db2025-x1/deps/googletest/googlemock/include -isystem /home/<USER>/db2025-x1/deps/googletest/googlemock -isystem /home/<USER>/db2025-x1/deps/googletest/googletest/include -isystem /home/<USER>/db2025-x1/deps/googletest/googletest -Wall -O0 -g -ggdb3 -g -O0 -g -Wall -Wshadow -Wno-error=dangling-else -DGTEST_HAS_PTHREAD=1 -fexceptions -Wextra -Wno-unused-parameter -Wno-missing-field-initializers -o CMakeFiles/gmock_main.dir/src/gmock_main.cc.o -c /home/<USER>/db2025-x1/deps/googletest/googlemock/src/gmock_main.cc", "file": "/home/<USER>/db2025-x1/deps/googletest/googlemock/src/gmock_main.cc"}, {"directory": "/home/<USER>/db2025-x1/rmdb_client/deps/googletest/googletest", "command": "/usr/bin/c++  -I/home/<USER>/db2025-x1 -I/home/<USER>/db2025-x1/src -I/home/<USER>/db2025-x1/deps/googletest/googletest/include -I/home/<USER>/db2025-x1/deps/googletest/googletest -Wall -O0 -g -ggdb3 -g -O0 -g -Wall -Wshadow -Wno-error=dangling-else -DGTEST_HAS_PTHREAD=1 -fexceptions -Wextra -Wno-unused-parameter -Wno-missing-field-initializers -o CMakeFiles/gtest.dir/src/gtest-all.cc.o -c /home/<USER>/db2025-x1/deps/googletest/googletest/src/gtest-all.cc", "file": "/home/<USER>/db2025-x1/deps/googletest/googletest/src/gtest-all.cc"}, {"directory": "/home/<USER>/db2025-x1/rmdb_client/deps/googletest/googletest", "command": "/usr/bin/c++  -I/home/<USER>/db2025-x1 -I/home/<USER>/db2025-x1/src -isystem /home/<USER>/db2025-x1/deps/googletest/googletest/include -isystem /home/<USER>/db2025-x1/deps/googletest/googletest -Wall -O0 -g -ggdb3 -g -O0 -g -Wall -Wshadow -Wno-error=dangling-else -DGTEST_HAS_PTHREAD=1 -fexceptions -Wextra -Wno-unused-parameter -Wno-missing-field-initializers -o CMakeFiles/gtest_main.dir/src/gtest_main.cc.o -c /home/<USER>/db2025-x1/deps/googletest/googletest/src/gtest_main.cc", "file": "/home/<USER>/db2025-x1/deps/googletest/googletest/src/gtest_main.cc"}]