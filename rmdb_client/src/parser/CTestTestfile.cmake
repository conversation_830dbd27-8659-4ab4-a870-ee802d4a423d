# CMake generated Testfile for 
# Source directory: /home/<USER>/db2025-x1/src/parser
# Build directory: /home/<USER>/db2025-x1/rmdb_client/src/parser
# 
# This file includes the relevant testing commands required for 
# testing this directory and lists subdirectories to be tested as well.
add_test(test_parser "/home/<USER>/db2025-x1/rmdb_client/bin/test_parser")
set_tests_properties(test_parser PROPERTIES  WORKING_DIRECTORY "/home/<USER>/db2025-x1/rmdb_client/bin" _BACKTRACE_TRIPLES "/home/<USER>/db2025-x1/src/parser/CMakeLists.txt;14;add_test;/home/<USER>/db2025-x1/src/parser/CMakeLists.txt;0;")
