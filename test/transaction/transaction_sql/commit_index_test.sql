create table warehouse (w_id int, w_name char(10), w_street_1 char(20), w_street_2 char(20), w_city char(20), w_state char(2), w_zip char(9), w_tax float, w_ytd float);
create table district (d_id int, d_w_id int, d_name char(10), d_street_1 char(20), d_street_2 char(20), d_city char(20), d_state char(2), d_zip char(9), d_tax float, d_ytd float, d_next_o_id int);
create table customer (c_id int, c_d_id int, c_w_id int, c_first char(16), c_middle char(2), c_last char(16), c_street_1 char(20), c_street_2 char(20), c_city char(20), c_state char(2), c_zip char(9), c_phone char(16), c_since char(30), c_credit char(2), c_credit_lim int, c_discount float, c_balance float, c_ytd_payment float, c_payment_cnt int, c_delivery_cnt int, c_data char(50));
create table history (h_c_id int, h_c_d_id int, h_c_w_id int, h_d_id int, h_w_id int, h_date char(19), h_amount float, h_data char(24));
create table new_orders (no_o_id int, no_d_id int, no_w_id int);
create table orders (o_id int, o_d_id int, o_w_id int, o_c_id int, o_entry_d char(19), o_carrier_id int, o_ol_cnt int, o_all_local int);
create table order_line ( ol_o_id int, ol_d_id int, ol_w_id int, ol_number int, ol_i_id int, ol_supply_w_id int, ol_delivery_d char(19), ol_quantity int, ol_amount float, ol_dist_info char(24));
create table item (i_id int, i_im_id int, i_name char(24), i_price float, i_data char(50));
create table stock (s_i_id int, s_w_id int, s_quantity int, s_dist_01 char(24), s_dist_02 char(24), s_dist_03 char(24), s_dist_04 char(24), s_dist_05 char(24), s_dist_06 char(24), s_dist_07 char(24), s_dist_08 char(24), s_dist_09 char(24), s_dist_10 char(24), s_ytd float, s_order_cnt int, s_remote_cnt int, s_data char(50));
create index orders (o_w_id, o_d_id, o_id);
create index order_line (ol_w_id, ol_d_id, ol_o_id, ol_number);
insert into warehouse values (1, 'Q68zBZsVtS', 'OIWB5HRk7QNQqw1cyBt9', '2kyaKU3QSV8dZQpAGdwN', 'guCflg5xIAOPLZRE7dVy', '0z', 'xnnsT3Oo3', 0.190000, 300000.000000);
insert into district values (1, 1, 'arpX4qo41C', 'OKhV153zP5sdWqqPiQ43', 'stJ6ypGTTjcjwoNQUF43', 'NGfBc8oqxE489nhVYggv', 'r7', 'LYOeX6qw5', 0.150000, 300000.000000, 4);
insert into district values (2, 1, 'ocHzHb1oqY', 'UjxhkzMRI83YFxMUkMbK', '3dFzbFVow4eyRONu7N4V', '13k8Fp6vER3LGAP6cnOF', 'uM', 'QniGlFKBV', 0.100000, 300000.000000, 4);
insert into customer values (1, 1, 1, 'L7HK6mHLuj8Dk0tL', 'OE', 'BARRBARRBARR', 'QEpWd0sdl6JIoZXjUCNt', 'NhDqlD4508d8WHEQzO0D', 'vcRLD3C2bk4kDclgBcWt', 'mn', 'rIXMAxco4', '5123542876369076', '2023-06-03 19:25:47', 'GC', 50000, 0.080000, 10.000000, 10.000000, 1, 0, 'wZaCxlFNqDLcxQc5EsDt6Pj8VaZot6T1yhFJMKxSconPUAQ81v');
insert into customer values (2, 1, 1, 'hNfVBw163duFxmnp', 'OE', 'BARRBARROUGH', 'gRoKT9jF6JE5s28ZJ2f7', 'rP7rx5l2wDKojp8qAZ2O', 'ssq9q3Xk9NLSrZDz1psW', 'FN', 'zHUGapraC', '4587239186950955', '2023-06-03 19:25:47', 'BC', 50000, 0.100000, 10.000000, 10.000000, 1, 0, 'cOjRdyvrL6asx71BgLGef9POQpxNSQJNRP74uacEVhQ7WpMYdK');
insert into customer values (3, 1, 1, '3FR3WClIXnYKpz7r', 'OE', 'BARRBARRABLE', 'zRFLRtj099HuI5UtJBpu', 'ufnTs0XAD489RaroTYWt', '9VRbXXhjJQckr9ugkGrp', 'Vg', '3pBsbdGUy', '3406537267863603', '2023-06-03 19:25:47', 'GC', 50000, 0.390000, 10.000000, 10.000000, 1, 0, 'RmKd44fLpTr57WFQYN7BP6NE6SCofSXruG9DV4gx4pPGXNQ56h');
insert into customer values (1, 2, 1, 'wGtFtVAvPwfh6KAT', 'OE', 'BARRBARRBARR', 'Kr5B7rhEQuZFm6XS8Jc0', 'tD8h7JA48gSaVQRwq4hx', '5gW8iFcKRWgRA0D2dQA6', 'Yr', 'gmrcWUEcZ', '0122704584621942', '2023-06-03 19:25:47', 'BC', 50000, 0.330000, 10.000000, 10.000000, 1, 0, 'tRsOva5RaixclHHTojvWwUFw2qMEB7AXAsucK1NobauiOo2j02');
insert into customer values (2, 2, 1, 'Vz0W09fdZdc2535X', 'OE', 'BARRBARROUGH', 'ztSN4ZbVEDw9DvftxPJR', 'k6v43f4izHOpvwHmoJts', '2UidBq6F7g8X5IPLlQfh', '7o', 'eJSUdwo5R', '2245153609451538', '2023-06-03 19:25:47', 'BC', 50000, 0.420000, 10.000000, 10.000000, 1, 0, 'nTn9wXfQCsF2k9Uts8uGSIQGAMqt0OM8wpCmPnLcntMrm07Tzu');
insert into customer values (3, 2, 1, 'PvV3u9jUo0jANNQL', 'OE', 'BARRBARRABLE', 'lKgVNdTszeYdmNLy0vs0', 'cbJOrraWAO90qTLhJRaX', 'lrFAErgzgsMGGe1hg0Ho', 'co', 'cntBP2vtU', '0936192361492412', '2023-06-03 19:25:47', 'GC', 50000, 0.250000, 10.000000, 10.000000, 1, 0, 'i9YpHKOWtsALClutjIyetfqDpXQP9ug0Zlo5g6qKw9977EvI20');
insert into history values (1, 1, 1, 1, 1, '2023-06-03 19:25:47', 10.000000, 'Nng61h2qw01g1NXKDuRVzfX4');
insert into history values (2, 1, 1, 1, 1, '2023-06-03 19:25:47', 10.000000, 'Nu9SgB9guPkjxN0UpY2kuNmK');
insert into history values (3, 1, 1, 1, 1, '2023-06-03 19:25:47', 10.000000, 'Yj5LE6YGgHotRur6POyOLv0x');
insert into history values (1, 2, 1, 2, 1, '2023-06-03 19:25:47', 10.000000, 'Rzdkl1N6edY3uS6bg3tlc3DU');
insert into history values (2, 2, 1, 2, 1, '2023-06-03 19:25:47', 10.000000, 'oFXbh0DWlB2ZAgR7cyLdQXT5');
insert into history values (3, 2, 1, 2, 1, '2023-06-03 19:25:47', 10.000000, 'g5NdUjf16kiUtLPyDLlQ3p9c');
insert into new_orders values (2, 1, 1);
insert into new_orders values (3, 1, 1);
insert into new_orders values (2, 2, 1);
insert into new_orders values (3, 2, 1);
insert into orders values (1, 1, 1, 1, '2023-06-03 19:25:47', 3, 15, 1);
insert into orders values (2, 1, 1, 3, '2023-06-03 19:25:47', 0, 10, 1);
insert into orders values (3, 1, 1, 2, '2023-06-03 19:25:47', 0, 15, 1);
insert into orders values (1, 2, 1, 3, '2023-06-03 19:25:47', 10, 15, 1);
insert into orders values (2, 2, 1, 2, '2023-06-03 19:25:47', 0, 6, 1);
insert into orders values (3, 2, 1, 1, '2023-06-03 19:25:47', 0, 5, 1);
insert into order_line values (1, 1, 1, 1, 1, 1, '2023-06-03 19:25:47', 5, 0.000000, 'IeQ8muD7ZIQXG4Tj6AftKAcx');
insert into order_line values (1, 1, 1, 2, 1, 1, '2023-06-03 19:25:47', 5, 0.000000, 'GrRqIJFjRhx7XLfORVJPNz5V');
insert into order_line values (1, 1, 1, 3, 4, 1, '2023-06-03 19:25:47', 5, 0.000000, 'ZVNpKAnkqLJnkj5eIaTQfu8T');
insert into order_line values (1, 1, 1, 4, 6, 1, '2023-06-03 19:25:47', 5, 0.000000, 'yeoXVIWqzpoNqjjmPjm5WBbl');
insert into order_line values (1, 1, 1, 5, 7, 1, '2023-06-03 19:25:47', 5, 0.000000, '60YrsFapjMcyASJ1YNn44Rs1');
insert into order_line values (1, 1, 1, 6, 10, 1, '2023-06-03 19:25:47', 5, 0.000000, 'viy8dUrXTQYPncU0WRa9mQbY');
insert into order_line values (1, 1, 1, 7, 9, 1, '2023-06-03 19:25:47', 5, 0.000000, '0dJaCjAdC9VHc9nD1LuV5g8t');
insert into order_line values (1, 1, 1, 8, 7, 1, '2023-06-03 19:25:47', 5, 0.000000, 'FFTeZHtCIfpHPkrxex9qVfF0');
insert into order_line values (1, 1, 1, 9, 9, 1, '2023-06-03 19:25:47', 5, 0.000000, 'mImfQt67su0KG1A85zVrNSXH');
insert into order_line values (1, 1, 1, 10, 1, 1, '2023-06-03 19:25:47', 5, 0.000000, 'iEzDfmieljbehRW8PMAiRurV');
insert into order_line values (1, 1, 1, 11, 4, 1, '2023-06-03 19:25:47', 5, 0.000000, '6XggohIDGKlfmWhZJMhhw9Zb');
insert into order_line values (1, 1, 1, 12, 2, 1, '2023-06-03 19:25:47', 5, 0.000000, 'UC1KOFjMz4tJdbDPTCJdzf2S');
insert into order_line values (1, 1, 1, 13, 3, 1, '2023-06-03 19:25:47', 5, 0.000000, 'IOUQmzxO9xfDmQRRDEakGqCh');
insert into order_line values (1, 1, 1, 14, 8, 1, '2023-06-03 19:25:47', 5, 0.000000, 'StQ9k5qoKF2r1KtwRpeNcw7l');
insert into order_line values (2, 1, 1, 1, 5, 1, '2023-06-03 19:25:47', 5, 0.000000, '8ISHL46C5lbCnfVS7jLhmE5S');
insert into order_line values (2, 1, 1, 2, 4, 1, '2023-06-03 19:25:47', 5, 0.000000, 'mfFSm7K5QoutWabFxcZlpc3C');
insert into order_line values (2, 1, 1, 3, 8, 1, '2023-06-03 19:25:47', 5, 0.000000, 'BAKggnpD5HYpI7vdobLuZTP9');
insert into order_line values (2, 1, 1, 4, 3, 1, '2023-06-03 19:25:47', 5, 0.000000, 'PKJUQhYKCQ4714l352BBZdBM');
insert into order_line values (2, 1, 1, 5, 7, 1, '2023-06-03 19:25:47', 5, 0.000000, 's2G0QAK6BKY8ztzOA68e7pNA');
insert into order_line values (2, 1, 1, 6, 2, 1, '2023-06-03 19:25:47', 5, 0.000000, 'xFwPrHx268fkAG4W7zMYMZsz');
insert into order_line values (2, 1, 1, 7, 6, 1, '2023-06-03 19:25:47', 5, 0.000000, 'hd8WhcT7LCKKEraxPXVJooRk');
insert into order_line values (2, 1, 1, 8, 9, 1, '2023-06-03 19:25:47', 5, 0.000000, 'iKmilYAEY8d4reT81Grtrlyv');
insert into order_line values (2, 1, 1, 9, 10, 1, '2023-06-03 19:25:47', 5, 0.000000, 's7BERJPPLeM5gw76yAnbr0Nq');
insert into order_line values (2, 1, 1, 10, 10, 1, '2023-06-03 19:25:47', 5, 0.000000, '8PZWz5EAh227kjyO2rGsCgLh');
insert into order_line values (2, 1, 1, 11, 8, 1, '2023-06-03 19:25:47', 5, 0.000000, 'BgoM61i4u0TRhf6ufvrWuv4O');
insert into order_line values (2, 1, 1, 12, 3, 1, '2023-06-03 19:25:47', 5, 0.000000, '3f4Or3LS1svCfgssqVlNUT62');
insert into order_line values (2, 1, 1, 13, 3, 1, '2023-06-03 19:25:47', 5, 0.000000, 'vb2vUDc6zEiUmcNu068D1AKr');
insert into order_line values (2, 1, 1, 14, 1, 1, '2023-06-03 19:25:47', 5, 0.000000, 'whU0s5pvzohloLliey6TbeBt');
insert into order_line values (3, 1, 1, 1, 3, 1, '2023-06-03 19:25:47', 5, 0.000000, 'oAr4MsSv1diZkeQPLchxp9B5');
insert into order_line values (3, 1, 1, 2, 7, 1, '2023-06-03 19:25:47', 5, 0.000000, 'Ah1ESQLy9g5nJpMsqcexnspb');
insert into order_line values (3, 1, 1, 3, 2, 1, '2023-06-03 19:25:47', 5, 0.000000, 'O1qPYzzD4Rnp6c1TrIAEiVkN');
insert into order_line values (3, 1, 1, 4, 9, 1, '2023-06-03 19:25:47', 5, 0.000000, 'vpWO8CHAtDYieY2ayuVxGPVj');
insert into order_line values (3, 1, 1, 5, 1, 1, '2023-06-03 19:25:47', 5, 0.000000, 'JH5zpu82iUjHFe9EFJSx01TB');
insert into order_line values (3, 1, 1, 6, 8, 1, '2023-06-03 19:25:47', 5, 0.000000, 'BWRYCOSePxNWzfgNUh6m0cId');
insert into order_line values (3, 1, 1, 7, 2, 1, '2023-06-03 19:25:47', 5, 0.000000, 'Dj1UHeIxODJfvk5Hkom69mGV');
insert into order_line values (3, 1, 1, 8, 4, 1, '2023-06-03 19:25:47', 5, 0.000000, 'AbT8asPwSyBTsbKMXPwhcNUD');
insert into order_line values (3, 1, 1, 9, 6, 1, '2023-06-03 19:25:47', 5, 0.000000, 'XooxmcBheikfgjQBTAcQ71nA');
insert into order_line values (3, 1, 1, 10, 8, 1, '2023-06-03 19:25:47', 5, 0.000000, 'PLkeBRXtG3bePx2JPmJSdYev');
insert into order_line values (3, 1, 1, 11, 9, 1, '2023-06-03 19:25:47', 5, 0.000000, 'gimwPUWwF6Rc508wArPPpjU7');
insert into order_line values (3, 1, 1, 12, 9, 1, '2023-06-03 19:25:47', 5, 0.000000, 'A7MgkXELnKVLkIN5vo9WKqT1');
insert into order_line values (3, 1, 1, 13, 6, 1, '2023-06-03 19:25:47', 5, 0.000000, 'b21hKFyZNwJZBAUbTdiEFXUm');
insert into order_line values (3, 1, 1, 14, 7, 1, '2023-06-03 19:25:47', 5, 0.000000, 'OZMQ4e5cAFdM96oFUDZQn3YY');
insert into order_line values (1, 2, 1, 1, 4, 1, '2023-06-03 19:25:47', 5, 0.000000, 'Qsub3eslbgppjGUYLSTe0RUB');
insert into order_line values (1, 2, 1, 2, 9, 1, '2023-06-03 19:25:47', 5, 0.000000, 'oKev7mdYKN2QXXHlA8TZIAvq');
insert into order_line values (1, 2, 1, 3, 1, 1, '2023-06-03 19:25:47', 5, 0.000000, 'QLhADTXYzJLApABlLNuZMypf');
insert into order_line values (1, 2, 1, 4, 10, 1, '2023-06-03 19:25:47', 5, 0.000000, 'DGQyEpmr3tF9O97mMqwEZmst');
insert into order_line values (1, 2, 1, 5, 7, 1, '2023-06-03 19:25:47', 5, 0.000000, 'v8RJKmKaOSce0lvMrzERhgKJ');
insert into order_line values (1, 2, 1, 6, 7, 1, '2023-06-03 19:25:47', 5, 0.000000, 'UTUQrNNOw6Vm6G0FeATN5MCG');
insert into order_line values (1, 2, 1, 7, 5, 1, '2023-06-03 19:25:47', 5, 0.000000, 'S6UIjlZpdolSppo9bnTdW0t4');
insert into order_line values (1, 2, 1, 8, 6, 1, '2023-06-03 19:25:47', 5, 0.000000, '4gqT8ezOzJSSiJgajbLY5kak');
insert into order_line values (1, 2, 1, 9, 5, 1, '2023-06-03 19:25:47', 5, 0.000000, 'EDCh0ykub9AfuC7kSwJqVb4e');
insert into order_line values (1, 2, 1, 10, 9, 1, '2023-06-03 19:25:47', 5, 0.000000, 'aZGnCYryhhsnWDbw8xgzvdh8');
insert into order_line values (1, 2, 1, 11, 10, 1, '2023-06-03 19:25:47', 5, 0.000000, 'L2H3M2neYU5UwJIW85zmkHZv');
insert into order_line values (1, 2, 1, 12, 9, 1, '2023-06-03 19:25:47', 5, 0.000000, 'kRZl3HOiYRl1VF4Xiahl29C9');
insert into order_line values (2, 2, 1, 1, 1, 1, '2023-06-03 19:25:47', 5, 0.000000, 'dNyrVDKqM9uJdaf53OOo6gmA');
insert into order_line values (2, 2, 1, 2, 3, 1, '2023-06-03 19:25:47', 5, 0.000000, 'WdU6UHjJTnlt0DP8M2SXobHd');
insert into order_line values (2, 2, 1, 3, 8, 1, '2023-06-03 19:25:47', 5, 0.000000, 'ynYNAbpZRWzqGV8MVDOT5890');
insert into order_line values (2, 2, 1, 4, 5, 1, '2023-06-03 19:25:47', 5, 0.000000, 'gYe7HNZ9FNYtHYNVgfmlCN1a');
insert into order_line values (2, 2, 1, 5, 4, 1, '2023-06-03 19:25:47', 5, 0.000000, 'QmJCw741wqJe5e3nB4E2Es0O');
insert into order_line values (2, 2, 1, 6, 10, 1, '2023-06-03 19:25:47', 5, 0.000000, '38jDDYO6j5HpHeaSTR0mTv0w');
insert into order_line values (2, 2, 1, 7, 2, 1, '2023-06-03 19:25:47', 5, 0.000000, 'K1Q3YuC4XQwLnOfGWOuN7nSD');
insert into order_line values (2, 2, 1, 8, 3, 1, '2023-06-03 19:25:47', 5, 0.000000, 'nRuBmgMu5LBt2JIVA7l110Ek');
insert into order_line values (2, 2, 1, 9, 9, 1, '2023-06-03 19:25:47', 5, 0.000000, 'AHzxFfyDLYSB6XzwMum5wWL0');
insert into order_line values (2, 2, 1, 10, 1, 1, '2023-06-03 19:25:47', 5, 0.000000, 'bjiVzXYB4ekJjvMo6jg71IYo');
insert into order_line values (2, 2, 1, 11, 7, 1, '2023-06-03 19:25:47', 5, 0.000000, 'e01Xd0CshK5UdLEfoKIHmAME');
insert into order_line values (3, 2, 1, 1, 2, 1, '2023-06-03 19:25:47', 5, 0.000000, 'TMvna3hJtVQ17xiRQ21oTzLh');
insert into order_line values (3, 2, 1, 2, 10, 1, '2023-06-03 19:25:47', 5, 0.000000, 'B1mGwoFpuxb8KZ4ptnnOm1Fa');
insert into order_line values (3, 2, 1, 3, 3, 1, '2023-06-03 19:25:47', 5, 0.000000, '1t2eE7ELvfTLM7KZiNEUwapP');
insert into order_line values (3, 2, 1, 4, 10, 1, '2023-06-03 19:25:47', 5, 0.000000, '9JqEyOXRzBVaLMhDEmND62TA');
insert into order_line values (3, 2, 1, 5, 9, 1, '2023-06-03 19:25:47', 5, 0.000000, 'kntQVoY1GWVOy8jvA3d97WBG');
insert into order_line values (3, 2, 1, 6, 8, 1, '2023-06-03 19:25:47', 5, 0.000000, 'Lk70c2RW3kR7lvjs7BgekbKy');
insert into order_line values (3, 2, 1, 7, 3, 1, '2023-06-03 19:25:47', 5, 0.000000, 'Rb1DYvIJ0V94s3ovxKZFF6Zx');
insert into order_line values (3, 2, 1, 8, 3, 1, '2023-06-03 19:25:47', 5, 0.000000, 'LHGPhyk2ads2yzYxb8JFd9Iv');
insert into order_line values (3, 2, 1, 9, 1, 1, '2023-06-03 19:25:47', 5, 0.000000, 'HKFNCiHC1bG0GOUX643OdFBq');
insert into item values (1, 1939, 'f9SZmv9dIpyRpoo1nVqn9umW', 27.100000, 'O8l0Dw34rcCG9VtfMaTKP7xTaU2TH5xn0jdYtFp4nuNNpkD5mI');
insert into item values (2, 6107, 'WI8GYmjqcz7MPAsUIkvTME6q', 97.950000, 'bJ16Kno6WxhDVIt8LhkLRNNGiHxv6ahavhdDJMX9lt3DCzBuAB');
insert into item values (3, 792, '9hwccdHZdgbf7vM0FNDGmEmb', 8.625000, 'TPvz9uJgljup4SWtDctsyMterGRkuXbSq7bj8uGvWI7srjgAm9');
insert into item values (4, 7072, 'BLg4nebBDk7hweKFWviHE5MX', 19.350000, 'FGHQgGoAdAa7mSh7dZib1mgrdEXkbw2txQGXugOBrUWdZkAeFy');
insert into item values (5, 6421, 'm8YVqT62Od5nfnCEcik6XiVz', 2.400000, 'dDPRTcYRI1BTP1BzbtDA6Ta2PGLur9VrYDk55anBwleylfdNyi');
insert into item values (6, 9280, 'zmygDi8g2eUcAo5UZdpIlSEX', 94.312500, 'qMRcuTnCHtFFBCOC618bAyiqGYgF4ONOm96vAkrR8pPrSBPOky');
insert into item values (7, 5974, 'KyUIf93AUAimmrTuV6c7u9LO', 32.350000, 'm7ZcTcdg2V2WfOLQilzlS3swRKcPSkdjTFfQMLyNGyt4OlzgYA');
insert into item values (8, 3927, 'HrZNxFRMphHJN6xL0U2ut7y4', 11.850000, 'U7gJg1RyU8bETOAiSTq0tX28xLMEkQWdZsvrCxLFCkxdgsK2VQ');
insert into item values (9, 1435, '5UbII7s3ghlwj3u03PVeVlll', 5.450000, 'aH65R8QaN1M1WOLa2hbwbrbyNRUEDvlrCiedtTAGBIBjKqEKNu');
insert into item values (10, 5839, 'BHpI5dhw4dDgiodle6qIvU67', 55.000000, 'YAL3j4hAoyKrv42TzMRQQnWbLo3TZKG91lrJs664873vwnqtGz');
insert into stock values (1, 1, 13, 'wSrMDTGvcKKcH1Uq0FVagtpi', '5hqmccnVqnOXM7gxfIBRw17I', 'ipPzlDZJSYmchjnVE5kX7Eus', 'hcg3zOEgr8kXhIMgLSzXMhqF', 'JNkQ3xBRh5SXvmIOxQbzRvXj', '1YWGnqRjaqgMNc7nybGceaPz', 'Trboa3Us6ZwlouJQ3nrrDW7d', 'Mh8SIcVR3IU9YC6LxfBHwRFR', 'VPFgeU7vyhoomSJoklxa3F7M', 'NLFmQkNtR1wLBQxjS7HGlMFQ', 0.000000, 0, 0, 'kVPXiCVAkwyHbwGKnJ5BCn0NATtSZa3BAp2ylqjE2cT6zlvR1L');
insert into stock values (2, 1, 50, '3Qsv4FSQKzeTt4ovCAw8pXML', 'qD2TCsXp1iy08Zfh2Wf2nkBe', 'DkiDblFfdvBxX1JMHnvJ3U6k', 'sP2EoZk9xAXUGMusLTUsBPWZ', 'wzHQIo6R06LPKJk8EBK8qzgl', 'hHsfl8VvRRovMPsM0vXOOeVY', 'NvvPc01e1F8dUQPwunjD7lkE', 'mLod6bGNT9ssTCLcsLZQueFU', 'x4eC5GbguOBnoEwjfMU7DhBH', 'tBupTH9L74sgmVbMyyXLl5Sr', 0.000000, 0, 0, 'EguLzFFMFFJlniTewXQWkJMvWDSPn6CV5ZjQLotZsNIKMoBo0E');
insert into stock values (3, 1, 52, 'QN6CxGIQCpT0KTsrIPwfPTq3', '6ISHr1cj19YIlc8j5AmyCNqM', 'WPQuny27h2G7xRWg98J53ZOA', 'QvMDKDAc8174cI7nhwrISfx3', 'XtOajTgAqDRdLN7OeOCyl17Y', '0VDpFB5R3BfKTaRZRNVDxraK', 'syTvOZGiL802QCrRENLF78O6', 'o20ODA0Ucjy8o4Kn6iAPbhVU', 'ToJIHeZMsR7B58EhevG4VX0i', 'oZHOiEPnf6xFfVR2gl4Irxot', 0.000000, 0, 0, 't4Xhw9DPiWFxWZLGGkI63k0WECKINUVnp33zUYiH2GQvPxXrwK');
insert into stock values (4, 1, 41, 'SyQVEi78nAi3ioUbmrbJulFz', 'MIbnKBykLx2vWQ2qSM9Uel8s', 'CPAO7e4OpYPLfw4TyypDJsWE', 'xkeY5vx4weG6MIinBw6Q3GBW', 'HCbVbi3zMKR6s2vcoVM4qojv', 'kJMRm0MWeuDE5GDPZfnvEgrJ', '9kWiZWlf6i3EJKqhml95zUq6', 'S0HHga0s9VkjFP1wu3a3W9ZX', 'o02PEOsdbO7dmgf6eoVF2PUz', 'MX29OZNKhrBUvF6Mok7KwvkF', 0.000000, 0, 0, 'aIHHdDOi8FayK1ahfAor598gXNeUgeywe1j6QaICTTeq8tLrx6');
insert into stock values (5, 1, 58, '2AuL3NDHofnGX4pOGs6FFNwT', 'Ys2WMHlKoHZrd7qYz6tm6Ecg', 'RffaarL6mtlSRgWZvt5tGNrY', 'F0WNGsrNX0yXoP1MNxIPrbv3', 'rPV5WwIb2ZvxU4x90PySxSuU', 'FzdCYt1lPMPsaSkXhVvCTLXb', 'pDeJfTs523xpH94fNYqWQpUa', 'mXO3sAyEXXlc2gkyMtqlFTCb', 'COhSn099NpIwqwCiOLI4x6c4', 'VeLqOWCrGrhhASq0zPdr59fc', 0.000000, 0, 0, '7XoakZzTuSLRM81VufmDN6I8fg2EDPHwSwA80lEO4OhNyWWhyn');
insert into stock values (6, 1, 24, 'KHjyEe5RVZ54r3jol880B9Mq', 'dCQ1CsmDdaO6CQNydHjlpXDg', 'jJQ8Lcuk9ST1Cv7ORHxnaG1z', 'NKoHyrKIouQK0mULqG8UCUs1', 'gYXmjgXpjp9LqGjMXiqOI0cW', '4jvCIholgflNeZKRvqhwEhvX', 'V0Lsxoz7Bx6odGszw0bOjzhF', 'USxHmM9O2YBEPKqvyla0mgYT', 'YSsoqF7RZhm9BfoSJx7VG5vg', 'uH3hqOjOwoeJsqvSGV0z6JKw', 0.000000, 0, 0, 'IpBbBbkNfYg58YKInD7NnqlFg7Q0TztmKioBu9A9VFQfPxKkbS');
insert into stock values (7, 1, 56, 'kEFJaBLbYlWvOAnz021H6eFD', 'fL07KqbpajRxGRS4wexEDQvZ', '4CBZvaLugbf99aq8vsOaiiwv', 'rv7IRpWXiXc2TyPFIurDrPyx', 'sybRDHzsKQWyb60nyQb5flCf', 'TjCkFIObMXOm8PxJgimhl591', '5b2fzHQ6nVHGdVhOoEf0n07V', 'pr5t8SW6GIuAZjOnycK08ROH', 'OsdU2jKQxcYel6qm0PvJAhFF', 'UCSQrdmIJwMeTXBICx6drJWk', 0.000000, 0, 0, '0tSYOjvGu04Rzn3S10eAmrm8A93hdQ69TNhws6C4ydXG5OkQU1');
insert into stock values (8, 1, 67, 'XnvBf6EPFhOyE4veaNjSRph5', '0jIbAvr7hHuLV2RfdYK67nD2', 'B4la1I6oOXudZkBsZQl1tfCo', 'mrz5uGa9reD1QlMIw7qigHjQ', 'R7xYIgEjo9EEpY5tIDehUVHb', 'T6Cv6B9kV0NZ21GzVvtF3MT5', 'RufTQPiUSGphsUwYcUoZIR03', 'oI6t4KIaWWY3jvinN7F7k3Ln', 'Kj1urkV7CdOkmvpKy7pCXbbb', 'DPu0jMfwhlYZH5hzG8dZFO3w', 0.000000, 0, 0, 'h26NmgGLZ7CO0HRWnUhfEjn234DS0xySiWZ9ecaXNzPo89Osqt');
insert into stock values (9, 1, 100, 'yEcV2yKpYLxaa2Ot4XFqze2N', 'Og0ieBggZhJXKmdspU2lmprO', 'gkVWfin7JSKVicNyQacQXzQf', 'qbmutUVEg7Z7cVEQjnNUpAhV', 'HkWuBxBVlMH206G0qleMmeEJ', 'Al95S193th7sdWys5VDmYpyE', 'Lal4b07tPMxJYTGypSHacAJP', '2yQL0ZZY9EsDn2GrMZDsMNMu', 'Gk9rSdVzHJM9UxJ0XpkPVBwl', '0eS1lg5zeY1fDM4GWWHLSsxc', 0.000000, 0, 0, '8djbwPQrO5c7yPL0MFv1awtkj6cjtyzFqiOGShe02OzZTi2HUs');
insert into stock values (10, 1, 24, 'oFCwmXrKxL0xGWzL3e7ffVi9', 'QRIsmuTYl0i54iIaHA8Tg6p8', 'MgGUm7cxaIbW5HotwKsOvSbL', '7yInbTjSVCEvqoM1FVuHl9tU', 'wAIza7kmXO1CG5D5E0FOYA4D', 'ZtLuM4oq0z6nFh4RfNxPie9j', 'SwvuRN1dgKti85FzB6gJRTr9', 'a2THN4CkUnJvhDsnDkB8LL43', '8REazocC4E1zaXTwja6bdL4C', 'IrBbmsNcKSApFJjXhoTA5BrT', 0.000000, 0, 0, 'LNDAguaLPtLCnrjxaO2rEHWaA3YBhWosgorEmsookt25H45vZm');
begin;
select c_discount, c_last, c_credit, w_tax from customer, warehouse where w_id=1 and c_w_id=w_id and c_d_id=1 and c_id=2;
select d_next_o_id, d_tax from district where d_id=1 and d_w_id=1;
update district set d_next_o_id=5 where d_id=1 and d_w_id=1;
insert into orders values (4, 1, 1, 2, '2023-06-03 19:25:47', 26, 5, 1);
insert into new_orders values (4, 1, 1);
select i_price, i_name, i_data from item where i_id=10;
select s_quantity, s_data, s_dist_01, s_dist_02, s_dist_03, s_dist_04, s_dist_05, s_dist_06, s_dist_07, s_dist_08, s_dist_09, s_dist_10 from stock where s_i_id=10 and s_w_id=1;
update stock set s_quantity=7 where s_i_id=10 and s_w_id=1;
insert into order_line values (4, 1, 1, 1, 10, 1, '2023-06-03 19:25:47', 7, 286.625000, 'VF2uQHlDhtxa5dKhPwWyCqgY');
select i_price, i_name, i_data from item where i_id=10;
select s_quantity, s_data, s_dist_01, s_dist_02, s_dist_03, s_dist_04, s_dist_05, s_dist_06, s_dist_07, s_dist_08, s_dist_09, s_dist_10 from stock where s_i_id=10 and s_w_id=1;
update stock set s_quantity=4 where s_i_id=10 and s_w_id=1;
insert into order_line values (4, 1, 1, 2, 10, 1, '2023-06-03 19:25:47', 4, 587.625000, 'KycgPD8lxa34k61eDp0ZTGVa');
select i_price, i_name, i_data from item where i_id=6;
select s_quantity, s_data, s_dist_01, s_dist_02, s_dist_03, s_dist_04, s_dist_05, s_dist_06, s_dist_07, s_dist_08, s_dist_09, s_dist_10 from stock where s_i_id=6 and s_w_id=1;
update stock set s_quantity=4 where s_i_id=6 and s_w_id=1;
insert into order_line values (4, 1, 1, 3, 6, 1, '2023-06-03 19:25:47', 4, 203.156250, 'WNiJ9wxwASHqLafFzwcZR0x4');
select i_price, i_name, i_data from item where i_id=9;
select s_quantity, s_data, s_dist_01, s_dist_02, s_dist_03, s_dist_04, s_dist_05, s_dist_06, s_dist_07, s_dist_08, s_dist_09, s_dist_10 from stock where s_i_id=9 and s_w_id=1;
update stock set s_quantity=8 where s_i_id=9 and s_w_id=1;
insert into order_line values (4, 1, 1, 4, 9, 1, '2023-06-03 19:25:47', 8, 113.000000, 'mGZ52H95MeTVdST9GNcJKCRO');
select i_price, i_name, i_data from item where i_id=4;
select s_quantity, s_data, s_dist_01, s_dist_02, s_dist_03, s_dist_04, s_dist_05, s_dist_06, s_dist_07, s_dist_08, s_dist_09, s_dist_10 from stock where s_i_id=4 and s_w_id=1;
update stock set s_quantity=9 where s_i_id=4 and s_w_id=1;
insert into order_line values (4, 1, 1, 5, 4, 1, '2023-06-03 19:25:47', 9, 940.000000, 'OHvFymiGEdnzJWo4iv811TRo');
commit;
select * from order_line;
select * from stock;
select * from district;
select * from new_orders;
select * from orders;
select * from orders where o_w_id = 1 and o_d_id = 1 and o_id = 4;