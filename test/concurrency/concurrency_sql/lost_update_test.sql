preload 4
create table concurrency_test (id int, name char(8), score float);
insert into concurrency_test values (1, 'xia<PERSON><PERSON>', 90.0);
insert into concurrency_test values (2, 'xiaoming', 95.0);
insert into concurrency_test values (3, 'zhanghua', 88.5);

txn1 4
t1a begin;------------1
t1b select * from concurrency_test where id = 2;------------4
t1c update concurrency_test set score = 100.0 where id = 2;------------5
t1d commit;6

txn2 5
t2a begin;------------2
t2b select * from concurrency_test where id = 2; ------------3
t2c update concurrency_test set score = 75.5 where id = 2; ------------7
t2d commit;------------8
t2e select * from concurrency_test where id = 2;------------9

permutation 8
t1a
t2a
t2b
t1b
t1c
t2c
t2d
t2e