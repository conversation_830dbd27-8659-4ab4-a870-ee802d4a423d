-- Lab3-查询执行  测试点4: 单表删除与条件查询
create table student (id int, name char(9), major char(32));
insert into student values (0, '<PERSON><PERSON><PERSON>', 'aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa');
insert into student values (1, '<PERSON>T<PERSON>Tom', 'Computer ScienceComputer Science');
insert into student values (2, '<PERSON><PERSON><PERSON><PERSON>', 'Computer ScienceComputer Science');
insert into student values (3, '<PERSON><PERSON><PERSON><PERSON>', 'Electrical Engineeringer Science');
insert into student values (3, '<PERSON><PERSON><PERSON><PERSON>', 'aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa');
insert into student values (4, '<PERSON><PERSON><PERSON><PERSON>', 'bbbbbbbbbbbbbbbcccccccccdddddddd');
insert into student values (5, '<PERSON><PERSON><PERSON><PERSON>', 'aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa');
insert into student values (-1, 'Li<PERSON>ianShe', 'c741258963qwertyuioplkjhgfdsazxc');
insert into student values (7, 'RMDBrmdbr', '74125896332145698712365478996321');
delete from student where name = 'Jack';
delete from student where id < 0;
select * from student;
select name,major from student where id > -5;
delete from student where id = 3 and name = '<PERSON>JackJ';
select * from student;
select name from student where id = 3;
select name from student where name = 'JackJackJ';
delete from student where major = 'aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa';
select * from student where id>=1;
