-- Lab3-查询执行  测试点2: 单表插入与条件查询
create table student (id int, name char(9), major char(32));
insert into student values (0, '<PERSON><PERSON><PERSON>', 'aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa');
insert into student values (1, '<PERSON><PERSON><PERSON>T<PERSON>', 'Computer ScienceComputer Science');
insert into student values (2, '<PERSON><PERSON><PERSON><PERSON>', 'Computer ScienceComputer Science');
insert into student values (3, '<PERSON><PERSON>ack<PERSON>', 'Electrical Engineeringer Science');
insert into student values (3, '<PERSON><PERSON><PERSON><PERSON>', 'aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa');
select * from student where id >= 1;
select id from student where id = 2;
select major from student where name = '<PERSON><PERSON><PERSON><PERSON>';
select name,major from student where name = '<PERSON><PERSON><PERSON><PERSON>' and id = 0;
select name,major from student where name = '<PERSON><PERSON><PERSON><PERSON>' and id = 2;
select name,id from student where major = 'aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa';
select myname from student;
select name from student where myname = 'aaa';
