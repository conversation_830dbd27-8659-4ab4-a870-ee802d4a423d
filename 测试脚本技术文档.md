# db2025-x1 测试脚本技术文档

## 1. 脚本整体架构和组成部分

### 1.1 核心组件

| 组件 | 文件名 | 功能描述 |
|------|--------|----------|
| 主测试脚本 | `comprehensive_test.py` | 核心测试引擎，支持所有9个题目的测试 |
| Shell入口 | `run_all_tests.sh` | Shell脚本入口，提供命令行接口 |
| 快速测试 | `quick_test.py` | 简化版测试脚本，用于快速验证 |
| 聚合专项测试 | `aggregate_test_suite.py` | 题目5专用的聚合函数测试 |
| 故障恢复专项测试 | `crash_recovery_test_suite.py` | 题目9专用的故障恢复测试 |
| SQL测试工具 | `sql_test.py` | SQL测试执行工具 |
| 框架验证 | `test_framework_validation.py` | 测试框架完整性验证 |

### 1.2 架构设计

```
comprehensive_test.py (主控制器)
├── discover_unit_tests() - 自动发现单元测试
├── discover_sql_tests() - 自动发现SQL测试
├── run_unit_test() - 执行单元测试
├── run_sql_test() - 执行SQL测试
├── compare_output_with_expected() - 输出比较
└── run_topic_tests() - 按题目执行测试
```

## 2. 每个题目的具体测试方法

### 题目1：存储管理
- **测试类型**: 单元测试
- **测试文件**: `unit_test`
- **测试内容**: 
  - LRU替换策略测试
  - 缓冲池管理器测试
  - 并发缓冲池测试
  - 存储测试
  - 记录管理器测试

### 题目2：查询执行
- **测试类型**: SQL集成测试
- **测试文件**: 
  - `test/query/query_sql/basic_query_test1.sql`
  - `test/query/query_sql/basic_query_test2.sql`
  - `test/query/query_sql/basic_query_test3.sql`
  - `test/query/query_sql/basic_query_test4.sql`
  - `test/query/query_sql/basic_query_test5.sql`
- **SQL语句类型**: CREATE TABLE, INSERT, SELECT, JOIN, WHERE, ORDER BY

### 题目3：唯一索引
- **测试类型**: 单元测试
- **测试文件**: 
  - `b_plus_tree_concurrent_test` ✅
  - `b_plus_tree_delete_test` ❌ (LargeScaleTest失败)
  - `b_plus_tree_insert_test` ✅
- **测试内容**: B+树插入、删除、并发操作

### 题目4：查询优化
- **测试类型**: SQL集成测试
- **测试文件**: 复用查询执行测试
- **SQL语句类型**: 复杂查询，验证优化器工作

### 题目5：聚合函数与分组统计
- **测试类型**: SQL集成测试
- **测试文件**: 
  - `test_agg.sql`
  - `test_agg2.sql`
  - `simple_agg_test.sql`
  - `single_agg_group_test.sql`
  - `test_empty_aggregate.sql`
  - `complete_empty_aggregate_test.sql`
- **SQL语句类型**: COUNT, MAX, MIN, SUM, AVG, GROUP BY, HAVING

### 题目6：半连接（Semi Join）
- **测试类型**: SQL集成测试
- **测试文件**: `test/query/query_sql/basic_query_test5.sql`
- **SQL语句类型**: 包含连接查询的复杂SQL

### 题目7：事务控制语句
- **测试类型**: SQL集成测试
- **测试文件**: 
  - `test/transaction/transaction_sql/commit_test.sql`
  - `test/transaction/transaction_sql/abort_test.sql`
  - `test/transaction/transaction_sql/commit_index_test.sql`
  - `test/transaction/transaction_sql/abort_index_test.sql`
- **SQL语句类型**: BEGIN, COMMIT, ABORT

### 题目8：多版本并发控制（MVCC）
- **测试类型**: SQL集成测试
- **测试文件**: 
  - `test/concurrency/concurrency_sql/dirty_read_test.sql`
  - `test/concurrency/concurrency_sql/dirty_write_test.sql`
  - `test/concurrency/concurrency_sql/unrepeatable_read_test.sql`
  - `test/concurrency/concurrency_sql/phantom_read_test_1.sql`
  - `test/concurrency/concurrency_sql/phantom_read_test_2.sql`
  - `test/concurrency/concurrency_sql/phantom_read_test_3.sql`
  - `test/concurrency/concurrency_sql/phantom_read_test_4.sql`
  - `test/concurrency/concurrency_sql/lost_update_test.sql`
- **SQL语句类型**: 并发事务测试

### 题目9：基于静态检查点的故障恢复
- **测试类型**: SQL集成测试
- **测试文件**: 
  - `test_basic_recovery.sql`
  - `test_crash_recovery.sql`
- **SQL语句类型**: 故障恢复相关操作

## 3. 测试文件组织结构和命名规范

### 3.1 目录结构
```
db2025-x1/
├── test/                           # 官方测试目录
│   ├── query/query_sql/           # 查询测试SQL文件
│   ├── transaction/transaction_sql/ # 事务测试SQL文件
│   └── concurrency/concurrency_sql/ # 并发测试SQL文件
├── build/bin/                      # 单元测试可执行文件
├── *.sql                          # 根目录SQL测试文件
└── *_test_suite.py                # 专项测试脚本
```

### 3.2 命名规范

**单元测试**:
- 格式: `{功能}_test`
- 示例: `unit_test`, `b_plus_tree_insert_test`

**SQL测试**:
- 格式: `{功能}_test.sql` 或 `test_{功能}.sql`
- 示例: `basic_query_test1.sql`, `test_agg.sql`

**期望输出**:
- 格式: `{测试文件名}_answer.txt`
- 示例: `basic_query_test1_answer.txt`

## 4. 测试执行流程

### 4.1 单元测试流程
1. 扫描 `build/bin/` 目录
2. 识别 `*_test` 可执行文件
3. 直接执行并解析输出
4. 根据返回码判断成功/失败

### 4.2 SQL测试流程
1. 启动RMDB服务器 (`./build/bin/rmdb {db_name}`)
2. 使用 `sql_test.py` 执行SQL文件
3. 检查输出文件 (`build/{db_name}/output.txt`)
4. 与期望输出比较（如果存在）
5. 停止服务器并清理

### 4.3 测试发现机制
- **单元测试**: 自动扫描 `build/bin/` 目录
- **SQL测试**: 预定义文件列表 + 存在性验证
- **智能分类**: 根据文件名和路径自动分配到对应题目

## 5. 根据《测试说明文档.txt》的验证方法

### 5.1 验证步骤
1. **环境检查**: 确保Python3和pexpect可用
2. **构建验证**: 自动构建服务端和客户端
3. **测试发现**: 列出所有发现的测试文件
4. **执行验证**: 按题目顺序执行所有测试
5. **结果统计**: 生成详细的通过/失败报告

### 5.2 符合性检查
- ✅ 覆盖所有9个题目
- ✅ 包含单元测试和SQL集成测试
- ✅ 自动化执行，无需手动干预
- ✅ 详细的日志记录和错误报告
- ✅ 支持选择性测试（按题目、按类型）

## 6. 使用方法

### 6.1 基本用法
```bash
# 运行所有测试
python3 comprehensive_test.py

# 运行指定题目
python3 comprehensive_test.py --topic 5

# 仅运行单元测试
python3 comprehensive_test.py --unit-only

# 仅运行SQL测试
python3 comprehensive_test.py --sql-only

# 列出所有测试
python3 comprehensive_test.py --list-tests
```

### 6.2 Shell脚本入口
```bash
# 使用Shell脚本
./run_all_tests.sh

# 显示帮助
./run_all_tests.sh --help
```

## 7. 当前测试结果

### 7.1 最新测试结果 (2025-07-21)
```
题目1 (存储管理): 1/1 通过 ✅
题目2 (查询执行): 5/5 通过 ✅
题目3 (唯一索引): 2/3 通过 ⚠️
题目4 (查询优化): 2/2 通过 ✅
题目5 (聚合函数): 6/6 通过 ✅
题目6 (半连接): 1/1 通过 ✅
题目7 (事务控制): 4/4 通过 ✅
题目8 (MVCC): 8/8 通过 ✅
题目9 (故障恢复): 2/2 通过 ✅

总体结果: 31/32 通过 (96.9%)
```

### 7.2 已知问题
- **题目3**: `b_plus_tree_delete_test` 中的 `LargeScaleTest` 失败
  - 原因: B+树删除操作在大规模测试中的实现问题
  - 状态: 代码实现问题，非测试框架问题

## 8. 故障排除指南

### 8.1 常见问题

**问题1: 服务器启动失败**
```bash
# 检查端口占用
netstat -tlnp | grep :8765

# 杀死残留进程
pkill -f rmdb
```

**问题2: 客户端连接超时**
```bash
# 增加服务器启动等待时间
# 在comprehensive_test.py中修改time.sleep(5)
```

**问题3: 权限问题**
```bash
chmod +x *.py *.sh
```

**问题4: Python依赖缺失**
```bash
pip3 install pexpect
```

### 8.2 调试方法

**启用详细日志**:
```bash
# 查看详细日志
cat comprehensive_test_log.txt
```

**单独测试**:
```bash
# 测试单个题目
python3 comprehensive_test.py --topic 1

# 测试单个SQL文件
python3 sql_test.py test_agg.sql ./rmdb_client/build/rmdb_client testdb ./build/bin/rmdb
```

**验证测试框架**:
```bash
python3 test_framework_validation.py
```

## 9. 扩展和维护

### 9.1 添加新测试
1. 将SQL文件放入相应目录或根目录
2. 更新 `comprehensive_test.py` 中的 `known_sql_tests` 字典
3. 运行 `--list-tests` 验证发现

### 9.2 修改测试配置
- 编辑 `comprehensive_test.py` 中的配置常量
- 调整超时时间、数据库名称等参数

### 9.3 性能优化
- 减少不必要的构建步骤
- 并行执行独立测试
- 优化服务器启动/停止流程

---

**版本**: 1.0  
**更新日期**: 2025-07-21  
**维护者**: db2025-x1 团队
