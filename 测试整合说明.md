# db2025-x1 全面测试整合方案

## 概述

本测试整合方案为db2025-x1数据库管理系统设计大赛决赛优化阶段提供了一个全面的测试套件，整合了所有9个题目的测试用例，包括单元测试（C++）和SQL集成测试。

## 文件结构

```
db2025-x1/
├── run_all_tests.sh              # 主测试脚本（Shell）
├── comprehensive_test.py         # 综合测试脚本（Python）
├── aggregate_test_suite.py       # 聚合函数专项测试
├── crash_recovery_test_suite.py  # 故障恢复专项测试
├── 测试整合说明.md               # 本文档
└── comprehensive_test_log.txt     # 测试日志（运行后生成）
```

## 测试覆盖范围

### 题目1: 存储管理
- **单元测试**: buffer_pool_manager_test, disk_manager_test, lru_replacer_test, record_manager_test
- **测试内容**: 磁盘管理器、缓冲池管理器、LRU替换策略、记录管理器

### 题目2: 查询执行
- **SQL测试**: 建表、插入、查询、更新、删除、连接查询
- **测试点**: 5个基础查询测试点

### 题目3: 唯一索引
- **SQL测试**: 索引创建/删除/展示、索引查询、索引维护、性能测试
- **测试点**: 5个索引相关测试点

### 题目4: 查询优化
- **SQL测试**: 选择运算下推、投影下推、连接顺序优化、稳健性测试
- **测试点**: EXPLAIN语句支持

### 题目5: 聚合函数与分组统计
- **专项测试**: 单独聚合函数、分组统计、健壮性测试、ORDER BY测试
- **支持函数**: COUNT, MAX, MIN, SUM, AVG, GROUP BY, HAVING, ORDER BY

### 题目6: 半连接（Semi Join）
- **SQL测试**: 基本Semi Join、重复匹配处理、空表处理、健壮性测试
- **测试点**: 5个Semi Join测试点

### 题目7: 事务控制语句
- **SQL测试**: 事务提交、事务回滚、索引事务测试
- **测试点**: 4个事务控制测试点

### 题目8: 多版本并发控制（MVCC）
- **SQL测试**: 脏读、脏写、不可重复读、幻读、丢失更新测试
- **测试点**: 8个并发控制测试点

### 题目9: 基于静态检查点的故障恢复
- **专项测试**: 6个故障恢复测试点，包括性能测试
- **测试内容**: 单/多线程、索引、大数据、检查点性能对比

## 使用方法

### 1. 快速开始

```bash
# 运行所有测试（推荐）
./run_all_tests.sh

# 显示帮助信息
./run_all_tests.sh --help
```

### 2. 分类测试

```bash
# 仅运行单元测试
./run_all_tests.sh --unit-only

# 仅运行SQL测试
./run_all_tests.sh --sql-only

# 运行综合测试
./run_all_tests.sh --comprehensive
```

### 3. 指定题目测试

```bash
# 测试指定题目（1-9）
./run_all_tests.sh --topic 5

# 使用Python脚本测试指定题目
python3 comprehensive_test.py --topic 5
```

### 4. 专项测试

```bash
# 聚合函数专项测试
python3 aggregate_test_suite.py

# 故障恢复专项测试
python3 crash_recovery_test_suite.py
```

### 5. 高级选项

```bash
# 跳过构建步骤
./run_all_tests.sh --no-build

# 仅构建项目
./run_all_tests.sh --build-only

# 列出所有发现的测试
python3 comprehensive_test.py --list-tests
```

## 依赖要求

### 系统依赖
- Linux操作系统
- Python 3.6+
- 已配置好的构建环境（build和rmdb_client/build目录）

### Python依赖
```bash
pip3 install pexpect
```

**注意**: 环境已预配置，无需手动CMake配置。测试脚本会直接使用make命令进行构建。

## 测试输出

### 1. 控制台输出
- 实时显示测试进度
- 彩色日志输出（成功/警告/错误）
- 测试结果统计

### 2. 日志文件
- `comprehensive_test_log.txt`: 详细测试日志
- `build/{db_name}/output.txt`: 数据库输出文件

### 3. 测试报告
- 每个题目的通过/失败统计
- 总体测试结果汇总
- 性能测试结果（题目9）

## 特殊功能

### 1. 自动测试发现
- 自动扫描test目录下的测试文件
- 智能分类测试到对应题目
- 支持多种测试文件命名模式

### 2. 输出比较
- 自动比较实际输出与期望输出
- 支持多种期望输出文件格式
- 详细的差异报告

### 3. 故障恢复测试
- 模拟服务器崩溃
- 自动测量恢复时间
- 检查点性能验证（70%要求）

### 4. 并发测试支持
- 多线程事务测试
- MVCC隔离级别验证
- 死锁检测测试

## 故障排除

### 1. 编译错误
```bash
# 重新构建
./run_all_tests.sh
```

### 2. 测试失败
```bash
# 查看详细日志
cat comprehensive_test_log.txt

# 运行单个测试
python3 comprehensive_test.py --topic 1
```

### 3. 服务器启动失败
```bash
# 检查端口占用
netstat -tlnp | grep :8765

# 杀死残留进程
pkill -f rmdb
```

### 4. 权限问题
```bash
# 添加执行权限
chmod +x *.py *.sh
```

## 性能要求验证

### 题目3: 索引性能
- 自动验证索引查询性能提升
- 要求：建立索引后查询时间 ≤ 70% 原时间

### 题目9: 检查点性能
- 自动测量恢复时间对比
- 要求：检查点恢复时间 ≤ 70% 无检查点恢复时间

## 扩展说明

### 1. 添加新测试
- 将SQL文件放入相应的test子目录
- 测试会被自动发现和分类
- 支持自定义期望输出文件

### 2. 修改测试配置
- 编辑`comprehensive_test.py`中的配置
- 调整超时时间、数据库名称等参数

### 3. 集成CI/CD
- 脚本支持非交互式运行
- 返回适当的退出码
- 生成标准化的测试报告

## 注意事项

1. **测试顺序**: 某些测试可能有依赖关系，建议按顺序运行
2. **资源清理**: 测试会自动清理临时文件和进程
3. **并发限制**: 避免同时运行多个测试实例
4. **磁盘空间**: 确保有足够空间存储测试数据库
5. **网络端口**: 确保默认端口（8765）未被占用

## 联系支持

如遇到问题，请检查：
1. 测试日志文件
2. 系统依赖是否完整
3. 代码是否为最新版本
4. 测试环境是否干净

本测试套件旨在为db2025-x1项目提供全面、可靠的测试覆盖，确保所有功能模块的正确性和性能要求。
