#!/bin/bash

# db2025-x1 全面测试整合脚本
# 该脚本整合了所有9个题目的测试用例

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查依赖
check_dependencies() {
    log_info "检查依赖..."
    
    # 检查Python3
    if ! command -v python3 &> /dev/null; then
        log_error "Python3 未安装"
        exit 1
    fi
    
    # 检查必要的Python包
    python3 -c "import pexpect" 2>/dev/null || {
        log_error "pexpect 包未安装，请运行: pip3 install pexpect"
        exit 1
    }
    
    # 检查CMake
    if ! command -v cmake &> /dev/null; then
        log_error "CMake 未安装"
        exit 1
    fi
    
    # 检查make
    if ! command -v make &> /dev/null; then
        log_error "make 未安装"
        exit 1
    fi
    
    log_success "依赖检查通过"
}

# 构建项目
build_project() {
    log_info "开始构建项目..."
    
    # 创建构建目录
    if [ ! -d "build" ]; then
        mkdir build
    fi
    
    cd build
    
    # CMake配置
    log_info "运行CMake配置..."
    cmake .. || {
        log_error "CMake配置失败"
        exit 1
    }
    
    # 编译服务端
    log_info "编译服务端..."
    make rmdb -j4 || {
        log_error "服务端编译失败"
        exit 1
    }
    
    # 编译所有测试
    log_info "编译测试..."
    make -j4 || {
        log_warning "部分测试编译失败，继续..."
    }
    
    cd ..
    
    # 构建客户端
    log_info "构建客户端..."
    if [ ! -d "rmdb_client/build" ]; then
        mkdir -p rmdb_client/build
    fi
    
    cd rmdb_client/build
    
    cmake .. || {
        log_error "客户端CMake配置失败"
        exit 1
    }
    
    make rmdb_client -j4 || {
        log_error "客户端编译失败"
        exit 1
    }
    
    cd ../..
    
    log_success "项目构建完成"
}

# 运行单元测试
run_unit_tests() {
    log_info "===== 运行单元测试 ====="
    
    # 题目1: 存储管理
    log_info "运行题目1: 存储管理单元测试"
    
    unit_tests=(
        "buffer_pool_manager_test"
        "disk_manager_test"
        "lru_replacer_test"
        "record_manager_test"
    )
    
    passed=0
    total=${#unit_tests[@]}
    
    for test in "${unit_tests[@]}"; do
        if [ -f "build/bin/$test" ]; then
            log_info "运行 $test..."
            if ./build/bin/$test; then
                log_success "$test 通过"
                ((passed++))
            else
                log_error "$test 失败"
            fi
        else
            log_warning "$test 可执行文件不存在，跳过"
        fi
    done
    
    log_info "单元测试结果: $passed/$total 通过"
}

# 运行SQL集成测试
run_sql_tests() {
    log_info "===== 运行SQL集成测试 ====="
    
    # 题目2-4: 查询执行和优化
    log_info "运行题目2-4: 查询执行和优化测试"
    if [ -f "test/query/query_test_basic.py" ]; then
        cd build
        python3 ../test/query/query_test_basic.py || log_warning "查询测试失败"
        cd ..
    else
        log_warning "查询测试脚本不存在"
    fi
    
    # 题目5: 聚合函数
    log_info "运行题目5: 聚合函数与分组统计测试"
    if [ -f "aggregate_test_suite.py" ]; then
        python3 aggregate_test_suite.py || log_warning "聚合测试失败"
    else
        log_warning "聚合测试脚本不存在"
    fi
    
    # 题目7: 事务控制
    log_info "运行题目7: 事务控制语句测试"
    if [ -f "test/transaction/transaction_test.py" ]; then
        python3 test/transaction/transaction_test.py || log_warning "事务测试失败"
    else
        log_warning "事务测试脚本不存在"
    fi
    
    # 题目8: MVCC
    log_info "运行题目8: 多版本并发控制测试"
    if [ -f "test/concurrency/concurrency_test.py" ]; then
        python3 test/concurrency/concurrency_test.py || log_warning "并发测试失败"
    else
        log_warning "并发测试脚本不存在"
    fi
    
    # 题目9: 故障恢复
    log_info "运行题目9: 基于静态检查点的故障恢复测试"
    if [ -f "crash_recovery_test_suite.py" ]; then
        python3 crash_recovery_test_suite.py || log_warning "故障恢复测试失败"
    else
        log_warning "故障恢复测试脚本不存在"
    fi
}

# 运行综合测试
run_comprehensive_tests() {
    log_info "===== 运行综合测试 ====="
    
    if [ -f "comprehensive_test.py" ]; then
        python3 comprehensive_test.py || log_warning "综合测试失败"
    else
        log_warning "综合测试脚本不存在"
    fi
}

# 清理函数
cleanup() {
    log_info "清理测试环境..."
    
    # 杀死可能残留的进程
    pkill -f rmdb || true
    
    # 清理测试数据库
    rm -rf build/*_test_db* || true
    rm -rf build/testdb* || true
    
    log_success "清理完成"
}

# 显示帮助信息
show_help() {
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  -h, --help          显示此帮助信息"
    echo "  -b, --build-only    仅构建项目"
    echo "  -u, --unit-only     仅运行单元测试"
    echo "  -s, --sql-only      仅运行SQL测试"
    echo "  -c, --comprehensive 运行综合测试"
    echo "  --no-build          跳过构建步骤"
    echo "  --topic N           仅测试指定题目 (1-9)"
    echo ""
    echo "示例:"
    echo "  $0                  运行所有测试"
    echo "  $0 --unit-only      仅运行单元测试"
    echo "  $0 --topic 5        仅测试题目5"
}

# 主函数
main() {
    local build_only=false
    local unit_only=false
    local sql_only=false
    local comprehensive=false
    local no_build=false
    local topic=""
    
    # 解析命令行参数
    while [[ $# -gt 0 ]]; do
        case $1 in
            -h|--help)
                show_help
                exit 0
                ;;
            -b|--build-only)
                build_only=true
                shift
                ;;
            -u|--unit-only)
                unit_only=true
                shift
                ;;
            -s|--sql-only)
                sql_only=true
                shift
                ;;
            -c|--comprehensive)
                comprehensive=true
                shift
                ;;
            --no-build)
                no_build=true
                shift
                ;;
            --topic)
                topic="$2"
                shift 2
                ;;
            *)
                log_error "未知选项: $1"
                show_help
                exit 1
                ;;
        esac
    done
    
    # 设置陷阱以确保清理
    trap cleanup EXIT
    
    log_info "开始 db2025-x1 全面测试..."
    
    # 检查依赖
    check_dependencies
    
    # 构建项目
    if [ "$no_build" = false ]; then
        build_project
    fi
    
    if [ "$build_only" = true ]; then
        log_success "构建完成"
        exit 0
    fi
    
    # 根据参数运行相应测试
    if [ "$comprehensive" = true ]; then
        run_comprehensive_tests
    elif [ "$unit_only" = true ]; then
        run_unit_tests
    elif [ "$sql_only" = true ]; then
        run_sql_tests
    elif [ -n "$topic" ]; then
        log_info "运行题目 $topic 的测试"
        if [ -f "comprehensive_test.py" ]; then
            python3 comprehensive_test.py --topic "$topic"
        else
            log_error "综合测试脚本不存在"
            exit 1
        fi
    else
        # 运行所有测试
        run_unit_tests
        run_sql_tests
        run_comprehensive_tests
    fi
    
    log_success "测试完成"
}

# 运行主函数
main "$@"
