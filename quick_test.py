#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
快速测试脚本 - 简化版
直接运行测试，无需环境检查和CMake配置
"""

import os
import sys
import time
import subprocess
import signal
import pexpect
import argparse
from datetime import datetime

# 配置
SERVER_EXEC = "./build/bin/rmdb"
CLIENT_EXEC = "./rmdb_client/build/rmdb_client"

def log_message(message, level="INFO"):
    """记录日志消息"""
    timestamp = datetime.now().strftime("%H:%M:%S")
    colors = {
        "INFO": "\033[0;34m",
        "SUCCESS": "\033[0;32m", 
        "WARNING": "\033[1;33m",
        "ERROR": "\033[0;31m"
    }
    color = colors.get(level, "")
    reset = "\033[0m"
    print(f"{color}[{timestamp}] [{level}]{reset} {message}")

def build_if_needed():
    """按需构建项目"""
    log_message("检查并构建项目...")
    
    # 构建服务端
    if not os.path.exists(SERVER_EXEC):
        log_message("构建服务端...")
        result = subprocess.run("make rmdb -j4", shell=True, cwd="./build")
        if result.returncode != 0:
            log_message("服务端构建失败", "ERROR")
            return False
    
    # 构建客户端
    if not os.path.exists(CLIENT_EXEC):
        log_message("构建客户端...")
        result = subprocess.run("make rmdb_client -j4", shell=True, cwd="./rmdb_client/build")
        if result.returncode != 0:
            log_message("客户端构建失败", "ERROR")
            return False
    
    log_message("构建完成", "SUCCESS")
    return True

def run_unit_test(test_name):
    """运行单元测试"""
    test_path = f"./build/bin/{test_name}"
    if not os.path.exists(test_path):
        log_message(f"测试文件不存在: {test_path}", "WARNING")
        return False
    
    log_message(f"运行单元测试: {test_name}")
    result = subprocess.run(test_path, capture_output=True, text=True)
    
    if result.returncode == 0:
        log_message(f"✓ {test_name} 通过", "SUCCESS")
        return True
    else:
        log_message(f"✗ {test_name} 失败", "ERROR")
        if result.stderr:
            log_message(f"错误: {result.stderr[:200]}...", "ERROR")
        return False

def start_server(db_name):
    """启动服务器"""
    os.makedirs(f"./build/{db_name}", exist_ok=True)
    
    try:
        process = subprocess.Popen(
            f"{SERVER_EXEC} {db_name}",
            shell=True,
            stdout=subprocess.DEVNULL,
            stderr=subprocess.DEVNULL,
            preexec_fn=os.setsid
        )
        time.sleep(2)
        return process
    except Exception as e:
        log_message(f"启动服务器失败: {e}", "ERROR")
        return None

def stop_server(process):
    """停止服务器"""
    if process:
        try:
            os.killpg(os.getpgid(process.pid), signal.SIGTERM)
            process.wait(timeout=3)
        except:
            try:
                os.killpg(os.getpgid(process.pid), signal.SIGKILL)
            except:
                pass

def run_sql_test(sql_file, db_name):
    """运行SQL测试"""
    if not os.path.exists(sql_file):
        log_message(f"SQL文件不存在: {sql_file}", "WARNING")
        return False
    
    log_message(f"运行SQL测试: {os.path.basename(sql_file)}")
    
    # 启动服务器
    server = start_server(db_name)
    if not server:
        return False
    
    try:
        # 读取SQL命令
        with open(sql_file, 'r') as f:
            commands = [line.strip() for line in f if line.strip() and not line.strip().startswith('--')]
        
        # 连接客户端
        client = pexpect.spawn(CLIENT_EXEC, encoding='utf-8')
        client.logfile = None
        
        try:
            client.expect('Rucbase> ', timeout=10)
            
            # 执行SQL命令
            for cmd in commands:
                client.sendline(cmd)
                try:
                    client.expect('Rucbase> ', timeout=30)
                except pexpect.TIMEOUT:
                    log_message(f"SQL命令超时: {cmd}", "WARNING")
                    continue
            
            client.close()
            log_message(f"✓ SQL测试完成: {os.path.basename(sql_file)}", "SUCCESS")
            return True
            
        except Exception as e:
            log_message(f"SQL测试执行失败: {e}", "ERROR")
            return False
        finally:
            client.close()
    
    finally:
        stop_server(server)

def run_topic_tests(topic_id):
    """运行指定题目的测试"""
    log_message(f"\n===== 题目 {topic_id} 测试 =====")
    
    passed = 0
    total = 0
    
    # 题目1: 存储管理 - 单元测试
    if topic_id == 1:
        unit_tests = ["buffer_pool_manager_test", "disk_manager_test", "lru_replacer_test", "record_manager_test"]
        for test in unit_tests:
            total += 1
            if run_unit_test(test):
                passed += 1
    
    # 题目2: 查询执行 - SQL测试
    elif topic_id == 2:
        sql_tests = [
            "test/query/query_sql/basic_query_test1.sql",
            "test/query/query_sql/basic_query_test2.sql",
            "test/query/query_sql/basic_query_test3.sql",
            "test/query/query_sql/basic_query_test4.sql",
            "test/query/query_sql/basic_query_test5.sql"
        ]
        for sql_file in sql_tests:
            total += 1
            if run_sql_test(sql_file, f"topic2_test_db"):
                passed += 1
    
    # 题目5: 聚合函数 - 专项测试
    elif topic_id == 5:
        log_message("运行聚合函数专项测试...")
        result = subprocess.run("python3 aggregate_test_suite.py", shell=True)
        total += 1
        if result.returncode == 0:
            passed += 1
            log_message("✓ 聚合函数测试通过", "SUCCESS")
        else:
            log_message("✗ 聚合函数测试失败", "ERROR")
    
    # 题目7: 事务控制 - SQL测试
    elif topic_id == 7:
        sql_tests = [
            "test/transaction/transaction_sql/commit_test.sql",
            "test/transaction/transaction_sql/abort_test.sql"
        ]
        for sql_file in sql_tests:
            total += 1
            if run_sql_test(sql_file, f"topic7_test_db"):
                passed += 1
    
    # 题目9: 故障恢复 - 专项测试
    elif topic_id == 9:
        log_message("运行故障恢复专项测试...")
        result = subprocess.run("python3 crash_recovery_test_suite.py", shell=True)
        total += 1
        if result.returncode == 0:
            passed += 1
            log_message("✓ 故障恢复测试通过", "SUCCESS")
        else:
            log_message("✗ 故障恢复测试失败", "ERROR")
    
    else:
        log_message(f"题目 {topic_id} 暂未实现快速测试", "WARNING")
    
    log_message(f"题目 {topic_id} 结果: {passed}/{total} 通过")
    return passed, total

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="db2025-x1 快速测试脚本")
    parser.add_argument("--topic", type=int, help="测试指定题目 (1,2,5,7,9)")
    parser.add_argument("--no-build", action="store_true", help="跳过构建检查")
    args = parser.parse_args()
    
    log_message("db2025-x1 快速测试脚本")
    log_message("=" * 40)
    
    # 构建检查
    if not args.no_build:
        if not build_if_needed():
            log_message("构建失败，退出", "ERROR")
            return 1
    
    # 运行测试
    if args.topic:
        passed, total = run_topic_tests(args.topic)
    else:
        # 运行主要题目测试
        total_passed = 0
        total_tests = 0
        
        for topic in [1, 2, 5, 7, 9]:
            passed, tests = run_topic_tests(topic)
            total_passed += passed
            total_tests += tests
        
        passed, total = total_passed, total_tests
    
    # 总结
    log_message(f"\n===== 测试完成 =====")
    log_message(f"总体结果: {passed}/{total} 通过")
    
    if passed == total:
        log_message("所有测试通过!", "SUCCESS")
        return 0
    else:
        log_message(f"有 {total - passed} 个测试失败", "WARNING")
        return 1

if __name__ == "__main__":
    sys.exit(main())
