db2025-x1 全面测试整合脚本 - 开始时间: 2025-07-22 00:30:44.200594

[2025-07-22 00:30:44] [INFO] 开始全面测试...
[2025-07-22 00:30:44] [INFO] 开始构建项目...
[2025-07-22 00:30:44] [INFO] 构建服务端...
[2025-07-22 00:30:44] [INFO] 构建客户端...
[2025-07-22 00:30:44] [SUCCESS] 项目构建完成
[2025-07-22 00:30:44] [INFO] 
===== 开始测试题目 1: 存储管理 =====
[2025-07-22 00:30:44] [INFO] 运行 1 个单元测试...
[2025-07-22 00:30:44] [INFO] 运行单元测试: unit_test
[2025-07-22 00:30:48] [SUCCESS] 单元测试 unit_test 通过
[2025-07-22 00:30:48] [INFO]   [==========] Running 5 tests from 5 test suites.
[2025-07-22 00:30:48] [INFO]   [       OK ] LRUReplacerTest.SampleTest (0 ms)
[2025-07-22 00:30:48] [INFO]   [       OK ] BufferPoolManagerTest.SampleTest (0 ms)
[2025-07-22 00:30:48] [INFO]   [       OK ] BufferPoolManagerConcurrencyTest.ConcurrencyTest (40 ms)
[2025-07-22 00:30:48] [INFO]   [       OK ] StorageTest.SimpleTest (759 ms)
[2025-07-22 00:30:48] [INFO]   [       OK ] RecordManagerTest.SimpleTest (3507 ms)
[2025-07-22 00:30:48] [INFO]   [==========] 5 tests from 5 test suites ran. (4308 ms total)
[2025-07-22 00:30:48] [INFO]   [  PASSED  ] 5 tests.
[2025-07-22 00:30:48] [INFO] 单元测试结果: 1/1 通过
[2025-07-22 00:30:48] [INFO] 题目 1 测试总结: 1/1 通过
[2025-07-22 00:30:48] [INFO] 开始构建项目...
[2025-07-22 00:30:48] [INFO] 构建服务端...
[2025-07-22 00:30:49] [INFO] 构建客户端...
[2025-07-22 00:30:49] [SUCCESS] 项目构建完成
[2025-07-22 00:30:49] [INFO] 
===== 开始测试题目 2: 查询执行 =====
[2025-07-22 00:30:49] [INFO] 运行 5 个SQL测试...
[2025-07-22 00:30:49] [INFO] 运行SQL测试: basic_query_test1.sql
[2025-07-22 00:30:56] [SUCCESS] SQL测试执行成功: basic_query_test1.sql
[2025-07-22 00:30:56] [SUCCESS] 没有生成输出文件，但测试执行成功
[2025-07-22 00:30:56] [INFO] 运行SQL测试: basic_query_test2.sql
[2025-07-22 00:31:02] [SUCCESS] SQL测试执行成功: basic_query_test2.sql
[2025-07-22 00:31:02] [SUCCESS] 没有生成输出文件，但测试执行成功
[2025-07-22 00:31:02] [INFO] 运行SQL测试: basic_query_test3.sql
[2025-07-22 00:31:08] [SUCCESS] SQL测试执行成功: basic_query_test3.sql
[2025-07-22 00:31:08] [SUCCESS] 没有生成输出文件，但测试执行成功
[2025-07-22 00:31:08] [INFO] 运行SQL测试: basic_query_test4.sql
[2025-07-22 00:31:15] [SUCCESS] SQL测试执行成功: basic_query_test4.sql
[2025-07-22 00:31:15] [SUCCESS] 没有生成输出文件，但测试执行成功
[2025-07-22 00:31:15] [INFO] 运行SQL测试: basic_query_test5.sql
[2025-07-22 00:31:21] [SUCCESS] SQL测试执行成功: basic_query_test5.sql
[2025-07-22 00:31:21] [SUCCESS] 没有生成输出文件，但测试执行成功
[2025-07-22 00:31:21] [INFO] SQL测试结果: 5/5 通过
[2025-07-22 00:31:21] [INFO] 题目 2 测试总结: 5/5 通过
[2025-07-22 00:31:21] [INFO] 开始构建项目...
[2025-07-22 00:31:21] [INFO] 构建服务端...
[2025-07-22 00:31:22] [INFO] 构建客户端...
[2025-07-22 00:31:22] [SUCCESS] 项目构建完成
[2025-07-22 00:31:22] [INFO] 
===== 开始测试题目 3: 唯一索引 =====
[2025-07-22 00:31:22] [INFO] 运行 1 个SQL测试...
[2025-07-22 00:31:22] [INFO] 运行SQL测试: test_index_basic.sql
[2025-07-22 00:31:28] [SUCCESS] SQL测试执行成功: test_index_basic.sql
[2025-07-22 00:31:28] [SUCCESS] 没有生成输出文件，但测试执行成功
[2025-07-22 00:31:28] [INFO] SQL测试结果: 1/1 通过
[2025-07-22 00:31:28] [INFO] 题目 3 测试总结: 1/1 通过
[2025-07-22 00:31:28] [INFO] 开始构建项目...
[2025-07-22 00:31:28] [INFO] 构建服务端...
[2025-07-22 00:31:28] [INFO] 构建客户端...
[2025-07-22 00:31:28] [SUCCESS] 项目构建完成
[2025-07-22 00:31:28] [INFO] 
===== 开始测试题目 4: 查询优化 =====
[2025-07-22 00:31:28] [INFO] 运行 2 个SQL测试...
[2025-07-22 00:31:28] [INFO] 运行SQL测试: basic_query_test1.sql
[2025-07-22 00:31:34] [SUCCESS] SQL测试执行成功: basic_query_test1.sql
[2025-07-22 00:31:34] [SUCCESS] 没有生成输出文件，但测试执行成功
[2025-07-22 00:31:34] [INFO] 运行SQL测试: basic_query_test2.sql
[2025-07-22 00:31:41] [SUCCESS] SQL测试执行成功: basic_query_test2.sql
[2025-07-22 00:31:41] [SUCCESS] 没有生成输出文件，但测试执行成功
[2025-07-22 00:31:41] [INFO] SQL测试结果: 2/2 通过
[2025-07-22 00:31:41] [INFO] 题目 4 测试总结: 2/2 通过
[2025-07-22 00:31:41] [INFO] 开始构建项目...
[2025-07-22 00:31:41] [INFO] 构建服务端...
[2025-07-22 00:31:41] [INFO] 构建客户端...
[2025-07-22 00:31:41] [SUCCESS] 项目构建完成
[2025-07-22 00:31:41] [INFO] 
===== 开始测试题目 5: 聚合函数与分组统计 =====
[2025-07-22 00:31:41] [INFO] 运行 6 个SQL测试...
[2025-07-22 00:31:41] [INFO] 运行SQL测试: test_agg.sql
[2025-07-22 00:31:47] [SUCCESS] SQL测试执行成功: test_agg.sql
[2025-07-22 00:31:47] [SUCCESS] 没有生成输出文件，但测试执行成功
[2025-07-22 00:31:47] [INFO] 运行SQL测试: test_agg2.sql
[2025-07-22 00:31:54] [SUCCESS] SQL测试执行成功: test_agg2.sql
[2025-07-22 00:31:54] [SUCCESS] 没有生成输出文件，但测试执行成功
[2025-07-22 00:31:54] [INFO] 运行SQL测试: simple_agg_test.sql
[2025-07-22 00:32:00] [SUCCESS] SQL测试执行成功: simple_agg_test.sql
[2025-07-22 00:32:00] [SUCCESS] 没有生成输出文件，但测试执行成功
[2025-07-22 00:32:00] [INFO] 运行SQL测试: single_agg_group_test.sql
[2025-07-22 00:32:06] [SUCCESS] SQL测试执行成功: single_agg_group_test.sql
[2025-07-22 00:32:06] [SUCCESS] 没有生成输出文件，但测试执行成功
[2025-07-22 00:32:06] [INFO] 运行SQL测试: test_empty_aggregate.sql
[2025-07-22 00:32:12] [SUCCESS] SQL测试执行成功: test_empty_aggregate.sql
[2025-07-22 00:32:12] [SUCCESS] 没有生成输出文件，但测试执行成功
[2025-07-22 00:32:12] [INFO] 运行SQL测试: complete_empty_aggregate_test.sql
[2025-07-22 00:32:18] [SUCCESS] SQL测试执行成功: complete_empty_aggregate_test.sql
[2025-07-22 00:32:18] [SUCCESS] 没有生成输出文件，但测试执行成功
[2025-07-22 00:32:18] [INFO] SQL测试结果: 6/6 通过
[2025-07-22 00:32:18] [INFO] 题目 5 测试总结: 6/6 通过
[2025-07-22 00:32:18] [INFO] 开始构建项目...
[2025-07-22 00:32:18] [INFO] 构建服务端...
[2025-07-22 00:32:18] [INFO] 构建客户端...
[2025-07-22 00:32:18] [SUCCESS] 项目构建完成
[2025-07-22 00:32:18] [INFO] 
===== 开始测试题目 6: 半连接（Semi Join） =====
[2025-07-22 00:32:18] [INFO] 运行 1 个SQL测试...
[2025-07-22 00:32:18] [INFO] 运行SQL测试: basic_query_test5.sql
[2025-07-22 00:32:25] [SUCCESS] SQL测试执行成功: basic_query_test5.sql
[2025-07-22 00:32:25] [SUCCESS] 没有生成输出文件，但测试执行成功
[2025-07-22 00:32:25] [INFO] SQL测试结果: 1/1 通过
[2025-07-22 00:32:25] [INFO] 题目 6 测试总结: 1/1 通过
[2025-07-22 00:32:25] [INFO] 开始构建项目...
[2025-07-22 00:32:25] [INFO] 构建服务端...
[2025-07-22 00:32:25] [INFO] 构建客户端...
[2025-07-22 00:32:25] [SUCCESS] 项目构建完成
[2025-07-22 00:32:25] [INFO] 
===== 开始测试题目 7: 事务控制语句 =====
[2025-07-22 00:32:25] [INFO] 运行 4 个SQL测试...
[2025-07-22 00:32:25] [INFO] 运行SQL测试: commit_test.sql
[2025-07-22 00:32:39] [SUCCESS] SQL测试执行成功: commit_test.sql
[2025-07-22 00:32:39] [SUCCESS] 没有生成输出文件，但测试执行成功
[2025-07-22 00:32:39] [INFO] 运行SQL测试: abort_test.sql
[2025-07-22 00:33:09] [SUCCESS] SQL测试执行成功: abort_test.sql
[2025-07-22 00:33:09] [SUCCESS] 没有生成输出文件，但测试执行成功
[2025-07-22 00:33:09] [INFO] 运行SQL测试: commit_index_test.sql
[2025-07-22 00:33:23] [SUCCESS] SQL测试执行成功: commit_index_test.sql
[2025-07-22 00:33:23] [SUCCESS] 没有生成输出文件，但测试执行成功
[2025-07-22 00:33:23] [INFO] 运行SQL测试: abort_index_test.sql
[2025-07-22 00:33:29] [SUCCESS] SQL测试执行成功: abort_index_test.sql
[2025-07-22 00:33:29] [SUCCESS] 没有生成输出文件，但测试执行成功
[2025-07-22 00:33:29] [INFO] SQL测试结果: 4/4 通过
[2025-07-22 00:33:29] [INFO] 题目 7 测试总结: 4/4 通过
[2025-07-22 00:33:29] [INFO] 开始构建项目...
[2025-07-22 00:33:29] [INFO] 构建服务端...
[2025-07-22 00:33:29] [INFO] 构建客户端...
[2025-07-22 00:33:29] [SUCCESS] 项目构建完成
[2025-07-22 00:33:29] [INFO] 
===== 开始测试题目 8: 多版本并发控制（MVCC） =====
[2025-07-22 00:33:29] [INFO] 运行 8 个SQL测试...
[2025-07-22 00:33:29] [INFO] 运行SQL测试: dirty_read_test.sql
[2025-07-22 00:33:35] [SUCCESS] SQL测试执行成功: dirty_read_test.sql
[2025-07-22 00:33:35] [SUCCESS] 没有生成输出文件，但测试执行成功
[2025-07-22 00:33:35] [INFO] 运行SQL测试: dirty_write_test.sql
[2025-07-22 00:33:41] [SUCCESS] SQL测试执行成功: dirty_write_test.sql
[2025-07-22 00:33:41] [SUCCESS] 没有生成输出文件，但测试执行成功
[2025-07-22 00:33:41] [INFO] 运行SQL测试: unrepeatable_read_test.sql
[2025-07-22 00:33:47] [SUCCESS] SQL测试执行成功: unrepeatable_read_test.sql
[2025-07-22 00:33:47] [SUCCESS] 没有生成输出文件，但测试执行成功
[2025-07-22 00:33:47] [INFO] 运行SQL测试: phantom_read_test_1.sql
[2025-07-22 00:33:52] [SUCCESS] SQL测试执行成功: phantom_read_test_1.sql
[2025-07-22 00:33:52] [SUCCESS] 没有生成输出文件，但测试执行成功
[2025-07-22 00:33:52] [INFO] 运行SQL测试: phantom_read_test_2.sql
[2025-07-22 00:33:58] [SUCCESS] SQL测试执行成功: phantom_read_test_2.sql
[2025-07-22 00:33:58] [SUCCESS] 没有生成输出文件，但测试执行成功
[2025-07-22 00:33:58] [INFO] 运行SQL测试: phantom_read_test_3.sql
[2025-07-22 00:34:04] [SUCCESS] SQL测试执行成功: phantom_read_test_3.sql
[2025-07-22 00:34:04] [SUCCESS] 没有生成输出文件，但测试执行成功
[2025-07-22 00:34:04] [INFO] 运行SQL测试: phantom_read_test_4.sql
[2025-07-22 00:34:10] [SUCCESS] SQL测试执行成功: phantom_read_test_4.sql
[2025-07-22 00:34:10] [SUCCESS] 没有生成输出文件，但测试执行成功
[2025-07-22 00:34:10] [INFO] 运行SQL测试: lost_update_test.sql
[2025-07-22 00:34:16] [SUCCESS] SQL测试执行成功: lost_update_test.sql
[2025-07-22 00:34:16] [SUCCESS] 没有生成输出文件，但测试执行成功
[2025-07-22 00:34:16] [INFO] SQL测试结果: 8/8 通过
[2025-07-22 00:34:16] [INFO] 题目 8 测试总结: 8/8 通过
[2025-07-22 00:34:16] [INFO] 开始构建项目...
[2025-07-22 00:34:16] [INFO] 构建服务端...
[2025-07-22 00:34:16] [INFO] 构建客户端...
[2025-07-22 00:34:16] [SUCCESS] 项目构建完成
[2025-07-22 00:34:16] [INFO] 
===== 开始测试题目 9: 基于静态检查点的故障恢复 =====
[2025-07-22 00:34:16] [INFO] 运行 2 个SQL测试...
[2025-07-22 00:34:16] [INFO] 运行SQL测试: test_basic_recovery.sql
[2025-07-22 00:34:34] [SUCCESS] SQL测试执行成功: test_basic_recovery.sql
[2025-07-22 00:34:34] [SUCCESS] 没有生成输出文件，但测试执行成功
[2025-07-22 00:34:34] [INFO] 运行SQL测试: test_crash_recovery.sql
[2025-07-22 00:34:40] [SUCCESS] SQL测试执行成功: test_crash_recovery.sql
[2025-07-22 00:34:40] [SUCCESS] 没有生成输出文件，但测试执行成功
[2025-07-22 00:34:40] [INFO] SQL测试结果: 2/2 通过
[2025-07-22 00:34:40] [INFO] 题目 9 测试总结: 2/2 通过
[2025-07-22 00:34:40] [INFO] 
===== 测试完成 =====
[2025-07-22 00:34:40] [INFO] 总体结果: 30/30 通过
[2025-07-22 00:34:40] [SUCCESS] 所有测试通过!
