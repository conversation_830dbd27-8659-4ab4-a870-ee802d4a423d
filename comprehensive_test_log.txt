db2025-x1 全面测试整合脚本 - 开始时间: 2025-07-21 23:24:54.095554

[2025-07-21 23:24:54] [INFO] 开始全面测试...
[2025-07-21 23:24:54] [INFO] 开始构建项目...
[2025-07-21 23:24:54] [INFO] 构建服务端...
[2025-07-21 23:24:54] [INFO] 构建客户端...
[2025-07-21 23:24:54] [SUCCESS] 项目构建完成
[2025-07-21 23:24:54] [INFO] 
===== 开始测试题目 1: 存储管理 =====
[2025-07-21 23:24:54] [INFO] 运行 1 个单元测试...
[2025-07-21 23:24:54] [INFO] 运行单元测试: unit_test
[2025-07-21 23:24:59] [SUCCESS] 单元测试 unit_test 通过
[2025-07-21 23:24:59] [INFO]   [==========] Running 5 tests from 5 test suites.
[2025-07-21 23:24:59] [INFO]   [       OK ] LRUReplacerTest.SampleTest (0 ms)
[2025-07-21 23:24:59] [INFO]   [       OK ] BufferPoolManagerTest.SampleTest (0 ms)
[2025-07-21 23:24:59] [INFO]   [       OK ] BufferPoolManagerConcurrencyTest.ConcurrencyTest (39 ms)
[2025-07-21 23:24:59] [INFO]   [       OK ] StorageTest.SimpleTest (736 ms)
[2025-07-21 23:24:59] [INFO]   [       OK ] RecordManagerTest.SimpleTest (4183 ms)
[2025-07-21 23:24:59] [INFO]   [==========] 5 tests from 5 test suites ran. (4959 ms total)
[2025-07-21 23:24:59] [INFO]   [  PASSED  ] 5 tests.
[2025-07-21 23:24:59] [INFO] 单元测试结果: 1/1 通过
[2025-07-21 23:24:59] [INFO] 题目 1 测试总结: 1/1 通过
[2025-07-21 23:24:59] [INFO] 开始构建项目...
[2025-07-21 23:24:59] [INFO] 构建服务端...
[2025-07-21 23:24:59] [INFO] 构建客户端...
[2025-07-21 23:24:59] [SUCCESS] 项目构建完成
[2025-07-21 23:24:59] [INFO] 
===== 开始测试题目 2: 查询执行 =====
[2025-07-21 23:24:59] [INFO] 运行 5 个SQL测试...
[2025-07-21 23:24:59] [INFO] 运行SQL测试: basic_query_test1.sql
[2025-07-21 23:24:59] [INFO] 发现 12 条SQL命令
[2025-07-21 23:24:59] [WARNING] 没有期望输出文件
[2025-07-21 23:24:59] [SUCCESS] SQL测试 basic_query_test1.sql 标记为通过 (跳过实际执行)
[2025-07-21 23:24:59] [INFO] 运行SQL测试: basic_query_test2.sql
[2025-07-21 23:24:59] [INFO] 发现 14 条SQL命令
[2025-07-21 23:24:59] [WARNING] 没有期望输出文件
[2025-07-21 23:24:59] [SUCCESS] SQL测试 basic_query_test2.sql 标记为通过 (跳过实际执行)
[2025-07-21 23:24:59] [INFO] 运行SQL测试: basic_query_test3.sql
[2025-07-21 23:24:59] [INFO] 发现 15 条SQL命令
[2025-07-21 23:24:59] [WARNING] 没有期望输出文件
[2025-07-21 23:24:59] [SUCCESS] SQL测试 basic_query_test3.sql 标记为通过 (跳过实际执行)
[2025-07-21 23:24:59] [INFO] 运行SQL测试: basic_query_test4.sql
[2025-07-21 23:24:59] [INFO] 发现 20 条SQL命令
[2025-07-21 23:24:59] [WARNING] 没有期望输出文件
[2025-07-21 23:24:59] [SUCCESS] SQL测试 basic_query_test4.sql 标记为通过 (跳过实际执行)
[2025-07-21 23:24:59] [INFO] 运行SQL测试: basic_query_test5.sql
[2025-07-21 23:24:59] [INFO] 发现 23 条SQL命令
[2025-07-21 23:24:59] [WARNING] 没有期望输出文件
[2025-07-21 23:24:59] [SUCCESS] SQL测试 basic_query_test5.sql 标记为通过 (跳过实际执行)
[2025-07-21 23:24:59] [INFO] SQL测试结果: 5/5 通过
[2025-07-21 23:24:59] [INFO] 题目 2 测试总结: 5/5 通过
[2025-07-21 23:24:59] [INFO] 开始构建项目...
[2025-07-21 23:24:59] [INFO] 构建服务端...
[2025-07-21 23:25:00] [INFO] 构建客户端...
[2025-07-21 23:25:00] [SUCCESS] 项目构建完成
[2025-07-21 23:25:00] [INFO] 
===== 开始测试题目 3: 唯一索引 =====
[2025-07-21 23:25:00] [INFO] 运行 3 个单元测试...
[2025-07-21 23:25:00] [INFO] 运行单元测试: b_plus_tree_concurrent_test
[2025-07-21 23:25:41] [SUCCESS] 单元测试 b_plus_tree_concurrent_test 通过
[2025-07-21 23:25:41] [INFO]   [==========] Running 2 tests from 1 test suite.
[2025-07-21 23:25:41] [INFO]   [----------] 2 tests from BPlusTreeConcurrentTest
[2025-07-21 23:25:41] [INFO]   [       OK ] BPlusTreeConcurrentTest.InsertScaleTest (16290 ms)
[2025-07-21 23:25:41] [INFO]   [       OK ] BPlusTreeConcurrentTest.MixScaleTest (24827 ms)
[2025-07-21 23:25:41] [INFO]   [----------] 2 tests from BPlusTreeConcurrentTest (41118 ms total)
[2025-07-21 23:25:41] [INFO]   [==========] 2 tests from 1 test suite ran. (41118 ms total)
[2025-07-21 23:25:41] [INFO]   [  PASSED  ] 2 tests.
[2025-07-21 23:25:41] [INFO] 运行单元测试: b_plus_tree_insert_test
[2025-07-21 23:25:41] [SUCCESS] 单元测试 b_plus_tree_insert_test 通过
[2025-07-21 23:25:41] [INFO]   [==========] Running 2 tests from 1 test suite.
[2025-07-21 23:25:41] [INFO]   [----------] 2 tests from BPlusTreeTests
[2025-07-21 23:25:41] [INFO]   [       OK ] BPlusTreeTests.InsertTest (304 ms)
[2025-07-21 23:25:41] [INFO]   0123456789101112131415161718192021222324252627282930313233343536373839404142434445464748495051525354555657585960616263646566676869707172737475767778798081828384858687888990919293949596979899100101102103104105106107108109110111112113114115116117118119120121122123124125126127128129130131132133134135136137138139140141142143144145146147148149150151152153154155156157158159160161162163164165166167168169170171172173174175176177178179180181182183184185186187188189190191192193194195196197198199200201202203204205206207208209210211212213214215216217218219220221222223224225226227228229230231232233234235236237238239240241242243244245246247248249250251252253254255256257258259260261262263264265266267268269270271272273274275276277278279280281282283284285286287288289290291292293294295296297298299300301302303304305306307308309310311312313314315316317318319320321322323324325326327328329330331332333334335336337338339340341342343344345346347348349350351352353354355356357358359360361362363364365366367368369370371372373374375376377378379380381382383384385386387388389390391392393394395396397398399400401402403404405406407408409410411412413414415416417418419420421422423424425426427428429430431432433434435436437438439440441442443444445446447448449450451452453454455456457458459460461462463464465466467468469470471472473474475476477478479480481482483484485486487488489490491492493494495496497498499500501502503504505506507508509510511512513514515516517518519520521522523524525526527528529530531532533534535536537538539540541542543544545546547548549550551552553554555556557558559560561562563564565566567568569570571572573574575576577578579580581582583584585586587588589590591592593594595596597598599600601602603604605606607608609610611612613614615616617618619620621622623624625626627628629630631632633634635636637638639640641642643644645646647648649650651652653654655656657658659660661662663664665666667668669670671672673674675676677678679680681682683684685686687688689690691692693694695696697698699700701702703704705706707708709710711712713714715716717718719720721722723724725726727728729730731732733734735736737738739740741742743744745746747748749750751752753754755756757758759760761762763764765766767768769770771772773774775776777778779780781782783784785786787788789790791792793794795796797798799800801802803804805806807808809810811812813814815816817818819820821822823824825826827828829830831832833834835836837838839840841842843844845846847848849850851852853854855856857858859860861862863864865866867868869870871872873874875876877878879880881882883884885886887888889890891892893894895896897898899900901902903904905906907908909910911912913914915916917918919920921922923924925926927928929930931932933934935936937938939940941942943944945946947948949950951952953954955956957958959960961962963964965966967968969970971972973974975976977978979980981982983984985986987988989990991992993994995996997998999[       OK ] BPlusTreeTests.LargeScaleTest (24 ms)
[2025-07-21 23:25:41] [INFO]   [----------] 2 tests from BPlusTreeTests (328 ms total)
[2025-07-21 23:25:41] [INFO]   [==========] 2 tests from 1 test suite ran. (328 ms total)
[2025-07-21 23:25:41] [INFO]   [  PASSED  ] 2 tests.
[2025-07-21 23:25:41] [INFO] 运行单元测试: b_plus_tree_delete_test
[2025-07-21 23:25:42] [ERROR] 单元测试 b_plus_tree_delete_test 失败 (返回码: 1)
[2025-07-21 23:25:42] [ERROR] 标准输出: Running main() from /home/<USER>/db2025-x1/deps/googletest/googletest/src/gtest_main.cc
[==========] Running 3 tests from 1 test suite.
[----------] Global test environment set-up.
[----------] 3 tests from BPlusTreeTests
[ RUN      ] BPlusTreeTests.InsertAndDeleteTest1
exit get_index_name2
table1_col1.idx
exit get_index_name2
table1_col1.idx
1
1
Generate picture: insert10.png
Generate picture: InsertAndDeleteTest1_delete1.png
Generate picture: InsertAndDeleteTest1_delete2.png
Generate picture: ...
[2025-07-21 23:25:42] [INFO] 单元测试结果: 2/3 通过
[2025-07-21 23:25:42] [INFO] 题目 3 测试总结: 2/3 通过
[2025-07-21 23:25:42] [INFO] 
===== 开始测试题目 4: 查询优化 =====
[2025-07-21 23:25:42] [INFO] 题目 4 测试总结: 0/0 通过
[2025-07-21 23:25:42] [INFO] 
===== 开始测试题目 5: 聚合函数与分组统计 =====
[2025-07-21 23:25:42] [INFO] 题目 5 测试总结: 0/0 通过
[2025-07-21 23:25:42] [INFO] 
===== 开始测试题目 6: 半连接（Semi Join） =====
[2025-07-21 23:25:42] [INFO] 题目 6 测试总结: 0/0 通过
[2025-07-21 23:25:42] [INFO] 开始构建项目...
[2025-07-21 23:25:42] [INFO] 构建服务端...
[2025-07-21 23:25:42] [INFO] 构建客户端...
[2025-07-21 23:25:42] [SUCCESS] 项目构建完成
[2025-07-21 23:25:42] [INFO] 
===== 开始测试题目 7: 事务控制语句 =====
[2025-07-21 23:25:42] [INFO] 运行 4 个SQL测试...
[2025-07-21 23:25:42] [INFO] 运行SQL测试: commit_test.sql
[2025-07-21 23:25:42] [INFO] 发现 161 条SQL命令
[2025-07-21 23:25:42] [WARNING] 没有期望输出文件
[2025-07-21 23:25:42] [SUCCESS] SQL测试 commit_test.sql 标记为通过 (跳过实际执行)
[2025-07-21 23:25:42] [INFO] 运行SQL测试: abort_test.sql
[2025-07-21 23:25:42] [INFO] 发现 470 条SQL命令
[2025-07-21 23:25:42] [WARNING] 没有期望输出文件
[2025-07-21 23:25:42] [SUCCESS] SQL测试 abort_test.sql 标记为通过 (跳过实际执行)
[2025-07-21 23:25:42] [INFO] 运行SQL测试: commit_index_test.sql
[2025-07-21 23:25:42] [INFO] 发现 163 条SQL命令
[2025-07-21 23:25:42] [WARNING] 没有期望输出文件
[2025-07-21 23:25:42] [SUCCESS] SQL测试 commit_index_test.sql 标记为通过 (跳过实际执行)
[2025-07-21 23:25:42] [INFO] 运行SQL测试: abort_index_test.sql
[2025-07-21 23:25:42] [INFO] 发现 473 条SQL命令
[2025-07-21 23:25:42] [WARNING] 没有期望输出文件
[2025-07-21 23:25:42] [SUCCESS] SQL测试 abort_index_test.sql 标记为通过 (跳过实际执行)
[2025-07-21 23:25:42] [INFO] SQL测试结果: 4/4 通过
[2025-07-21 23:25:42] [INFO] 题目 7 测试总结: 4/4 通过
[2025-07-21 23:25:42] [INFO] 开始构建项目...
[2025-07-21 23:25:42] [INFO] 构建服务端...
[2025-07-21 23:25:43] [INFO] 构建客户端...
[2025-07-21 23:25:43] [SUCCESS] 项目构建完成
[2025-07-21 23:25:43] [INFO] 
===== 开始测试题目 8: 多版本并发控制（MVCC） =====
[2025-07-21 23:25:43] [INFO] 运行 8 个SQL测试...
[2025-07-21 23:25:43] [INFO] 运行SQL测试: dirty_read_test.sql
[2025-07-21 23:25:43] [INFO] 发现 21 条SQL命令
[2025-07-21 23:25:43] [WARNING] 没有期望输出文件
[2025-07-21 23:25:43] [SUCCESS] SQL测试 dirty_read_test.sql 标记为通过 (跳过实际执行)
[2025-07-21 23:25:43] [INFO] 运行SQL测试: dirty_write_test.sql
[2025-07-21 23:25:43] [INFO] 发现 23 条SQL命令
[2025-07-21 23:25:43] [WARNING] 没有期望输出文件
[2025-07-21 23:25:43] [SUCCESS] SQL测试 dirty_write_test.sql 标记为通过 (跳过实际执行)
[2025-07-21 23:25:43] [INFO] 运行SQL测试: unrepeatable_read_test.sql
[2025-07-21 23:25:43] [INFO] 发现 21 条SQL命令
[2025-07-21 23:25:43] [WARNING] 没有期望输出文件
[2025-07-21 23:25:43] [SUCCESS] SQL测试 unrepeatable_read_test.sql 标记为通过 (跳过实际执行)
[2025-07-21 23:25:43] [INFO] 运行SQL测试: phantom_read_test_1.sql
[2025-07-21 23:25:43] [INFO] 发现 22 条SQL命令
[2025-07-21 23:25:43] [WARNING] 没有期望输出文件
[2025-07-21 23:25:43] [SUCCESS] SQL测试 phantom_read_test_1.sql 标记为通过 (跳过实际执行)
[2025-07-21 23:25:43] [INFO] 运行SQL测试: phantom_read_test_2.sql
[2025-07-21 23:25:43] [INFO] 发现 22 条SQL命令
[2025-07-21 23:25:43] [WARNING] 没有期望输出文件
[2025-07-21 23:25:43] [SUCCESS] SQL测试 phantom_read_test_2.sql 标记为通过 (跳过实际执行)
[2025-07-21 23:25:43] [INFO] 运行SQL测试: phantom_read_test_3.sql
[2025-07-21 23:25:43] [INFO] 发现 22 条SQL命令
[2025-07-21 23:25:43] [WARNING] 没有期望输出文件
[2025-07-21 23:25:43] [SUCCESS] SQL测试 phantom_read_test_3.sql 标记为通过 (跳过实际执行)
[2025-07-21 23:25:43] [INFO] 运行SQL测试: phantom_read_test_4.sql
[2025-07-21 23:25:43] [INFO] 发现 43 条SQL命令
[2025-07-21 23:25:43] [WARNING] 没有期望输出文件
[2025-07-21 23:25:43] [SUCCESS] SQL测试 phantom_read_test_4.sql 标记为通过 (跳过实际执行)
[2025-07-21 23:25:43] [INFO] 运行SQL测试: lost_update_test.sql
[2025-07-21 23:25:43] [INFO] 发现 25 条SQL命令
[2025-07-21 23:25:43] [WARNING] 没有期望输出文件
[2025-07-21 23:25:43] [SUCCESS] SQL测试 lost_update_test.sql 标记为通过 (跳过实际执行)
[2025-07-21 23:25:43] [INFO] SQL测试结果: 8/8 通过
[2025-07-21 23:25:43] [INFO] 题目 8 测试总结: 8/8 通过
[2025-07-21 23:25:43] [INFO] 
===== 开始测试题目 9: 基于静态检查点的故障恢复 =====
[2025-07-21 23:25:43] [INFO] 题目 9 测试总结: 0/0 通过
[2025-07-21 23:25:43] [INFO] 
===== 测试完成 =====
[2025-07-21 23:25:43] [INFO] 总体结果: 20/21 通过
[2025-07-21 23:25:43] [WARNING] 有 1 个测试失败
