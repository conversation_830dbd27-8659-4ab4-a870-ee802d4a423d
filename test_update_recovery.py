#!/usr/bin/env python3
"""
专门测试UPDATE操作的恢复功能
"""

import subprocess
import time
import os

class UpdateRecoveryTester:
    def __init__(self):
        self.build_dir = "/home/<USER>/db2025-x1/build"
        self.client_dir = "/home/<USER>/db2025-x1/rmdb_client/build"
        self.server_process = None
        
    def cleanup(self):
        """清理所有进程"""
        if self.server_process:
            try:
                self.server_process.terminate()
                self.server_process.wait(timeout=5)
            except:
                try:
                    self.server_process.kill()
                except:
                    pass
        
        try:
            subprocess.run("pkill -f rmdb", shell=True, timeout=5)
            time.sleep(3)
        except:
            pass
    
    def start_server(self, db_name):
        """启动服务器"""
        print(f"启动服务器: {db_name}")
        self.cleanup()
        
        cmd = f"./bin/rmdb {db_name}"
        self.server_process = subprocess.Popen(
            cmd, shell=True, cwd=self.build_dir,
            stdout=subprocess.PIPE, stderr=subprocess.PIPE,
            text=True, bufsize=1
        )
        
        time.sleep(5)
        
        if self.server_process.poll() is not None:
            stdout, stderr = self.server_process.communicate()
            print(f"服务器启动失败: {stderr}")
            return False
        
        return True
    
    def run_client_commands(self, commands):
        """运行客户端命令"""
        cmd = "./rmdb_client"
        client_process = subprocess.Popen(
            cmd, shell=True, cwd=self.client_dir,
            stdin=subprocess.PIPE, stdout=subprocess.PIPE, 
            stderr=subprocess.PIPE, text=True
        )
        
        input_text = "\n".join(commands) + "\nexit\n"
        try:
            stdout, stderr = client_process.communicate(input=input_text, timeout=30)
            return stdout, stderr
        except subprocess.TimeoutExpired:
            client_process.kill()
            return "", "TIMEOUT"
    
    def test_simple_update(self):
        """测试简单UPDATE恢复"""
        print("\n=== 测试简单UPDATE恢复 ===")
        
        subprocess.run(f"rm -rf {self.build_dir}/test_simple_update", shell=True)
        if not self.start_server("test_simple_update"):
            return False
        
        try:
            # 创建表并插入数据
            commands = [
                "create table test_table (id int, value int);",
                "insert into test_table values (1, 100);",
                "insert into test_table values (2, 200);",
                "select * from test_table;"
            ]
            
            stdout, stderr = self.run_client_commands(commands)
            print("初始数据:")
            print(stdout)
            
            # 执行UPDATE并crash
            update_commands = [
                "begin;",
                "update test_table set value = 150 where id = 1;",
                "commit;",
                "begin;",
                "update test_table set value = 250 where id = 2;",
                "crash"
            ]
            
            stdout, stderr = self.run_client_commands(update_commands)
            print("UPDATE操作完成，服务器crash")
            
            if self.server_process:
                self.server_process.wait(timeout=10)
            
            # 重启并验证
            if not self.start_server("test_simple_update"):
                return False
            
            verify_commands = [
                "select * from test_table;",
                "select * from test_table where id = 1;",
                "select * from test_table where id = 2;"
            ]
            
            stdout, stderr = self.run_client_commands(verify_commands)
            print("恢复后数据:")
            print(stdout)
            
            # 验证结果
            if "150" in stdout and "200" in stdout and "250" not in stdout:
                print("✅ 简单UPDATE恢复测试通过")
                return True
            else:
                print("❌ 简单UPDATE恢复测试失败")
                print(f"期望: id=1的value是150, id=2的value是200")
                return False
                
        except Exception as e:
            print(f"❌ 测试异常: {e}")
            return False
        finally:
            self.cleanup()

def main():
    tester = UpdateRecoveryTester()
    try:
        success = tester.test_simple_update()
        print(f"\n测试结果: {'通过' if success else '失败'}")
    except Exception as e:
        print(f"测试异常: {e}")
    finally:
        tester.cleanup()

if __name__ == "__main__":
    main()
