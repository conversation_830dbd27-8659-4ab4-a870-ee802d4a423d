#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
基于静态检查点的故障恢复专项测试套件
专门针对题目9的测试需求
"""

import os
import sys
import time
import subprocess
import signal
import pexpect
import threading
from datetime import datetime

# 配置
SERVER_EXEC = "./build/bin/rmdb"
CLIENT_EXEC = "./rmdb_client/build/rmdb_client"

# TPC-C风格的测试数据
TPCC_SCHEMA = [
    "create table warehouse (w_id int, w_name char(10), w_street_1 char(20), w_street_2 char(20), w_city char(20), w_state char(2), w_zip char(9), w_tax float, w_ytd float);",
    "create table district (d_id int, d_w_id int, d_name char(10), d_street_1 char(20), d_street_2 char(20), d_city char(20), d_state char(2), d_zip char(9), d_tax float, d_ytd float, d_next_o_id int);",
    "create table customer (c_id int, c_d_id int, c_w_id int, c_first char(16), c_middle char(2), c_last char(16), c_street_1 char(20), c_street_2 char(20), c_city char(20), c_state char(2), c_zip char(9), c_phone char(16), c_since char(30), c_credit char(2), c_credit_lim int, c_discount float, c_balance float, c_ytd_payment float, c_payment_cnt int, c_delivery_cnt int, c_data char(50));",
    "create table history (h_c_id int, h_c_d_id int, h_c_w_id int, h_d_id int, h_w_id int, h_date char(19), h_amount float, h_data char(24));",
    "create table new_orders (no_o_id int, no_d_id int, no_w_id int);",
    "create table orders (o_id int, o_d_id int, o_w_id int, o_c_id int, o_entry_d char(19), o_carrier_id int, o_ol_cnt int, o_all_local int);",
    "create table order_line (ol_o_id int, ol_d_id int, ol_w_id int, ol_number int, ol_i_id int, ol_supply_w_id int, ol_delivery_d char(30), ol_quantity int, ol_amount float, ol_dist_info char(24));",
    "create table item (i_id int, i_im_id int, i_name char(24), i_price float, i_data char(50));",
    "create table stock (s_i_id int, s_w_id int, s_quantity int, s_dist_01 char(24), s_dist_02 char(24), s_dist_03 char(24), s_dist_04 char(24), s_dist_05 char(24), s_dist_06 char(24), s_dist_07 char(24), s_dist_08 char(24), s_dist_09 char(24), s_dist_10 char(24), s_ytd float, s_order_cnt int, s_remote_cnt int, s_data char(50));"
]

# 故障恢复测试用例
RECOVERY_TESTS = [
    {
        "name": "crash_recovery_single_thread_test",
        "description": "单线程发送事务，数据量较小，不包括建立检查点",
        "use_checkpoint": False,
        "data_size": "small",
        "threads": 1
    },
    {
        "name": "crash_recovery_multi_thread_test", 
        "description": "多线程发送事务，数据量较小，不包括建立检查点",
        "use_checkpoint": False,
        "data_size": "small",
        "threads": 4
    },
    {
        "name": "crash_recovery_index_test",
        "description": "单线程发送事务，包含建立索引，数据量较大，不包括建立检查点",
        "use_checkpoint": False,
        "data_size": "large",
        "threads": 1,
        "use_index": True
    },
    {
        "name": "crash_recovery_large_data_test",
        "description": "多线程发送事务，数据量较大，不包括建立检查点",
        "use_checkpoint": False,
        "data_size": "large",
        "threads": 4
    },
    {
        "name": "crash_recovery_without_checkpoint",
        "description": "单线程发送事务，数据量巨大，不包括建立检查点，记录恢复时间",
        "use_checkpoint": False,
        "data_size": "huge",
        "threads": 1,
        "measure_time": True
    },
    {
        "name": "crash_recovery_with_checkpoint",
        "description": "单线程发送事务，数据量巨大，包括建立检查点，记录恢复时间",
        "use_checkpoint": True,
        "data_size": "huge", 
        "threads": 1,
        "measure_time": True
    }
]

def log_message(message, level="INFO"):
    """记录日志消息"""
    timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    log_line = f"[{timestamp}] [{level}] {message}"
    print(log_line)

def start_rmdb_server(db_name):
    """启动RMDB服务器"""
    log_message(f"启动RMDB服务器: {SERVER_EXEC} {db_name}")
    
    # 确保输出目录存在
    os.makedirs(f"./build/{db_name}", exist_ok=True)
    
    try:
        server_process = subprocess.Popen(
            f"{SERVER_EXEC} {db_name}",
            shell=True,
            stdout=subprocess.DEVNULL,
            stderr=subprocess.DEVNULL,
            preexec_fn=os.setsid
        )
        time.sleep(2)
        return server_process
    except Exception as e:
        log_message(f"启动服务器失败: {e}", "ERROR")
        return None

def stop_rmdb_server(server_process):
    """停止RMDB服务器"""
    if server_process:
        try:
            os.killpg(os.getpgid(server_process.pid), signal.SIGTERM)
            server_process.wait(timeout=5)
        except subprocess.TimeoutExpired:
            os.killpg(os.getpgid(server_process.pid), signal.SIGKILL)
        except Exception:
            pass

def crash_server(server_process):
    """模拟服务器崩溃"""
    log_message("模拟服务器崩溃...")
    if server_process:
        try:
            # 发送SIGKILL信号模拟崩溃
            os.killpg(os.getpgid(server_process.pid), signal.SIGKILL)
            log_message("服务器已崩溃")
        except Exception as e:
            log_message(f"崩溃服务器时出错: {e}", "ERROR")

def generate_test_data(client, data_size, use_index=False):
    """生成测试数据"""
    log_message(f"生成{data_size}规模的测试数据...")
    
    # 创建表结构
    for schema_sql in TPCC_SCHEMA:
        client.sendline(schema_sql)
        client.expect('Rucbase> ', timeout=30)
    
    # 根据数据规模确定插入数量
    if data_size == "small":
        num_records = 10
    elif data_size == "large":
        num_records = 100
    elif data_size == "huge":
        num_records = 1000
    else:
        num_records = 10
    
    # 插入测试数据
    for i in range(1, num_records + 1):
        # 插入warehouse数据
        sql = f"insert into warehouse values ({i}, 'warehouse{i}', 'street1_{i}', 'street2_{i}', 'city{i}', 'ST', '12345', 0.1, 1000.0);"
        client.sendline(sql)
        client.expect('Rucbase> ', timeout=30)
        
        # 插入district数据
        sql = f"insert into district values ({i}, {i}, 'district{i}', 'street1_{i}', 'street2_{i}', 'city{i}', 'ST', '12345', 0.1, 1000.0, 1);"
        client.sendline(sql)
        client.expect('Rucbase> ', timeout=30)
        
        # 插入customer数据
        sql = f"insert into customer values ({i}, {i}, {i}, 'first{i}', 'MI', 'last{i}', 'street1_{i}', 'street2_{i}', 'city{i}', 'ST', '12345', '555-1234', '2023-01-01 00:00:00', 'GC', 50000, 0.1, 1000.0, 0.0, 1, 0, 'customer_data_{i}');"
        client.sendline(sql)
        client.expect('Rucbase> ', timeout=30)
    
    # 如果需要索引
    if use_index:
        log_message("创建索引...")
        index_sqls = [
            "create index warehouse(w_id);",
            "create index district(d_id, d_w_id);",
            "create index customer(c_id, c_d_id, c_w_id);"
        ]
        for sql in index_sqls:
            client.sendline(sql)
            client.expect('Rucbase> ', timeout=30)
    
    log_message(f"测试数据生成完成 ({num_records}条记录)")

def execute_transactions(client, num_transactions, use_checkpoint=False):
    """执行事务"""
    log_message(f"执行{num_transactions}个事务...")
    
    checkpoint_interval = num_transactions // 3 if use_checkpoint else 0
    
    for i in range(1, num_transactions + 1):
        # 开始事务
        client.sendline("begin;")
        client.expect('Rucbase> ', timeout=30)
        
        # 执行一些操作
        sqls = [
            f"select c_discount, c_last, c_credit, w_tax from customer, warehouse where w_id=1 and c_w_id=w_id and c_d_id=1 and c_id={i % 10 + 1};",
            f"select d_next_o_id, d_tax from district where d_id=1 and d_w_id=1;",
            f"update district set d_next_o_id={i+5} where d_id=1 and d_w_id=1;",
            f"insert into orders values ({i}, 1, 1, {i % 10 + 1}, '2023-06-03 19:25:47', 26, 5, 1);",
            f"insert into new_orders values ({i}, 1, 1);"
        ]
        
        for sql in sqls:
            client.sendline(sql)
            client.expect('Rucbase> ', timeout=30)
        
        # 提交事务
        client.sendline("commit;")
        client.expect('Rucbase> ', timeout=30)
        
        # 在指定间隔创建检查点
        if use_checkpoint and checkpoint_interval > 0 and i % checkpoint_interval == 0:
            log_message(f"创建检查点 (事务 {i})")
            client.sendline("create static_checkpoint;")
            client.expect('Rucbase> ', timeout=60)
    
    log_message(f"事务执行完成")

def measure_recovery_time(db_name):
    """测量恢复时间"""
    log_message("开始测量恢复时间...")
    
    start_time = time.time()
    
    # 启动服务器
    server_process = start_rmdb_server(db_name)
    if not server_process:
        return None
    
    # 尝试连接并执行查询
    while True:
        try:
            client = pexpect.spawn(CLIENT_EXEC, encoding='utf-8')
            client.expect('Rucbase> ', timeout=5)
            
            # 执行测试查询
            client.sendline("select * from district;")
            client.expect('Rucbase> ', timeout=10)
            
            client.close()
            break
            
        except (pexpect.TIMEOUT, pexpect.EOF):
            time.sleep(0.05)
            continue
    
    end_time = time.time()
    recovery_time = end_time - start_time
    
    stop_rmdb_server(server_process)
    
    log_message(f"恢复时间: {recovery_time:.2f} 秒")
    return recovery_time

def run_recovery_test(test_case):
    """运行单个故障恢复测试"""
    log_message(f"\n===== 运行测试: {test_case['name']} =====")
    log_message(f"描述: {test_case['description']}")
    
    db_name = test_case['name']
    
    # 清理旧数据库
    if os.path.exists(f"./build/{db_name}"):
        subprocess.run(f"rm -rf ./build/{db_name}", shell=True)
    
    # 启动服务器
    server_process = start_rmdb_server(db_name)
    if not server_process:
        return False
    
    try:
        # 连接客户端
        client = pexpect.spawn(CLIENT_EXEC, encoding='utf-8')
        client.logfile = None
        client.expect('Rucbase> ', timeout=10)
        
        # 生成测试数据
        generate_test_data(client, test_case['data_size'], test_case.get('use_index', False))
        
        # 执行事务
        num_transactions = 50 if test_case['data_size'] == 'huge' else 20
        execute_transactions(client, num_transactions, test_case.get('use_checkpoint', False))
        
        client.close()
        
        # 模拟崩溃
        crash_server(server_process)
        time.sleep(1)
        
        # 测量恢复时间
        recovery_time = None
        if test_case.get('measure_time', False):
            recovery_time = measure_recovery_time(db_name)
        else:
            # 简单恢复测试
            recovery_server = start_rmdb_server(db_name)
            if recovery_server:
                time.sleep(3)  # 等待恢复完成
                stop_rmdb_server(recovery_server)
                recovery_time = 0  # 占位符
        
        if recovery_time is not None:
            log_message(f"测试 {test_case['name']} 完成", "SUCCESS")
            return True, recovery_time
        else:
            log_message(f"测试 {test_case['name']} 失败", "ERROR")
            return False, None
            
    except Exception as e:
        log_message(f"测试执行出错: {e}", "ERROR")
        return False, None
    
    finally:
        if server_process:
            try:
                stop_rmdb_server(server_process)
            except:
                pass

def main():
    """主函数"""
    log_message("开始故障恢复专项测试...")
    
    passed_tests = 0
    total_tests = len(RECOVERY_TESTS)
    recovery_times = {}
    
    for test_case in RECOVERY_TESTS:
        result = run_recovery_test(test_case)
        if isinstance(result, tuple):
            success, recovery_time = result
            if success:
                passed_tests += 1
                if recovery_time is not None:
                    recovery_times[test_case['name']] = recovery_time
        elif result:
            passed_tests += 1
    
    # 检查检查点性能要求
    if 'crash_recovery_without_checkpoint' in recovery_times and 'crash_recovery_with_checkpoint' in recovery_times:
        t1 = recovery_times['crash_recovery_without_checkpoint']
        t2 = recovery_times['crash_recovery_with_checkpoint']
        improvement = (t1 - t2) / t1 * 100
        
        log_message(f"\n===== 检查点性能分析 =====")
        log_message(f"无检查点恢复时间: {t1:.2f} 秒")
        log_message(f"有检查点恢复时间: {t2:.2f} 秒")
        log_message(f"性能提升: {improvement:.1f}%")
        
        if t2 <= t1 * 0.7:
            log_message("检查点性能要求满足 (≤70%)", "SUCCESS")
        else:
            log_message("检查点性能要求不满足 (>70%)", "WARNING")
    
    # 总结
    log_message(f"\n===== 故障恢复测试完成 =====")
    log_message(f"测试结果: {passed_tests}/{total_tests} 通过")
    
    if passed_tests == total_tests:
        log_message("所有故障恢复测试通过!", "SUCCESS")
        return 0
    else:
        log_message(f"有 {total_tests - passed_tests} 个测试失败", "WARNING")
        return 1

if __name__ == "__main__":
    sys.exit(main())
