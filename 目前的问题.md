好的，但是现在还是有问题，我运行之后发现平台依旧报错了运行时间过长。学长为我查看代码并发现了一些错误。首先让我们看项目文档中对故障恢复功能的说明：
* 故障恢复(Failure recovery) 
1. 相关知识点 
    基于REDO/UNDO日志的恢复算法：基于REDO/UNDO日志的恢复算法；算法的优缺点分析
恢复算法ARIES：LSN;日志结构；日志缓冲区管理；ARIES算法
2.项目结构  
故障恢复模块相关代码子目录为src/recovery，主要包含日志管理器和故障恢复管理器。
日志记录使用LogRecord对象来进行存储，LogRecord分为日志头和日志数据项，日志头记录每一条日志都需要记录的固定信息，包括以下内容：

class LogRecord {
    LogType log_type_;         /* 日志对应操作的类型 */
    lsn_t lsn_;                /* 当前日志的lsn */
    uint32_t log_tot_len_;     /* 整个日志记录的⻓度 */
    txn_id_t log_tid_;         /* 创建当前日志的事务ID */
    lsn_t prev_lsn_;           /* 事务创建的前一条日志记录的lsn，用于undo */
};

根据日志记录对应操作的类型，需要记录不同的数据项信息，框架中给出了Begin操作和Insert操作对应的数据结构，以插入操作为例，对应数据结构如下：

class InsertLogRecord: public LogRecord {
    RmRecord insert_value_;     // 插入的记录
    Rid rid_;                   // 记录插入的位置
    char* table_name_;          // 插入记录的表名称
    size_t table_name_size_;    // 表名称的大小
};

参赛队伍需要实现其他操作对应的数据结构。
日志管理器主要提供添加日志和把日志刷入磁盘的功能，提供了日志缓冲区用来存放WAL日志记录：

/* 日志缓冲区，只有一个buffer，因此需要阻塞地去把日志写入缓冲区中 */
class LogBuffer {
public:
    LogBuffer() { 
        offset_ = 0; 
        memset(buffer_, 0, sizeof(buffer_));
    }
    bool is_full(int append_size) {
        if(offset_ + append_size > LOG_BUFFER_SIZE)
            return true;
        return false;
    }
    char buffer_[LOG_BUFFER_SIZE+1];
    int offset_;    // 写入log的offset
};

其他模块通过日志管理器的add_log_to_buffer()接口来把日志记录写入缓冲区，通过flush_log_to_disk()接口来把日志记录刷盘，日志管理器数据结构如下：

class LogManager {
std::atomic<lsn_t> global_lsn_{0}; // 全局lsn，递增，用于为每条记录分发lsn
std::mutex latch_; // 用于对log_buffer_的互斥访问
LogBuffer log_buffer_; // 日志缓冲区
lsn_t persist_lsn_; // 记录已经持久化到磁盘中的最后一条日志的日志号
DiskManager* disk_manager_;
};

系统中使用两个lsn来保证WAL的正确性，一个是日志管理器维护的persisten_lsn_，一个是每个数据⻚维护的page_lsn_，persistent_lsn_字段用来标识已经刷新到磁盘中的最后一条日志的日志序列号，page_lsn_记录了最近一个对该数据⻚进行写操作的操作对应的日志序列号，当存储层想要把内存中的数据⻚刷新到磁盘中时，首先要保证page_lsn_小于等于当前系统的持久化日志序列号persisten_lsn_，保证对该数据⻚进行修改的所有操作对应的日志记录已经刷新到了磁盘中。
  故障恢复管理器主要提供故障恢复功能，分为analyze()、redo()和undo()三个接口，需要自主完成。

以上就是项目文档中的说明，现在来看看学长提到的一些项目中的问题，可能需要你去修改，注意你修改的时候应该先鉴别是否正确然后去修改，同时也要保证修改的时候应该全面、正确的去修改：
一、 根据项目文档中的说明，WAL是和Page有关的，是缓冲池的Page页维护了一个lsn，这个lsn是最后一次修改该页的事务的lsn，事务管理器维护了一个已经持久化到磁盘的最大maxlsn，当发生换页的时候也就是把Page写到磁盘的时候需要检查lsn和maxlsn的大小关系，若是大于maslsn说明Page上有为刷盘的操作需要调用把日志刷到磁盘的函数同时更新maxlsn。
所以要改bufferpool的flush页的逻辑，判断页的lsn和malsn的关系来判断是否要把日志刷盘。
二、在rmdb.cpp中，这个代码块：
if (strcmp(data_recv, "crash") == 0) {
            std::cout << "Server crash" << std::endl;
            // 刷新日志到磁盘
            if (log_manager) {
                log_manager->flush_log_to_disk();
            }
            // 设置退出标志并关闭服务器socket以中断主线程
            should_exit = true;
            if (sockfd_server != -1) {
                close(sockfd_server);
                sockfd_server = -1;
            }
            close(fd);
            // 使用exit()来立即终止整个进程，模拟crash行为
            exit(0);
        }
这里的crash不用在把日志刷新到磁盘了，因为如果发生crash时若是正在执行的事务是需要undo的，但是因为上面的刷盘逻辑，该事务的修改是的数据是没有落盘的那日志也不需要了，因为undo是撤销但是实际上那些未刷盘的数据根本没必要undo。
所以把crash刷新日志的逻辑去掉把bufferpool中有可能发生数据页算盘的地方加一个判断日志是否需要刷盘的判断。
三、在transaction_manager.cpp中，这个代码块：

void TransactionManager::abort(Transaction * txn, LogManager *log_manager) {
    // Todo:
    // 1. 回滚所有写操作
    // 2. 释放所有锁
    // 3. 清空事务相关资源，eg.锁集
    // 4. 把事务日志刷入磁盘中
    // 5. 更新事务状态
    // 如果需要支持MVCC请在上述过程中添加代码
    
    /*1.回滚写操作*/
    auto write_set = txn->get_write_set();
    if (write_set) {
        // 使用反向迭代器从写集合末尾向开头遍历
        for (auto it = write_set->rbegin(); it != write_set->rend(); ++it) {
            auto& ws = *it;
            LogRecord* log;
            switch (ws->GetWriteType()) {
                case WType::INSERT_TUPLE:
                {
                    insert_abort(txn, *ws, log_manager);
                    log = new DeleteLogRecord();
                    break;
                }
                case WType::DELETE_TUPLE:
                {
                    delete_abort(txn, *ws, log_manager);
                    log = new InsertLogRecord();
                    break;
                }
                case WType::UPDATE_TUPLE:
                {
                    update_abort(txn, *ws, log_manager);
                    log = new UpdateLogRecord();
                    break;
                }
            }
            /*写日志*/
            // txn->set_prev_lsn(log_manager->add_log_to_buffer(log));
        }
        write_set->clear();
    }
把这里的log删了，因为rmfile里写过日志了。

四、在log_recovery.cpp中，这个代码块：
/**
 * @description: 撤销插入操作（删除记录）
 */
void RecoveryManager::undo_insert_operation(const InsertLogRecord& log) {
    try {
        std::string table_name(log.table_name_, log.table_name_size_);

        // 确保表文件已打开
        if (sm_manager_->fhs_.find(table_name) == sm_manager_->fhs_.end()) {
            std::cout << "Table " << table_name << " not opened, skipping undo INSERT" << std::endl;
            return;
        }

        auto& fh = sm_manager_->fhs_.at(table_name);

        // 在恢复过程中直接删除记录，不记录日志
        // 创建一个临时的Context，但不记录日志
        Context temp_context(nullptr, nullptr, nullptr);

        // 获取页面句柄
        RmPageHandle page_handle = fh->fetch_page_handle(log.rid_.page_no);

        // 边界检查
        auto file_hdr = fh->get_file_hdr();
        if (log.rid_.slot_no >= file_hdr.num_records_per_page) {
            std::cerr << "Invalid slot number in undo INSERT: " << log.rid_.slot_no << std::endl;
            buffer_pool_manager_->unpin_page(page_handle.page->get_page_id(), false);
            return;
        }

        // 检查槽位是否已经被设置
        if (!Bitmap::is_set(page_handle.bitmap, log.rid_.slot_no)) {
            std::cout << "Slot already empty in undo INSERT, skipping" << std::endl;
            buffer_pool_manager_->unpin_page(page_handle.page->get_page_id(), false);
            return;
        }

        // 清空槽位数据
        char *slot_ptr = page_handle.get_slot(log.rid_.slot_no);
        memset(slot_ptr, 0, file_hdr.record_size);
        Bitmap::reset(page_handle.bitmap, log.rid_.slot_no);

        // 更新页面头信息
        if (page_handle.page_hdr->num_records > 0) {
            page_handle.page_hdr->num_records--;
        }

        // 解除页面固定
        buffer_pool_manager_->unpin_page(page_handle.page->get_page_id(), true);

        std::cout << "Undid INSERT operation for table " << table_name
                  << " at (" << log.rid_.page_no << "," << log.rid_.slot_no << ")" << std::endl;
    } catch (const std::exception& e) {
        std::cerr << "Failed to undo INSERT operation: " << e.what() << std::endl;
    }
}
这里不要在这里手动去删除，去调RmFileHandle::delete_record，删除涉及到空闲页维护不是简单的删除。delete_record方法的context参数就传temp context。

五、同样是在log_recovery.cpp中，这个代码块：
/**
 * @description: 重做删除操作
 */
void RecoveryManager::redo_delete_operation(const DeleteLogRecord& log) {
    try {
        std::string table_name(log.table_name_, log.table_name_size_);

        // 确保表文件已打开
        if (sm_manager_->fhs_.find(table_name) == sm_manager_->fhs_.end()) {
            std::cout << "Table " << table_name << " not opened, skipping redo DELETE" << std::endl;
            return;
        }

        auto& fh = sm_manager_->fhs_.at(table_name);

        // 重新删除记录
        fh->delete_record(log.rid_, nullptr);

        std::cout << "Redid DELETE operation for table " << table_name
                  << " at (" << log.rid_.page_no << "," << log.rid_.slot_no << ")" << std::endl;
    } catch (const std::exception& e) {
        std::cerr << "Failed to redo DELETE operation: " << e.what() << std::endl;
    }
}
代码中的fh->delete_record(log.rid_, nullptr);
传的context会访问
void RmFileHandle::delete_record(const Rid& rid, Context* context) {
    // Todo:
    // 1. 获取指定记录所在的page handle
    // 2. 更新page_handle.page_hdr中的数据结构
    // 注意考虑删除一条记录后页面未满的情况，需要调用release_page_handle()

    /*1.获取该页的page handle*/
    RmPageHandle page_handle = fetch_page_handle(rid.page_no);

    /*2.先写DELETE日志 (WAL原则)*/
if (context->log_mgr_)

如果报错就改成传temp context但是三个指针传空指针。

六、redo和undo是需要新元组和旧元组的数据的，我当时写的有问题，然后你们加上去，然后写update日志的地方都同步改一下，一个redo需要新值，undo需要旧值。
    也就是需要修改包含UpdateLogRecord的地方吗（学长这里的意思我也不是很清晰，需要你去理解）？比如：
① 检查log_manager.h中：
/**
 * TODO: update操作的日志记录
*/
class UpdateLogRecord: public LogRecord
② 
/**
 * @description: 更新记录文件中记录号为rid的记录
 * @param {Rid&} rid 要更新的记录的记录号（位置）
 * @param {char*} buf 新记录的数据
 * @param {Context*} context
 */
void RmFileHandle::update_record(const Rid& rid, char* buf, Context* context) {
    // Todo:
    // 1. 获取指定记录所在的page handle
    // 2. 更新记录

    /*1.获取该页的page handle*/
    RmPageHandle page_handle = fetch_page_handle(rid.page_no);

    /*2.先写UPDATE日志 (WAL原则)*/
    if (context->log_mgr_) {
        auto old_record = get_record(rid, context);
        Rid rid_copy = rid;  // 创建非const副本
        UpdateLogRecord *update_log = new UpdateLogRecord(context->txn_->get_transaction_id(), *old_record, rid_copy, disk_manager_->get_file_name(fd_));
        update_log->prev_lsn_ = context->txn_->get_prev_lsn();
        lsn_t lsn = context->log_mgr_->add_log_to_buffer(update_log);
        context->txn_->set_prev_lsn(lsn);
    }
③ log_recovery.cpp中：
/**
 * @description: 重做更新操作
 */
void RecoveryManager::redo_update_operation(const UpdateLogRecord& log) {
    try {
        std::string table_name(log.table_name_, log.table_name_size_);
        // 确保表文件已打开
        if (sm_manager_->fhs_.find(table_name) == sm_manager_->fhs_.end()) {
            std::cout << "Table " << table_name << " not opened, skipping redo UPDATE" << std::endl;
            return;
        }

        // 重新更新记录（这里需要新的数据，但日志中只有旧数据）
        // 简化处理：跳过UPDATE的重做
        std::cout << "Skipped UPDATE redo operation for table " << table_name
                  << " at (" << log.rid_.page_no << "," << log.rid_.slot_no << ")" << std::endl;
    } catch (const std::exception& e) {
        std::cerr << "Failed to redo UPDATE operation: " << e.what() << std::endl;
    }
}

④ 在log_recovery.cpp中：
/**
 * @description: 撤销更新操作（恢复旧值）
 */
void RecoveryManager::undo_update_operation(const UpdateLogRecord& log) {
    try {
        std::string table_name(log.table_name_, log.table_name_size_);

        // 确保表文件已打开
        if (sm_manager_->fhs_.find(table_name) == sm_manager_->fhs_.end()) {
            std::cout << "Table " << table_name << " not opened, skipping undo UPDATE" << std::endl;
            return;
        }

        auto& fh = sm_manager_->fhs_.at(table_name);
